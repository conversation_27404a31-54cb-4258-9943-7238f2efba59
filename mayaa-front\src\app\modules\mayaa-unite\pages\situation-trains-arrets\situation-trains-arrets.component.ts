import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { ArretsService } from '../../services/arrets.service';
import { CalendarService } from '../../../../services/calendar.service';
import { SituationTrainsArrets } from '../../../../model/arrets.interface';

@Component({
  selector: 'app-situation-trains-arrets',
  templateUrl: './situation-trains-arrets.component.html',
  styleUrls: ['./situation-trains-arrets.component.scss']
})
export class SituationTrainsArretsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Données
  situationData: SituationTrainsArrets[] = [];
  situationInternes: SituationTrainsArrets | null = null;
  situationExternes: SituationTrainsArrets | null = null;
  analyseComplexe: SituationTrainsArrets | null = null;
  loading = false;

  constructor(
    private arretsService: ArretsService,
    private calendarService: CalendarService
  ) {}

  ngOnInit(): void {
    // Écouter les changements de calendrier et d'unité
    combineLatest([
      this.calendarService.selectedDate$,
      this.calendarService.selectedUnite$
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([date, unite]) => {
      if (date && unite) {
        this.chargerDonnees(unite, this.formatDateForApi(date));
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private chargerDonnees(unite: string, mois: string): void {
    this.loading = true;

    // Charger toutes les données de situation
    this.arretsService.getSituationTrains(unite, mois).pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.situationData = data;
      this.situationInternes = data.find(item => item.typeCause === 'INTERNES') || null;
      this.situationExternes = data.find(item => item.typeCause === 'EXTERNES') || null;
      this.loading = false;
    });

    // Charger l'analyse complexe
    this.arretsService.getAnalyseComplexe(unite, mois).pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.analyseComplexe = data;
    });
  }

  private formatDateForApi(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  // Méthodes utilitaires pour l'affichage
  getTypeBadgeClass(type: string): string {
    return type === 'INTERNES' ? 'badge-interne' : 'badge-externe';
  }

  formatNumber(value: number | undefined): string {
    return value?.toFixed(1) || '0.0';
  }

  formatCausesList(causes: string | undefined): string[] {
    return causes ? causes.split(', ') : [];
  }

  getTotalArrets(): number {
    const internesCount = this.situationInternes?.nombreArrets || 0;
    const externesCount = this.situationExternes?.nombreArrets || 0;
    return internesCount + externesCount;
  }

  getPourcentageInternes(): number {
    const total = this.getTotalArrets();
    if (total === 0) return 0;
    return ((this.situationInternes?.nombreArrets || 0) / total) * 100;
  }

  getPourcentageExternes(): number {
    const total = this.getTotalArrets();
    if (total === 0) return 0;
    return ((this.situationExternes?.nombreArrets || 0) / total) * 100;
  }
}
