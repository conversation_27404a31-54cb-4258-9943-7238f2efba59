::ng-deep :root {
  --darkbg: #251D29;
  --darkt: #FFD1F7;
  --lightbg: #fff;
  --lightt: #D43370;
  --toggleHeight: 11.2em;
  --toggleWidth: 18em;
  --toggleBtnRadius: 7em;
  --bgColor--night: #423966;
  --toggleBtn-bgColor--night: var(--bgColor--night);
  --mooncolor: #D9FBFF;
  --bgColor--day: #9ee3fb;
  --toggleBtn-bgColor--day: var(--bgColor--day);
}

::ng-deep .tdnn {
  // margin: 0 auto;
  font-size: 25%;
  position: relative;
  height: var(--toggleHeight);
  width: var(--toggleWidth);
  border-radius: var(--toggleHeight);
  transition: all 500ms ease-in-out;
  background: var(--bgColor--night);
  display: flex;
  cursor: pointer;
}

::ng-deep .day {
  background: #FFBF71;
}

::ng-deep .moon {
  position: absolute;
  display: block;
  border-radius: 50%;
  transition: all 400ms ease-in-out;

  top: 2.3em;
  left: 2em;
  transform: rotate(-75deg);
  width: calc(var(--toggleBtnRadius) * 0.9);
  height: calc(var(--toggleBtnRadius) * 0.9);
  background: var(--bgColor--night);
  box-shadow:
    1em 1.75em 0 0em var(--mooncolor) inset,
    rgba(255, 255, 255, 0.1) 0em -4.9em 0 -3.15em,
    rgba(255, 255, 255, 0.1) 2.1em 4.9em 0 -3.15em,
    rgba(255, 255, 255, 0.1) 1.4em 9.1em 0 -2.8em,
    rgba(255, 255, 255, 0.1) 4.2em 1.4em 0 -2.87em,
    rgba(255, 255, 255, 0.1) 5.6em 5.6em 0 -3.15em,
    rgba(255, 255, 255, 0.1) 4.2em 9.1em 0 -3.15em,
    rgba(255, 255, 255, 0.1) -2.8em 4.9em 0 -3.15em,
    rgba(255, 255, 255, 0.1) -0.7em 7em 0 -3.15em;
}

::ng-deep .sun {
  top: 3.15em;
  left: 10em;
  transform: rotate(0deg);
  width: 4.9em;
  height: 4.9em;
  background: #fff;
  box-shadow:
    2.1em 2.1em 0 3.5em #fff inset,
    0 -3.5em 0 -1.89em #fff,
    2.45em -2.45em 0 -2.1em #fff,
    3.5em 0 0 -1.89em #fff,
    2.45em 2.45em 0 -2.1em #fff,
    0 3.5em 0 -1.89em #fff,
    -2.45em 2.45em 0 -2.1em #fff,
    -3.5em 0 0 -1.89em #fff,
    -2.45em -2.45em 0 -2.1em #fff;
}

.p-calendar {
  width: 100px !important;
}

