import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { KeycloakAuthGuard, KeycloakService } from 'keycloak-angular';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard extends KeycloakAuthGuard {
  constructor(protected override readonly router: Router, protected readonly keycloak: KeycloakService) {
    super(router, keycloak);
  }

  public async isAccessAllowed(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const requiredPermission = route.data['permissions'];
    const tokenParsed = this.keycloak.getKeycloakInstance().tokenParsed;
    let permissions: string[]=[];
    permissions = this.keycloak.getUserRoles().filter((role: string) => role.startsWith('$'));
    if (!Array.isArray(requiredPermission) || requiredPermission.length === 0) {
      return true;
    }
    if (requiredPermission.every((perm) => permissions.includes(perm))) return true;
    else {
      return false;
    }
  }
}
