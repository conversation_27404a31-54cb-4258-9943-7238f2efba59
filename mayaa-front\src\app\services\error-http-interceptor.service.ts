import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { InterceptorService } from '../../layout/interceptor.service';

@Injectable()
//deprecated
// to remove
export class ErrorHttpInterceptor implements HttpInterceptor {

  constructor(private interceptorService: InterceptorService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>andler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.error){
          this.interceptorService.updateErrors({
            error: error.error,
            code: error.status,
            http: true,
            dialog: (error.error.silentMode === 0 && error.status === 400) || error.status === 500 ? true : undefined,
          });

        }
        return throwError(()=>error);
      })
    );
  }
}
