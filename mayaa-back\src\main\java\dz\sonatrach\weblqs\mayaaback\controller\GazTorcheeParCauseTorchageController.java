package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.GazTorcheeParCauseTorchage;
import dz.sonatrach.weblqs.mayaaback.repo.GazTorcheeParCauseTorchageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Contrôleur pour l'accès aux données de la table GAZ_TORCHEE_PAR_CAUSE_TORCHAGE.
 *
 * Endpoint principal :
 *   GET /api/gaz-torchee-par-cause-torchage/{unite}/{pmois}
 *
 * - unite : code de l'unité (String, 3 caractères)
 * - pmois : période au format ddMMyyyy (ex: 01072025)
 *
 * Réponse :
 *   200 OK : liste des objets GazTorcheeParCauseTorchage correspondant
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/gaz-torchee-par-cause-torchage")
public class GazTorcheeParCauseTorchageController {
    @Autowired
    private GazTorcheeParCauseTorchageRepository gazTorcheeParCauseTorchageRepository;

    @GetMapping("/{unite}/{pmois}")
    public ResponseEntity<List<GazTorcheeParCauseTorchage>> getByUniteAndPmois(@PathVariable String unite, @PathVariable String pmois) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<GazTorcheeParCauseTorchage> result = gazTorcheeParCauseTorchageRepository.findByUniteAndPmois(unite, date);
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }
}
