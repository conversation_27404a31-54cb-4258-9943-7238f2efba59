<div class="realisation-container">
  <!-- Header -->
  <div class="realisation-header">
    <h1 class="realisation-title">Volet Réalisation</h1>
    <div class="realisation-info" *ngIf="donneesRealisation">
      <span class="last-update">
        Dernière mise à jour: {{ donneesRealisation.derniereMiseAJour | date:'dd/MM/yyyy HH:mm' }}
      </span>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <div class="grid">
      <div class="col-12 md:col-6 lg:col-3" *ngFor="let i of [1,2,3,4]">
        <p-card>
          <p-skeleton height="2rem" class="mb-2"></p-skeleton>
          <p-skeleton height="4rem" class="mb-3"></p-skeleton>
          <p-skeleton height="1rem"></p-skeleton>
        </p-card>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <p-card>
      <div class="text-center text-red-600">
        <i class="pi pi-exclamation-triangle text-4xl mb-3"></i>
        <p class="text-lg">{{ error }}</p>
      </div>
    </p-card>
  </div>

  <!-- Realisation Content -->
  <div *ngIf="donneesRealisation && !loading" class="realisation-content">

    <!-- Statistiques générales -->
    <div class="grid mb-4">
      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon bg-green-100">
              <i class="pi pi-check-circle text-green-600"></i>
            </div>
            <div class="stats-info">
              <h3 class="stats-value">{{ donneesRealisation.statistiques.nombreUnitesActives }}</h3>
              <p class="stats-label">Unités Actives</p>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon bg-red-100">
              <i class="pi pi-times-circle text-red-600"></i>
            </div>
            <div class="stats-info">
              <h3 class="stats-value">{{ donneesRealisation.statistiques.nombreUnitesArretTotal }}</h3>
              <p class="stats-label">Arrêts Totaux</p>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon bg-orange-100">
              <i class="pi pi-wrench text-orange-600"></i>
            </div>
            <div class="stats-info">
              <h3 class="stats-value">{{ donneesRealisation.statistiques.nombreUnitesMaintenance }}</h3>
              <p class="stats-label">Maintenances</p>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon bg-blue-100">
              <i class="pi pi-chart-bar text-blue-600"></i>
            </div>
            <div class="stats-info">
              <h3 class="stats-value">{{ (donneesRealisation.totaux.tauxGlobalRealisationObjectif | number:'1.1-1') }}%</h3>
              <p class="stats-label">Taux Global Objectif</p>
            </div>
          </div>
        </p-card>
      </div>
    </div>

    <!-- Production GNL par Unité -->
    <div class="grid mb-4">
      <div class="col-12 lg:col-6">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>Production GNL Globale</h3>
              <span class="card-subtitle">Réalisation vs Objectifs</span>
            </div>
          </ng-template>

          <div class="chart-container">
            <p-chart
              *ngIf="productionGnlChartData"
              type="bar"
              [data]="productionGnlChartData"
              [options]="productionGnlChartOptions"
              width="400"
              height="300">
            </p-chart>
          </div>

          <p-divider></p-divider>

          <div class="totals-summary">
            <div class="total-item">
              <span class="total-label">Production Réelle:</span>
              <span class="total-value text-green-600">{{ (donneesRealisation.totaux.totalProductionReelle | number:'1.0-0') }}</span>
            </div>
            <div class="total-item">
              <span class="total-label">Objectif Total:</span>
              <span class="total-value text-orange-600">{{ (donneesRealisation.totaux.totalObjectif | number:'1.0-0') }}</span>
            </div>
            <div class="total-item">
              <span class="total-label">Prévision Totale:</span>
              <span class="total-value text-purple-600">{{ (donneesRealisation.totaux.totalPrevision | number:'1.0-0') }}</span>
            </div>
            <div class="total-item">
              <span class="total-label">Taux Réalisation/Objectif:</span>
              <span class="total-value" [ngClass]="getPourcentageClass(donneesRealisation.totaux.tauxGlobalRealisationObjectif)">
                {{ (donneesRealisation.totaux.tauxGlobalRealisationObjectif | number:'1.1-1') }}%
              </span>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 lg:col-6">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>Production par Unité</h3>
              <span class="card-subtitle">Détail des performances</span>
            </div>
          </ng-template>

          <p-table
            [value]="donneesRealisation.unites"
            styleClass="p-datatable-sm"
            [scrollable]="true"
            scrollHeight="350px">

            <ng-template pTemplate="header">
              <tr>
                <th>Unité</th>
                <th>Statut</th>
                <th class="text-right">Production</th>
                <th class="text-right">Objectif</th>
                <th class="text-right">Taux Réal.</th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-item>
              <tr>
                <td>
                  <div class="unite-info">
                    <strong>{{ item.uniteCode }}</strong>
                    <small class="text-gray-600 block">{{ item.unite }}</small>
                  </div>
                </td>
                <td>
                  <p-tag
                    [value]="item.statut"
                    [severity]="getTagSeverity(getPourcentageRealisation(item.productionReelle, item.objectifProduction))">
                  </p-tag>
                </td>
                <td class="text-right">
                  <strong class="text-green-600">{{ (item.productionReelle | number:'1.0-0') }}</strong>
                </td>
                <td class="text-right">
                  <span class="text-orange-600">{{ (item.objectifProduction | number:'1.0-0') }}</span>
                </td>
                <td class="text-right">
                  <div class="taux-realisation">
                    <span [ngClass]="getPourcentageClass(getPourcentageRealisation(item.productionReelle, item.objectifProduction))">
                      {{ getPourcentageRealisation(item.productionReelle, item.objectifProduction) }}%
                    </span>
                    <p-progressBar
                      [value]="getPourcentageRealisation(item.productionReelle, item.objectifProduction)"
                      [showValue]="false"
                      styleClass="mt-1"
                      [style]="{'height': '4px'}">
                    </p-progressBar>
                  </div>
                </td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="5" class="text-center text-gray-500 py-4">
                  Aucune donnée disponible
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-card>
      </div>
    </div>

    <!-- Évolution Mensuelle -->
    <div class="grid mb-4">
      <div class="col-12">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>Évolution Mensuelle de la Production</h3>
              <span class="card-subtitle">Tendance sur 6 mois</span>
            </div>
          </ng-template>

          <div class="chart-container">
            <p-chart
              *ngIf="evolutionChartData"
              type="line"
              [data]="evolutionChartData"
              [options]="evolutionChartOptions"
              width="100%"
              height="400">
            </p-chart>
          </div>
        </p-card>
      </div>
    </div>

  </div>
</div>
