import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MayaaUniteService } from './mayaa-unite.service';
import { ListUnite } from '../../../model/ListUnite';
import { environment } from '../../../environments/environment';

describe('MayaaUniteService - ListUnite', () => {
  let service: MayaaUniteService;
  let httpMock: HttpTestingController;
  const baseUrl = environment.apiUrl + '/api';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [MayaaUniteService]
    });
    service = TestBed.inject(MayaaUniteService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('getListUnites', () => {
    it('should return list of unites', () => {
      const mockUnites: ListUnite[] = [
        {
          id: 1,
          codeUnite: '5X2',
          unite: 'GL1Z',
          pmois: '2024-01-01',
          tauxAcObj: 19,
          tauxAcDesign: 14.8
        },
        {
          id: 2,
          codeUnite: '5X3',
          unite: 'GL2Z',
          pmois: '2024-01-01',
          tauxAcObj: 16,
          tauxAcDesign: 10
        }
      ];

      service.getListUnites().subscribe(unites => {
        expect(unites).toEqual(mockUnites);
        expect(unites.length).toBe(2);
        expect(unites[0].codeUnite).toBe('5X2');
        expect(unites[0].tauxAcObj).toBe(19);
        expect(unites[0].tauxAcDesign).toBe(14.8);
      });

      const req = httpMock.expectOne(`${baseUrl}/list-unites`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUnites);
    });

    it('should return empty array on error', () => {
      service.getListUnites().subscribe(unites => {
        expect(unites).toEqual([]);
      });

      const req = httpMock.expectOne(`${baseUrl}/list-unites`);
      req.error(new ErrorEvent('Network error'));
    });
  });

  describe('getUniteObjectifs', () => {
    it('should return unite objectifs for specific unite and period', () => {
      const mockUnite: ListUnite = {
        id: 1,
        codeUnite: '5X2',
        unite: 'GL1Z',
        pmois: '2024-01-01',
        tauxAcObj: 19,
        tauxAcDesign: 14.8
      };

      const unite = '5X2';
      const pmois = '01012024';

      service.getUniteObjectifs(unite, pmois).subscribe(result => {
        expect(result).toEqual(mockUnite);
        expect(result?.codeUnite).toBe('5X2');
        expect(result?.tauxAcObj).toBe(19);
        expect(result?.tauxAcDesign).toBe(14.8);
      });

      const req = httpMock.expectOne(`${baseUrl}/list-unites/${unite}/${pmois}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUnite);
    });

    it('should return null when unite not found', () => {
      const unite = '5X2';
      const pmois = '01012024';

      service.getUniteObjectifs(unite, pmois).subscribe(result => {
        expect(result).toBeNull();
      });

      const req = httpMock.expectOne(`${baseUrl}/list-unites/${unite}/${pmois}`);
      req.error(new ErrorEvent('Not found'));
    });
  });
});
