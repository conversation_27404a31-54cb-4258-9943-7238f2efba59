package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.UtilisationChaudieres;
import dz.sonatrach.weblqs.mayaaback.repo.UtilisationChaudieresRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("api/")
public class UtilisationChaudieresController {

    @Autowired
    private UtilisationChaudieresRepository utilisationChaudieresRepository;

    @GetMapping("/utilisation-chaudieres/{unite}/{mois}")
    public ResponseEntity<List<UtilisationChaudieres>> getByUniteAndMois(@PathVariable String unite, @PathVariable String mois) {
        LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<UtilisationChaudieres> result = utilisationChaudieresRepository.findByUniteAndMois(unite, date);
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }

    @GetMapping("/utilisation-chaudieres/{unite}")
    public ResponseEntity<List<UtilisationChaudieres>> getByUnite(@PathVariable String unite) {
        List<UtilisationChaudieres> result = utilisationChaudieresRepository.findByUnite(unite);
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }

    @GetMapping("/utilisation-chaudieres/mois/{mois}")
    public ResponseEntity<List<UtilisationChaudieres>> getByMois(@PathVariable String mois) {
        LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<UtilisationChaudieres> result = utilisationChaudieresRepository.findByMois(date);
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }
}
