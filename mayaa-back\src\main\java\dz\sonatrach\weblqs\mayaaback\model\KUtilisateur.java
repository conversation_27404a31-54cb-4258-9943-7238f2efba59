package dz.sonatrach.weblqs.mayaaback.model;

import java.util.List;

import org.springframework.stereotype.Component;

@Component
public class KUtilisateur {
		private String kid;
	    private String username;
	    private String nomComplet;
	    private String matricule;
	    private String fonction;
	    private String structure;
	    private List<String> groupIds;
	    private List<String> permissions;
	    private List<String> roles;
		public String getUsername() {
			return username;
		}
		public void setUsername(String username) {
			this.username = username;
		}
		public String getNomComplet() {
			return nomComplet;
		}
		public void setNomComplet(String nomComplet) {
			this.nomComplet = nomComplet;
		}
		
		public List<String> getPermissions() {
			return permissions;
		}
		public void setPermissions(List<String> permissions) {
			this.permissions = permissions;
		}
		public List<String> getRoles() {
			return roles;
		}
		public void setRoles(List<String> roles) {
			this.roles = roles;
		}
		public List<String> getGroupIds() {
			return groupIds;
		}
		public void setGroupIds(List<String> groupIds) {
			this.groupIds = groupIds;
		}
		public String getKid() {
			return kid;
		}
		public void setKid(String kid) {
			this.kid = kid;
		}
		public String getMatricule() {
			return matricule;
		}
		public void setMatricule(String matricule) {
			this.matricule = matricule;
		}
		public String getFonction() {
			return fonction;
		}
		public void setFonction(String fonction) {
			this.fonction = fonction;
		}
		public String getStructure() {
			return structure;
		}
		public void setStructure(String structure) {
			this.structure = structure;
		}
}
