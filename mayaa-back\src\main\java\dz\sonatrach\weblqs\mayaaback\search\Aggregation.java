package dz.sonatrach.weblqs.mayaaback.search;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;

@JsonView(View.basic.class)
public class Aggregation {
	
    private String aggregationType;
    private String field;
    private Map<Object, Object> results; 
    private boolean includeFilter;

    public Aggregation(String aggregationType, String field, Map<Object, Object> results) {
        this.aggregationType = aggregationType;
        this.field = field;
        this.results = results;
    }

    public Aggregation() {
		// TODO Auto-generated constructor stub
	}
    
    public String getAggregationType() {
        return aggregationType;
    }

    public void setAggregationType(String aggregationType) {
        this.aggregationType = aggregationType;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public Map<Object, Object> getResults() {
        return results;
    }

    public void setResults(Map<Object, Object> results) {
        this.results = results;
    }
    
    public boolean isIncludeFilter() {
        return includeFilter;
    }

    public void setIncludeFilter(boolean includeFilter) {
        this.includeFilter = includeFilter;
    }
}
