<div class="autoconsommation-container">
  <!-- Header -->
  <div class="autoconsommation-header">
    <h1 class="autoconsommation-title">Volet Autoconsommation</h1>
    <div class="autoconsommation-info" *ngIf="donneesAutoconsommation">
      <span class="last-update">
        Dernière mise à jour: {{ donneesAutoconsommation.derniereMiseAJour | date:'dd/MM/yyyy HH:mm' }}
      </span>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <div class="grid">
      <div class="col-12 md:col-6 lg:col-3" *ngFor="let i of [1,2,3,4]">
        <p-card>
          <p-skeleton height="2rem" class="mb-2"></p-skeleton>
          <p-skeleton height="4rem" class="mb-3"></p-skeleton>
          <p-skeleton height="1rem"></p-skeleton>
        </p-card>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <p-card>
      <div class="text-center text-red-600">
        <i class="pi pi-exclamation-triangle text-4xl mb-3"></i>
        <p class="text-lg">{{ error }}</p>
      </div>
    </p-card>
  </div>

  <!-- Autoconsommation Content -->
  <div *ngIf="donneesAutoconsommation && !loading" class="autoconsommation-content">

    <!-- Cartes de résumé autoconsommation -->
    <div class="grid mb-4">
      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card autoconsommation-card">
          <ng-template pTemplate="header">
            <div class="card-mini-header">
              <h4>Autoconsommation Nette</h4>
            </div>
          </ng-template>
          <div class="stats-content-vertical">
            <div class="main-value">
              <span class="value-number">{{ formatNumber(donneesAutoconsommation.totaux.totalAutoconsommationNette) }}</span>
              <span class="value-unit">10³ m³</span>
            </div>
            <div class="percentage-value">
              <span class="percentage text-blue-600">
                {{ formatNumber(donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette, 1) }}%
              </span>
              <span class="percentage-label">du GN reçu</span>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card autoconsommation-card">
          <ng-template pTemplate="header">
            <div class="card-mini-header">
              <h4>Gaz Torché Total</h4>
            </div>
          </ng-template>
          <div class="stats-content-vertical">
            <div class="main-value">
              <span class="value-number">{{ formatNumber(donneesAutoconsommation.totaux.totalGazTorche) }}</span>
              <span class="value-unit">10³ m³</span>
            </div>
            <div class="percentage-value">
              <span class="percentage text-red-600">
                {{ formatNumber(donneesAutoconsommation.totaux.tauxGlobalGazTorche, 1) }}%
              </span>
              <span class="percentage-label">du GN reçu</span>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card autoconsommation-card">
          <ng-template pTemplate="header">
            <div class="card-mini-header">
              <h4>Total GN Reçu</h4>
            </div>
          </ng-template>
          <div class="stats-content-vertical">
            <div class="main-value">
              <span class="value-number">{{ formatNumber(donneesAutoconsommation.totaux.totalGnRecu) }}</span>
              <span class="value-unit">10³ m³</span>
            </div>
            <div class="percentage-value">
              <span class="percentage text-gray-600">
                100%
              </span>
              <span class="percentage-label">Base de calcul</span>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 md:col-6 lg:col-3">
        <p-card class="stats-card autoconsommation-card">
          <ng-template pTemplate="header">
            <div class="card-mini-header">
              <h4>Performance Globale</h4>
            </div>
          </ng-template>
          <div class="stats-content-vertical">
            <div class="main-value">
              <span class="value-number" [ngClass]="getPourcentageClass(donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette)">
                {{ formatNumber(donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette, 1) }}%
              </span>
              <span class="value-unit">AC Nette</span>
            </div>
            <div class="percentage-value">
              <p-tag
                [value]="donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette <= 8 ? 'Excellent' : donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette <= 12 ? 'Acceptable' : 'À améliorer'"
                [severity]="getTagSeverity(donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette)">
              </p-tag>
            </div>
          </div>
        </p-card>
      </div>
    </div>

    <!-- Graphiques de répartition -->
    <div class="grid mb-4">
      <div class="col-12 lg:col-6">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>Répartition Autoconsommation par Unité</h3>
              <span class="card-subtitle">Distribution des consommations</span>
            </div>
          </ng-template>

          <div class="chart-container">
            <p-chart
              *ngIf="repartitionAutoconsChartData"
              type="pie"
              [data]="repartitionAutoconsChartData"
              [options]="repartitionAutoconsChartOptions"
              width="400"
              height="300">
            </p-chart>
          </div>

          <p-divider></p-divider>

          <div class="totals-summary">
            <div class="total-item">
              <span class="total-label">Total Autoconsommation:</span>
              <span class="total-value text-blue-600">{{ formatNumber(donneesAutoconsommation.totaux.totalAutoconsommationNette) }} (10³ m³)</span>
            </div>
            <div class="total-item">
              <span class="total-label">Taux Global:</span>
              <span class="total-value" [ngClass]="getPourcentageClass(donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette)">
                {{ formatNumber(donneesAutoconsommation.totaux.tauxGlobalAutoconsommationNette, 1) }}%
              </span>
            </div>
          </div>
        </p-card>
      </div>

      <div class="col-12 lg:col-6">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>Répartition Gaz Torché par Cause</h3>
              <span class="card-subtitle">Analyse des causes</span>
            </div>
          </ng-template>

          <div class="chart-container">
            <p-chart
              *ngIf="gazTorcheChartData"
              type="doughnut"
              [data]="gazTorcheChartData"
              [options]="gazTorcheChartOptions"
              width="400"
              height="300">
            </p-chart>
          </div>

          <p-divider></p-divider>

          <div class="totals-summary">
            <div class="total-item">
              <span class="total-label">Total Gaz Torché:</span>
              <span class="total-value text-red-600">{{ formatNumber(donneesAutoconsommation.totaux.totalGazTorche) }} (10³ m³)</span>
            </div>
            <div class="total-item">
              <span class="total-label">Taux Global:</span>
              <span class="total-value text-red-600">
                {{ formatNumber(donneesAutoconsommation.totaux.tauxGlobalGazTorche, 1) }}%
              </span>
            </div>
          </div>
        </p-card>
      </div>
    </div>

    <!-- Détail autoconsommation par unité -->
    <div class="grid mb-4">
      <div class="col-12">
        <p-card>
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>Autoconsommation par Unité</h3>
              <span class="card-subtitle">Détail des consommations et performances</span>
            </div>
          </ng-template>

          <p-table
            [value]="donneesAutoconsommation.unites"
            styleClass="p-datatable-sm"
            [scrollable]="true"
            scrollHeight="400px">

            <ng-template pTemplate="header">
              <tr>
                <th>Unité</th>
                <th>Statut</th>
                <th class="text-right">AC Nette</th>
                <th class="text-right">GN Reçu</th>
                <th class="text-right">Taux AC (%)</th>
                <th class="text-right">Performance</th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-item>
              <tr>
                <td>
                  <div class="unite-info">
                    <strong>{{ item.uniteCode }}</strong>
                    <small class="text-gray-600 block">{{ item.unite }}</small>
                  </div>
                </td>
                <td>
                  <p-tag
                    [value]="getStatusLabel(item.statut)"
                    [severity]="getStatusSeverity(item.statut)">
                  </p-tag>
                </td>
                <td class="text-right">
                  <strong class="text-blue-600">{{ formatNumber(item.autoconsommationNette) }}</strong>
                </td>
                <td class="text-right">
                  <span class="text-gray-600">{{ formatNumber(item.autoconsommationNette + item.gazTorcheTotal) }}</span>
                </td>
                <td class="text-right">
                  <span class="font-semibold" [ngClass]="getPourcentageClass(item.tauxAutoconsommationNette)">
                    {{ formatNumber(item.tauxAutoconsommationNette, 1) }}%
                  </span>
                </td>
                <td class="text-right">
                  <p-tag
                    [value]="item.tauxAutoconsommationNette <= 8 ? 'Excellent' : item.tauxAutoconsommationNette <= 12 ? 'Acceptable' : 'À améliorer'"
                    [severity]="getTagSeverity(item.tauxAutoconsommationNette)">
                  </p-tag>
                </td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="6" class="text-center text-gray-500 py-4">
                  Aucune donnée disponible
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-card>
      </div>
    </div>

  </div>
</div>
