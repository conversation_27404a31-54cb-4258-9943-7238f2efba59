package dz.sonatrach.weblqs.mayaaback.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import dz.sonatrach.weblqs.mayaaback.model.SyntheseArrets;
import dz.sonatrach.weblqs.mayaaback.repo.SyntheseArretsRepository;

/**
 * Contrôleur pour l'accès aux données de la vue SYNTHESE_ARRETS.
 *
 * Endpoints principaux :
 *   GET /api/synthese-arrets/{unite}/{mois}
 *   GET /api/synthese-arrets/{unite}/{mois}/{train}
 *   GET /api/synthese-arrets/{unite}/{mois}/trains
 *   GET /api/synthese-arrets/{unite}/{mois}/totaux
 *   GET /api/synthese-arrets/{unite}/{mois}/complexe
 *
 * - unite : code de l'unité (String, ex: "5X3")
 * - mois : période au format ddMMyyyy (ex: 01012024)
 * - train : code du train (ex: "T100", "T200", etc.)
 *
 * Réponses :
 *   200 OK : données trouvées
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/synthese-arrets")
public class SyntheseArretsController {

    @Autowired
    private SyntheseArretsRepository syntheseArretsRepository;

    /**
     * Récupère la synthèse des arrêts pour une unité et un mois donnés (tous trains)
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des données de synthèse des arrêts
     */
    @GetMapping("/{unite}/{mois}")
    public ResponseEntity<List<SyntheseArrets>> getByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<SyntheseArrets> result = syntheseArretsRepository.findByUniteAndMois(unite, date);
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la synthèse des arrêts pour une unité, un mois et un train spécifique
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @param train Code du train
     * @return Liste des données de synthèse des arrêts pour le train
     */
    @GetMapping("/{unite}/{mois}/{train}")
    public ResponseEntity<List<SyntheseArrets>> getByUniteAndMoisAndTrain(
            @PathVariable String unite, 
            @PathVariable String mois,
            @PathVariable String train) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<SyntheseArrets> result = syntheseArretsRepository.findByUniteAndMoisAndCodeTrain(unite, date, train);
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les trains disponibles pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des codes de trains avec "COMPLEXE" en premier
     */
    @GetMapping("/{unite}/{mois}/trains")
    public ResponseEntity<List<String>> getTrainsByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<String> trains = syntheseArretsRepository.findDistinctTrainsByUniteAndMois(unite, date);
            
            // Ajouter "COMPLEXE" en première position
            trains.add(0, "COMPLEXE");
            
            if (trains.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(trains);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les totaux pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Données totalisées
     */
    @GetMapping("/{unite}/{mois}/totaux")
    public ResponseEntity<List<SyntheseArrets>> getTotauxByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<SyntheseArrets> result = syntheseArretsRepository.findTotauxByUniteAndMois(unite, date);
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les données agrégées pour l'ensemble de l'unité (complexe)
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Données agrégées pour l'unité complète
     */
    @GetMapping("/{unite}/{mois}/complexe")
    public ResponseEntity<List<SyntheseArrets>> getComplexeByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<SyntheseArrets> result = syntheseArretsRepository.findAggregatedByUniteAndMois(unite, date);
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère l'évolution des données pour une période donnée
     * @param unite Code de l'unité
     * @param dateDebut Date de début au format ddMMyyyy
     * @param dateFin Date de fin au format ddMMyyyy
     * @return Liste des données sur la période
     */
    @GetMapping("/{unite}/evolution/{dateDebut}/{dateFin}")
    public ResponseEntity<List<SyntheseArrets>> getEvolutionByUniteAndPeriode(
            @PathVariable String unite,
            @PathVariable String dateDebut,
            @PathVariable String dateFin) {
        try {
            LocalDate debut = LocalDate.parse(dateDebut, DateTimeFormatter.ofPattern("ddMMyyyy"));
            LocalDate fin = LocalDate.parse(dateFin, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<SyntheseArrets> result = syntheseArretsRepository.findByUniteAndMoisBetween(unite, debut, fin);
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
