package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.service.RapportMensuelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * Contrôleur de test pour la génération du rapport mensuel PDF
 */
@RestController
@RequestMapping("api/test-rapport")
@CrossOrigin(origins = "*")
public class TestRapportController {

    @Autowired
    private RapportMensuelService rapportMensuelService;

    /**
     * Test simple de génération PDF
     */
    @GetMapping("/test")
    public ResponseEntity<byte[]> testGeneration() {
        try {
            System.out.println("=== TEST GENERATION RAPPORT ===");
            
            // Test avec le mois courant
            LocalDate moisTest = LocalDate.now();
            
            // Génération du PDF
            byte[] pdfBytes = rapportMensuelService.genererRapportMensuel(moisTest);
            
            // Headers HTTP
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "test_rapport.pdf");
            headers.setContentLength(pdfBytes.length);

            System.out.println("Test réussi: " + pdfBytes.length + " bytes générés");
            
            return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            System.err.println("Erreur lors du test: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Test de statut simple
     */
    @GetMapping("/status")
    public ResponseEntity<String> testStatus() {
        return ResponseEntity.ok("Service de rapport mensuel opérationnel");
    }
}
