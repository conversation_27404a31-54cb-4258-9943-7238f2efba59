package dz.sonatrach.weblqs.mayaaback.service;

import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;

// import dz.sonatrach.weblqs.mayaaback.dto.RapportMensuelDto;
// import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Service pour la génération du rapport mensuel PDF avec iText 9.2
 */
@Service
public class RapportMensuelService {

    // @Autowired
    // private AutoconsommationService autoconsommationService;

    // @Autowired
    // private RealisationService realisationService;

    // Couleurs Sonatrach
    private static final DeviceRgb SONATRACH_ORANGE = new DeviceRgb(255, 102, 0);
    private static final DeviceRgb SONATRACH_RED = new DeviceRgb(220, 53, 69);
    private static final DeviceRgb HEADER_GRAY = new DeviceRgb(248, 249, 250);

    /**
     * Génère le rapport mensuel complet en PDF
     */
    public byte[] genererRapportMensuel(LocalDate mois) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(baos);
        PdfDocument pdfDoc = new PdfDocument(writer);
        Document document = new Document(pdfDoc, PageSize.A4);

        // Configuration des marges
        document.setMargins(20, 20, 20, 20);

        try {
            // Formatage de la période
            String periode = mois.format(DateTimeFormatter.ofPattern("MMMM-yyyy", Locale.FRENCH));
            periode = periode.substring(0, 1).toUpperCase() + periode.substring(1);

            // TODO: Récupération des données d'autoconsommation
            // var autoConsData = autoconsommationService.getDonneesAutoconsommation(mois);

            // Génération des pages
            genererPageDeGarde(document, periode, mois);
            document.add(new AreaBreak());

            genererSommaire(document);
            document.add(new AreaBreak());

            genererIntroduction(document, periode);
            document.add(new AreaBreak());

            // Sections simplifiées pour le moment
            genererSectionsSimplifiees(document);

        } finally {
            document.close();
        }

        return baos.toByteArray();
    }

    // Méthode supprimée - construction des données simplifiée

    /**
     * Génère la page de garde
     */
    private void genererPageDeGarde(Document document, String periode, LocalDate mois) throws IOException {
        PdfFont boldFont = PdfFontFactory.createFont("Helvetica-Bold");
        PdfFont regularFont = PdfFontFactory.createFont("Helvetica");

        // En-tête avec logo (simulé)
        Table headerTable = new Table(2);
        headerTable.setWidth(UnitValue.createPercentValue(100));

        // Logo Sonatrach (rectangle coloré en attendant le vrai logo)
        Cell logoCell = new Cell();
        logoCell.add(new Paragraph("SONATRACH")
            .setFont(boldFont)
            .setFontSize(12)
            .setFontColor(ColorConstants.WHITE)
            .setBackgroundColor(SONATRACH_ORANGE)
            .setTextAlignment(TextAlignment.CENTER)
            .setPadding(10));
        headerTable.addCell(logoCell);

        // Informations organisation
        Cell infoCell = new Cell();
        String numeroRef = String.format("N°%d.%02d/GL1K TEC/%d",
            mois.getMonthValue(), mois.getMonthValue(), mois.getYear());

        infoCell.add(new Paragraph("Activité Liquéfaction & Séparation").setFont(regularFont).setFontSize(10))
               .add(new Paragraph("Division GNL & GPL").setFont(regularFont).setFontSize(10))
               .add(new Paragraph("Complexe GL1K").setFont(regularFont).setFontSize(10))
               .add(new Paragraph("Département Technique").setFont(regularFont).setFontSize(10))
               .add(new Paragraph(numeroRef).setFont(regularFont).setFontSize(9));
        headerTable.addCell(infoCell);

        document.add(headerTable);

        // Espacement
        document.add(new Paragraph("\n\n\n"));

        // Titre principal
        Paragraph titre = new Paragraph("Rapport Mensuel d'Analyse\nde l'Autoconsommation")
            .setFont(boldFont)
            .setFontSize(18)
            .setTextAlignment(TextAlignment.CENTER);

        // Période
        Paragraph periodeP = new Paragraph(periode)
            .setFont(boldFont)
            .setFontSize(16)
            .setTextAlignment(TextAlignment.CENTER);

        // Cadre pour le titre
        Table titreTable = new Table(1);
        titreTable.setWidth(UnitValue.createPercentValue(60));
        titreTable.setHorizontalAlignment(com.itextpdf.layout.properties.HorizontalAlignment.CENTER);

        Cell titreCell = new Cell();
        titreCell.add(titre)
               .add(new Paragraph("\n"))
               .add(periodeP)
               .setBorder(Border.NO_BORDER)
               .setPadding(20);

        titreTable.addCell(titreCell);
        document.add(titreTable);

        // Espacement
        document.add(new Paragraph("\n\n\n\n"));

        // Tableau de validation
        genererTableauValidation(document, periode);
    }

    /**
     * Génère le tableau de validation en bas de page
     */
    private void genererTableauValidation(Document document, String periode) throws IOException {
        PdfFont boldFont = PdfFontFactory.createFont("Helvetica-Bold");
        PdfFont regularFont = PdfFontFactory.createFont("Helvetica");

        Table validationTable = new Table(4);
        validationTable.setWidth(UnitValue.createPercentValue(100));

        // En-têtes
        String[] headers = {"REV", "Description", "Rapport préparé par l'ingénieur", "Approuvé par Chef de service Etude"};
        for (String header : headers) {
            Cell headerCell = new Cell();
            headerCell.add(new Paragraph(header).setFont(boldFont).setFontSize(9))
                     .setBackgroundColor(HEADER_GRAY)
                     .setTextAlignment(TextAlignment.CENTER)
                     .setPadding(5);
            validationTable.addCell(headerCell);
        }

        // Ligne de données
        validationTable.addCell(new Cell().add(new Paragraph("0").setFont(regularFont).setFontSize(9)).setPadding(5));
        validationTable.addCell(new Cell().add(new Paragraph("Rapport d'analyse de l'autoconsommation du mois de " +
            periode.split("-")[0]).setFont(regularFont).setFontSize(9)).setPadding(5));
        validationTable.addCell(new Cell().add(new Paragraph("D.LAAB").setFont(regularFont).setFontSize(9)).setPadding(5));
        validationTable.addCell(new Cell().add(new Paragraph("M. BOUDRKHANA").setFont(regularFont).setFontSize(9)).setPadding(5));

        document.add(validationTable);
    }

    /**
     * Génère le sommaire
     */
    private void genererSommaire(Document document) throws IOException {
        PdfFont boldFont = PdfFontFactory.createFont("Helvetica-Bold");
        PdfFont regularFont = PdfFontFactory.createFont("Helvetica");

        // Titre
        Paragraph titre = new Paragraph("SOMMAIRE")
            .setFont(boldFont)
            .setFontSize(16)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginBottom(20);
        document.add(titre);

        // Liste des sections
        String[] sections = {
            "1. Introduction",
            "2. Bilan global du complexe",
            "3. Performances du complexe",
            "4. Analyse de l'excès de l'autoconsommation (Energie interne)",
            "5. Analyse de l'excès de l'autoconsommation (Gaz torchés)",
            "6. Evolution des sièges de causes",
            "7. Evolution des classes de causes",
            "8. Suivi des actions entreprises /à entreprendre",
            "9. Actions Finalisées",
            "10. Annexes"
        };

        for (String section : sections) {
            document.add(new Paragraph(section)
                .setFont(regularFont)
                .setFontSize(12)
                .setMarginBottom(8));
        }

        document.add(new Paragraph("\n"));

        // Annexes
        document.add(new Paragraph("Annexe 1 : Bilan des Energies entrée et évolution de l'autoconsommation")
            .setFont(regularFont).setFontSize(12).setMarginBottom(8));
        document.add(new Paragraph("Annexe 2 : Tableaux des sièges de l'autoconsommation")
            .setFont(regularFont).setFontSize(12));
    }

    // Méthode genererEnTetePage supprimée - non utilisée dans la version simplifiée

    /**
     * Génère le tableau récapitulatif d'autoconsommation
     */
    private void genererTableauRecapitulatif(Document document, String periode) throws IOException {
        PdfFont boldFont = PdfFontFactory.createFont("Helvetica-Bold");
        PdfFont regularFont = PdfFontFactory.createFont("Helvetica");

        // Titre du tableau
        Paragraph titreTableau = new Paragraph("RÉCAPITULATION")
            .setFont(boldFont)
            .setFontSize(12)
            .setTextAlignment(TextAlignment.CENTER)
            .setBackgroundColor(SONATRACH_RED)
            .setFontColor(ColorConstants.WHITE)
            .setPadding(8)
            .setMarginBottom(0);
        document.add(titreTableau);

        Paragraph sousTitre = new Paragraph("AUTOCONSOMMATION " + periode.toUpperCase())
            .setFont(boldFont)
            .setFontSize(11)
            .setTextAlignment(TextAlignment.CENTER)
            .setBackgroundColor(SONATRACH_RED)
            .setFontColor(ColorConstants.WHITE)
            .setPadding(5)
            .setMarginTop(0)
            .setMarginBottom(10);
        document.add(sousTitre);

        // Tableau principal
        Table table = new Table(new float[]{4, 2, 1, 1});
        table.setWidth(UnitValue.createPercentValue(100));

        // En-têtes
        String[] headers = {"Gaz en (m³)", "Gaz en GN", "% GN", "Design / Objectif"};
        for (String header : headers) {
            Cell headerCell = new Cell();
            headerCell.add(new Paragraph(header)
                .setFont(boldFont)
                .setFontSize(9)
                .setTextAlignment(TextAlignment.CENTER))
                .setBackgroundColor(new DeviceRgb(144, 238, 144))
                .setPadding(5);
            table.addCell(headerCell);
        }

        // Données du tableau (exemple avec données mockées)
        ajouterLigneTableau(table, "Consommation nette", "64 430 523", "10,42%", "11,03%", regularFont, false);
        ajouterLigneTableau(table, "Total consommation nette causé par une source interne", "61 579 494", "9,95%", "", regularFont, true);
        ajouterLigneTableau(table, "Total consommation nette causé par une source externe", "2 851 029", "0,46%", "", regularFont, true);
        ajouterLigneTableau(table, "Total Gaz Torchés", "6 489 109", "1,05%", "2,27%", regularFont, false);
        ajouterLigneTableau(table, "Total Gaz Torchés causé par une source interne", "6 489 109", "1,05%", "", regularFont, true);
        ajouterLigneTableau(table, "Total Gaz Torchés causé par une source externe", "0", "0,00%", "", regularFont, true);
        ajouterLigneTableau(table, "Autoconsommation Complexe GL1K", "70 919 632", "11,46%", "13,3%", regularFont, false);
        ajouterLigneTableau(table, "Autoconsommation Complexe GL1K Causé par une source interne", "68 068 603", "11,00%", "", regularFont, true);
        ajouterLigneTableau(table, "Autoconsommation Complexe GL1K causé par une source externe", "2 851 029", "0,46%", "", regularFont, true);

        document.add(table);
    }

    /**
     * Ajoute une ligne au tableau récapitulatif
     */
    private void ajouterLigneTableau(Table table, String description, String gazGn, String pourcentage,
                                   String design, PdfFont font, boolean isSubItem) throws IOException {

        // Description avec indentation pour les sous-éléments
        String desc = isSubItem ? "    " + description : description;
        DeviceRgb bgColor = isSubItem ? new DeviceRgb(240, 248, 255) : new DeviceRgb(255, 255, 255);

        table.addCell(new Cell().add(new Paragraph(desc).setFont(font).setFontSize(9))
            .setBackgroundColor(bgColor).setPadding(4));
        table.addCell(new Cell().add(new Paragraph(gazGn).setFont(font).setFontSize(9))
            .setBackgroundColor(bgColor).setPadding(4).setTextAlignment(TextAlignment.RIGHT));
        table.addCell(new Cell().add(new Paragraph(pourcentage).setFont(font).setFontSize(9))
            .setBackgroundColor(bgColor).setPadding(4).setTextAlignment(TextAlignment.CENTER));
        table.addCell(new Cell().add(new Paragraph(design).setFont(font).setFontSize(9))
            .setBackgroundColor(bgColor).setPadding(4).setTextAlignment(TextAlignment.CENTER));
    }

    // Méthodes supprimées - implémentation simplifiée pour le test

    /**
     * Génère les sections simplifiées du rapport
     */
    private void genererSectionsSimplifiees(Document document) throws IOException {
        PdfFont titleFont = PdfFontFactory.createFont("Helvetica-Bold");
        PdfFont normalFont = PdfFontFactory.createFont("Helvetica");

        String[] sections = {
            "2. Bilan global du complexe",
            "3. Performances du complexe",
            "4. Analyse de l'excès de l'autoconsommation (Energie interne)",
            "5. Analyse de l'excès de l'autoconsommation (Gaz torchés)",
            "6. Evolution des sièges de causes",
            "7. Evolution des classes de causes",
            "8. Suivi des actions entreprises",
            "9. Actions Finalisées",
            "10. Annexes"
        };

        for (String section : sections) {
            Paragraph title = new Paragraph(section)
                .setFont(titleFont)
                .setFontSize(16)
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginBottom(20);
            document.add(title);

            Paragraph content = new Paragraph("Cette section sera développée avec les données réelles du système.")
                .setFont(normalFont)
                .setFontSize(12)
                .setMarginBottom(30);
            document.add(content);

            if (!section.equals("10. Annexes")) {
                document.add(new AreaBreak());
            }
        }
    }

    /**
     * Génère l'introduction avec le tableau récapitulatif
     */
    private void genererIntroduction(Document document, String periode) throws IOException {
        PdfFont titleFont = PdfFontFactory.createFont("Helvetica-Bold");
        PdfFont normalFont = PdfFontFactory.createFont("Helvetica");
        PdfFont boldFont = PdfFontFactory.createFont("Helvetica-Bold");

        // Titre
        Paragraph title = new Paragraph("1. Introduction")
            .setFont(titleFont)
            .setFontSize(16)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginBottom(20);
        document.add(title);

        // Texte d'introduction
        String texteIntro = "Le complexe a enregistré une autoconsommation de 70 919 632m³ éq GN, soit " +
            "11,46% par rapport à la charge d'équivalent GN reçue, avec une diminution de 2,12% " +
            "par rapport au design de distribution de 13,84%.\n\n" +
            "Cette autoconsommation est répartie comme suit :\n\n" +
            "• L'autoconsommation nette est évaluée à 64 430 523m³ éq GN, soit 10,42%, " +
            "dont 9,95% due aux causes internes.\n\n" +
            "• Le volume total des gaz torchés est évalué à 6 489 109m³ éq GN, soit 1,05% " +
            "aux causes internes.";

        Paragraph intro = new Paragraph(texteIntro)
            .setFont(normalFont)
            .setFontSize(11)
            .setTextAlignment(TextAlignment.JUSTIFIED)
            .setMarginBottom(20);
        document.add(intro);

        // Tableau récapitulatif
        genererTableauRecapitulatif(document, periode);

        // Événements majeurs
        document.add(new Paragraph("➤ Événements majeurs :")
            .setFont(boldFont)
            .setFontSize(12)
            .setMarginTop(20)
            .setMarginBottom(10));

        String evenementTexte = "• Démarrage du méga Train après le déclenchement enregistré le 12/11/2024 à " +
            "12h40 suite à l'apparition de l'alarme niveau très haut 16LAHH1350 du désaérateur " +
            "15MD04 : lors de stabilisation du méga train, il y eu un trip du turbo-compresseur " +
            "MR 16-MJ01-GT suite à la non ouverture des vannes DLN.";

        document.add(new Paragraph(evenementTexte)
            .setFont(normalFont)
            .setFontSize(10)
            .setMarginBottom(5));
    }
}
