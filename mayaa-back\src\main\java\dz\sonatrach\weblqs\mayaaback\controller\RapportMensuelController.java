package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.service.RapportMensuelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Contrôleur pour la génération du rapport mensuel PDF
 */
@RestController
@RequestMapping("api/rapport-mensuel")
@CrossOrigin(origins = "*")
public class RapportMensuelController {

    @Autowired
    private RapportMensuelService rapportMensuelService;

    /**
     * Génère et télécharge le rapport mensuel en PDF
     * @param periode Période au format ddMMyyyy (ex: 01112024 pour novembre 2024)
     * @return Fichier PDF du rapport mensuel
     */
    @GetMapping("/{periode}")
    public ResponseEntity<byte[]> genererRapportMensuel(@PathVariable String periode) {
        try {
            System.out.println("=== GENERATION RAPPORT MENSUEL ===");
            System.out.println("Période demandée: " + periode);

            // Conversion de la période
            LocalDate date = LocalDate.parse(periode, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // Génération du PDF
            byte[] pdfBytes = rapportMensuelService.genererRapportMensuel(date);
            
            // Nom du fichier
            String nomFichier = String.format("Rapport_Mensuel_Autoconsommation_%s.pdf", 
                date.format(DateTimeFormatter.ofPattern("MMMM_yyyy")));

            // Headers HTTP
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", nomFichier);
            headers.setContentLength(pdfBytes.length);

            System.out.println("Rapport généré avec succès: " + nomFichier + " (" + pdfBytes.length + " bytes)");
            
            return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            System.err.println("Erreur lors de la génération du rapport: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Prévisualise le rapport mensuel (retourne le PDF pour affichage)
     * @param periode Période au format ddMMyyyy
     * @return PDF pour prévisualisation
     */
    @GetMapping("/{periode}/preview")
    public ResponseEntity<byte[]> previsualiserRapportMensuel(@PathVariable String periode) {
        try {
            System.out.println("=== PREVISUALISATION RAPPORT MENSUEL ===");
            System.out.println("Période demandée: " + periode);

            LocalDate date = LocalDate.parse(periode, DateTimeFormatter.ofPattern("ddMMyyyy"));
            byte[] pdfBytes = rapportMensuelService.genererRapportMensuel(date);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("inline", "rapport_preview.pdf");

            return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            System.err.println("Erreur lors de la prévisualisation: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Vérifie la disponibilité des données pour une période donnée
     * @param periode Période au format ddMMyyyy
     * @return Statut de disponibilité des données
     */
    @GetMapping("/{periode}/status")
    public ResponseEntity<RapportStatusDto> verifierDisponibiliteDonnees(@PathVariable String periode) {
        try {
            LocalDate date = LocalDate.parse(periode, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // TODO: Implémenter la vérification de disponibilité des données
            RapportStatusDto status = new RapportStatusDto();
            status.setPeriode(periode);
            status.setDisponible(true);
            status.setMessage("Données disponibles pour la génération du rapport");
            
            return ResponseEntity.ok(status);

        } catch (Exception e) {
            RapportStatusDto status = new RapportStatusDto();
            status.setPeriode(periode);
            status.setDisponible(false);
            status.setMessage("Erreur lors de la vérification: " + e.getMessage());
            
            return ResponseEntity.ok(status);
        }
    }

    /**
     * DTO pour le statut du rapport
     */
    public static class RapportStatusDto {
        private String periode;
        private boolean disponible;
        private String message;

        // Getters et setters
        public String getPeriode() {
            return periode;
        }

        public void setPeriode(String periode) {
            this.periode = periode;
        }

        public boolean isDisponible() {
            return disponible;
        }

        public void setDisponible(boolean disponible) {
            this.disponible = disponible;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
