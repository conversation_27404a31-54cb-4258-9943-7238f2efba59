<!-- Exemple de test du composant complet -->
<div class="test-container">
  <h2>Test du Composant Indicateurs de Performance Complet</h2>
  
  <div class="test-controls">
    <div class="p-field">
      <label for="unite">Unité:</label>
      <input 
        id="unite" 
        type="text" 
        pInputText 
        [(ngModel)]="selectedUnite" 
        placeholder="Ex: 5X2"
        value="5X2">
    </div>
    
    <div class="p-field">
      <label for="pmois">Période (ddMMyyyy):</label>
      <input 
        id="pmois" 
        type="text" 
        pInputText 
        [(ngModel)]="selectedPmois" 
        placeholder="Ex: 01122024"
        value="01122024">
    </div>
    
    <button 
      pButton 
      type="button" 
      label="Tester le composant" 
      (click)="updateComponent()">
    </button>
  </div>
  
  <!-- Composant Indicateurs de Performance avec tous les tableaux -->
  <app-indicateurs-performance 
    [unite]="selectedUnite" 
    [pmois]="selectedPmois">
  </app-indicateurs-performance>
  
  <!-- Informations de test -->
  <div class="test-info">
    <h3>Donn<PERSON> attendues :</h3>
    <ul>
      <li><strong>Tableau N° 1</strong> : Indicateurs de Performance (Design vs Réelle)</li>
      <li><strong>Tableau N° 2</strong> : Bilan GN-GNL (Graphique interactif)</li>
      <li><strong>Tableau N° 3</strong> : Utilisation des chaudières (ABB et IHI)</li>
    </ul>
    
    <h3>APIs appelées :</h3>
    <ul>
      <li><code>GET /api/indicateur-performance/{{ selectedPmois }}/{{ selectedUnite }}</code></li>
      <li><code>GET /api/auto-cons-mens/01{{ selectedPmois.substring(2, 4) }}{{ selectedPmois.substring(4, 8) }}/{{ selectedUnite }}</code></li>
      <li><code>GET /api/gaz-torchee-par-cause-torchage/{{ selectedUnite }}/{{ selectedPmois }}</code></li>
      <li><code>GET /api/utilisation-chaudieres/{{ selectedUnite }}/{{ selectedPmois }}</code></li>
    </ul>
  </div>
</div>

<style>
.test-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  display: flex;
  gap: 1rem;
  align-items: end;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.p-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.p-field label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.test-info {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #0ea5e9;
}

.test-info h3 {
  color: #0c4a6e;
  margin-bottom: 1rem;
}

.test-info ul {
  margin: 0;
  padding-left: 1.5rem;
}

.test-info li {
  margin-bottom: 0.5rem;
  color: #374151;
}

.test-info code {
  background: #1e293b;
  color: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: 'Courier New', monospace;
}
</style>
