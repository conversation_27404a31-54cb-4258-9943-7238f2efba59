package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.AutoConsMens;


@Repository
public interface AutoConsMensuelRepository extends JpaRepository<AutoConsMens, Long> {
	Optional<AutoConsMens> findByPmoisAndUnite(LocalDate pMois, String pUnite);
	
	@Procedure(procedureName = "AUTOCONS.INSERT_DONNEES_UNITE_PAR_MOIS.INSERT_DONNES_MENSUEL_CONSOLID")
    void loadAutoCons(String vUnite, java.sql.Date vMois);
	
	AutoConsMens findFirstByUniteOrderByPmoisDesc(String unite);

	/**
	 * Trouve tous les indicateurs d'autoconsommation pour un mois donné.
	 * @param pmois Le mois de référence
	 * @return Liste des indicateurs d'autoconsommation
	 */
	List<AutoConsMens> findByPmois(LocalDate pmois);

	/**
	 * Trouve les indicateurs d'autoconsommation pour un mois et plusieurs unités données (optimisé).
	 * @param pmois Le mois de référence
	 * @param unites Liste des codes d'unités
	 * @return Liste des indicateurs d'autoconsommation
	 */
	List<AutoConsMens> findByPmoisAndUniteIn(LocalDate pmois, List<String> unites);

}