{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-skeleton.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * Skeleton is a placeholder to display instead of the actual content.\n * @group Components\n */\nclass Skeleton {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Shape of the element.\n   * @group Props\n   */\n  shape = 'rectangle';\n  /**\n   * Type of the animation.\n   * @gruop Props\n   */\n  animation = 'wave';\n  /**\n   * Border radius of the element, defaults to value from theme.\n   * @group Props\n   */\n  borderRadius;\n  /**\n   * Size of the skeleton.\n   * @group Props\n   */\n  size;\n  /**\n   * Width of the element.\n   * @group Props\n   */\n  width = '100%';\n  /**\n   * Height of the element.\n   * @group Props\n   */\n  height = '1rem';\n  containerClass() {\n    return {\n      'p-skeleton p-component': true,\n      'p-skeleton-circle': this.shape === 'circle',\n      'p-skeleton-none': this.animation === 'none'\n    };\n  }\n  get containerStyle() {\n    if (this.size) return {\n      ...this.style,\n      width: this.size,\n      height: this.size,\n      borderRadius: this.borderRadius\n    };else return {\n      width: this.width,\n      height: this.height,\n      borderRadius: this.borderRadius,\n      ...this.style\n    };\n  }\n  static ɵfac = function Skeleton_Factory(t) {\n    return new (t || Skeleton)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Skeleton,\n    selectors: [[\"p-skeleton\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      shape: \"shape\",\n      animation: \"animation\",\n      borderRadius: \"borderRadius\",\n      size: \"size\",\n      width: \"width\",\n      height: \"height\"\n    },\n    decls: 1,\n    vars: 7,\n    consts: [[3, \"ngClass\", \"ngStyle\"]],\n    template: function Skeleton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.containerStyle);\n        i0.ɵɵattribute(\"data-pc-name\", \"skeleton\")(\"aria-hidden\", true)(\"data-pc-section\", \"root\");\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    styles: [\"@layer primeng{.p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Skeleton, [{\n    type: Component,\n    args: [{\n      selector: 'p-skeleton',\n      template: ` <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle\" [attr.data-pc-name]=\"'skeleton'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'root'\"></div> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    animation: [{\n      type: Input\n    }],\n    borderRadius: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }]\n  });\n})();\nclass SkeletonModule {\n  static ɵfac = function SkeletonModule_Factory(t) {\n    return new (t || SkeletonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SkeletonModule,\n    declarations: [Skeleton],\n    imports: [CommonModule],\n    exports: [Skeleton]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SkeletonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Skeleton],\n      declarations: [Skeleton]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Skeleton, SkeletonModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,SAAS;AAAA,EACT,iBAAiB;AACf,WAAO;AAAA,MACL,0BAA0B;AAAA,MAC1B,qBAAqB,KAAK,UAAU;AAAA,MACpC,mBAAmB,KAAK,cAAc;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,KAAK;AAAM,aAAO,iCACjB,KAAK,QADY;AAAA,QAEpB,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,cAAc,KAAK;AAAA,MACrB;AAAA;AAAO,aAAO;AAAA,QACZ,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,cAAc,KAAK;AAAA,SAChB,KAAK;AAAA,EAEZ;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,GAAG;AACzC,WAAO,KAAK,KAAK,WAAU;AAAA,EAC7B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,WAAW,SAAS,CAAC;AAAA,IAClC,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,eAAe,CAAC,EAAE,WAAW,IAAI,cAAc;AAC5E,QAAG,YAAY,gBAAgB,UAAU,EAAE,eAAe,IAAI,EAAE,mBAAmB,MAAM;AAAA,MAC3F;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,OAAO;AAAA,IACrC,QAAQ,CAAC,sZAAwZ;AAAA,IACja,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,sZAAwZ;AAAA,IACna,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,GAAG;AAC/C,WAAO,KAAK,KAAK,iBAAgB;AAAA,EACnC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,QAAQ;AAAA,IACvB,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,QAAQ;AAAA,EACpB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,QAAQ;AAAA,MAClB,cAAc,CAAC,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}