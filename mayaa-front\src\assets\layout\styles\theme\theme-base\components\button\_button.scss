.p-button {
    color: $buttonTextColor;
    background: $buttonBg;
    border: $buttonBorder;
    padding: $buttonPadding;
    font-size: $fontSize;
    transition: $formElementTransition;
    border-radius: $borderRadius;

    &:not(:disabled):hover {
        background: $buttonHoverBg;
        color: $buttonTextHoverColor;
        border-color: $buttonHoverBorderColor;
    }

    &:not(:disabled):active {
        background: $buttonActiveBg;
        color: $buttonTextActiveColor;
        border-color: $buttonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $buttonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($buttonBg, $textButtonHoverBgOpacity);
            color: $buttonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($buttonBg, $textButtonActiveBgOpacity);
            color: $buttonBg;
            border: $outlinedButtonBorder;
        }

        &.p-button-plain {
            color: $plainButtonTextColor;
            border-color: $plainButtonTextColor;

            &:not(:disabled):hover {
                background: $plainButtonHoverBgColor;
                color: $plainButtonTextColor;
            }

            &:not(:disabled):active {
                background: $plainButtonActiveBgColor;
                color: $plainButtonTextColor;
            }
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $buttonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($buttonBg, $textButtonHoverBgOpacity);
            color: $buttonBg;
            border-color: transparent;
        }

        &:not(:disabled):active {
            background: rgba($buttonBg, $textButtonActiveBgOpacity);
            color: $buttonBg;
            border-color: transparent;
        }

        &.p-button-plain {
            color: $plainButtonTextColor;

            &:not(:disabled):hover {
                background: $plainButtonHoverBgColor;
                color: $plainButtonTextColor;
            }

            &:not(:disabled):active {
                background: $plainButtonActiveBgColor;
                color: $plainButtonTextColor;
            }
        }
    }

    &:focus {
        @include focused();
    }

    .p-button-label {
        transition-duration: $transitionDuration;
    }

    .p-button-icon-left {
        margin-right: $inlineSpacing;
    }

    .p-button-icon-right {
        margin-left: $inlineSpacing;
    }

    .p-button-icon-bottom {
        margin-top: $inlineSpacing;
    }

    .p-button-icon-top {
        margin-bottom: $inlineSpacing;
    }

    .p-badge {
        margin-left: $inlineSpacing;
        min-width: $fontSize;
        height: $fontSize;
        line-height: $fontSize;
        color: $buttonBg;
        background-color: $buttonTextColor;
    }

    &.p-button-raised {
        box-shadow: $raisedButtonShadow;
    }

    &.p-button-rounded {
        border-radius: $roundedButtonBorderRadius;
    }

    &.p-button-icon-only {
        width: $buttonIconOnlyWidth;
        padding: $buttonIconOnlyPadding;

        .p-button-icon-left,
        .p-button-icon-right {
            margin: 0;
        }

        &.p-button-rounded {
            border-radius: 50%;
            height: $buttonIconOnlyWidth;
        }
    }

    &.p-button-sm {
        @include scaledFontSize($fontSize, $scaleSM);
        @include scaledPadding($buttonPadding, $scaleSM);

        .p-button-icon {
            @include scaledFontSize($primeIconFontSize, $scaleSM);
        }
    }

    &.p-button-lg {
        @include scaledFontSize($fontSize, $scaleLG);
        @include scaledPadding($buttonPadding, $scaleLG);

        .p-button-icon {
            @include scaledFontSize($primeIconFontSize, $scaleLG);
        }
    }

    &.p-button-loading-label-only {
        .p-button-label {
            margin-left: $inlineSpacing;
        }

        .p-button-loading-icon {
            margin-right: 0;
        }
    }
}

.p-fluid {
    .p-button {
        width: 100%;
    }

    .p-button-icon-only {
        width: $buttonIconOnlyWidth;
    }

    .p-buttonset {
        display: flex;

        .p-button {
            flex: 1;
        }
    }
}

.p-button.p-button-secondary,
.p-buttonset.p-button-secondary > .p-button,
.p-splitbutton.p-button-secondary > .p-button {
    color: $secondaryButtonTextColor;
    background: $secondaryButtonBg;
    border: $secondaryButtonBorder;

    &:not(:disabled):hover {
        background: $secondaryButtonHoverBg;
        color: $secondaryButtonTextHoverColor;
        border-color: $secondaryButtonHoverBorderColor;
    }

    &:not(:disabled):focus {
        box-shadow: $secondaryButtonFocusShadow;
    }

    &:not(:disabled):active {
        background: $secondaryButtonActiveBg;
        color: $secondaryButtonTextActiveColor;
        border-color: $secondaryButtonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $secondaryButtonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($secondaryButtonBg, $textButtonHoverBgOpacity);
            color: $secondaryButtonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($secondaryButtonBg, $textButtonActiveBgOpacity);
            color: $secondaryButtonBg;
            border: $outlinedButtonBorder;
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $secondaryButtonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($secondaryButtonBg, $textButtonHoverBgOpacity);
            border-color: transparent;
            color: $secondaryButtonBg;
        }

        &:not(:disabled):active {
            background: rgba($secondaryButtonBg, $textButtonActiveBgOpacity);
            border-color: transparent;
            color: $secondaryButtonBg;
        }
    }
}

.p-button.p-button-info,
.p-buttonset.p-button-info > .p-button,
.p-splitbutton.p-button-info > .p-button {
    color: $infoButtonTextColor;
    background: $infoButtonBg;
    border: $infoButtonBorder;

    &:not(:disabled):hover {
        background: $infoButtonHoverBg;
        color: $infoButtonTextHoverColor;
        border-color: $infoButtonHoverBorderColor;
    }

    &:not(:disabled):focus {
        box-shadow: $infoButtonFocusShadow;
    }

    &:not(:disabled):active {
        background: $infoButtonActiveBg;
        color: $infoButtonTextActiveColor;
        border-color: $infoButtonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $infoButtonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($infoButtonBg, $textButtonHoverBgOpacity);
            color: $infoButtonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($infoButtonBg, $textButtonActiveBgOpacity);
            color: $infoButtonBg;
            border: $outlinedButtonBorder;
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $infoButtonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($infoButtonBg, $textButtonHoverBgOpacity);
            border-color: transparent;
            color: $infoButtonBg;
        }

        &:not(:disabled):active {
            background: rgba($infoButtonBg, $textButtonActiveBgOpacity);
            border-color: transparent;
            color: $infoButtonBg;
        }
    }
}

.p-button.p-button-success,
.p-buttonset.p-button-success > .p-button,
.p-splitbutton.p-button-success > .p-button {
    color: $successButtonTextColor;
    background: $successButtonBg;
    border: $successButtonBorder;

    &:not(:disabled):hover {
        background: $successButtonHoverBg;
        color: $successButtonTextHoverColor;
        border-color: $successButtonHoverBorderColor;
    }

    &:not(:disabled):focus {
        box-shadow: $successButtonFocusShadow;
    }

    &:not(:disabled):active {
        background: $successButtonActiveBg;
        color: $successButtonTextActiveColor;
        border-color: $successButtonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $successButtonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($successButtonBg, $textButtonHoverBgOpacity);
            color: $successButtonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($successButtonBg, $textButtonActiveBgOpacity);
            color: $successButtonBg;
            border: $outlinedButtonBorder;
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $successButtonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($successButtonBg, $textButtonHoverBgOpacity);
            border-color: transparent;
            color: $successButtonBg;
        }

        &:not(:disabled):active {
            background: rgba($successButtonBg, $textButtonActiveBgOpacity);
            border-color: transparent;
            color: $successButtonBg;
        }
    }
}

.p-button.p-button-warning,
.p-buttonset.p-button-warning > .p-button,
.p-splitbutton.p-button-warning > .p-button {
    color: $warningButtonTextColor;
    background: $warningButtonBg;
    border: $warningButtonBorder;

    &:not(:disabled):hover {
        background: $warningButtonHoverBg;
        color: $warningButtonTextHoverColor;
        border-color: $warningButtonHoverBorderColor;
    }

    &:not(:disabled):focus {
        box-shadow: $warningButtonFocusShadow;
    }

    &:not(:disabled):active {
        background: $warningButtonActiveBg;
        color: $warningButtonTextActiveColor;
        border-color: $warningButtonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $warningButtonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($warningButtonBg, $textButtonHoverBgOpacity);
            color: $warningButtonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($warningButtonBg, $textButtonActiveBgOpacity);
            color: $warningButtonBg;
            border: $outlinedButtonBorder;
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $warningButtonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($warningButtonBg, $textButtonHoverBgOpacity);
            border-color: transparent;
            color: $warningButtonBg;
        }

        &:not(:disabled):active {
            background: rgba($warningButtonBg, $textButtonActiveBgOpacity);
            border-color: transparent;
            color: $warningButtonBg;
        }
    }
}

.p-button.p-button-help,
.p-buttonset.p-button-help > .p-button,
.p-splitbutton.p-button-help > .p-button {
    color: $helpButtonTextColor;
    background: $helpButtonBg;
    border: $helpButtonBorder;

    &:not(:disabled):hover {
        background: $helpButtonHoverBg;
        color: $helpButtonTextHoverColor;
        border-color: $helpButtonHoverBorderColor;
    }

    &:not(:disabled):focus {
        box-shadow: $helpButtonFocusShadow;
    }

    &:not(:disabled):active {
        background: $helpButtonActiveBg;
        color: $helpButtonTextActiveColor;
        border-color: $helpButtonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $helpButtonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($helpButtonBg, $textButtonHoverBgOpacity);
            color: $helpButtonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($helpButtonBg, $textButtonActiveBgOpacity);
            color: $helpButtonBg;
            border: $outlinedButtonBorder;
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $helpButtonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($helpButtonBg, $textButtonHoverBgOpacity);
            border-color: transparent;
            color: $helpButtonBg;
        }

        &:not(:disabled):active {
            background: rgba($helpButtonBg, $textButtonActiveBgOpacity);
            border-color: transparent;
            color: $helpButtonBg;
        }
    }
}

.p-button.p-button-danger,
.p-buttonset.p-button-danger > .p-button,
.p-splitbutton.p-button-danger > .p-button {
    color: $dangerButtonTextColor;
    background: $dangerButtonBg;
    border: $dangerButtonBorder;

    &:not(:disabled):hover {
        background: $dangerButtonHoverBg;
        color: $dangerButtonTextHoverColor;
        border-color: $dangerButtonHoverBorderColor;
    }

    &:not(:disabled):focus {
        box-shadow: $dangerButtonFocusShadow;
    }

    &:not(:disabled):active {
        background: $dangerButtonActiveBg;
        color: $dangerButtonTextActiveColor;
        border-color: $dangerButtonActiveBorderColor;
    }

    &.p-button-outlined {
        background-color: transparent;
        color: $dangerButtonBg;
        border: $outlinedButtonBorder;

        &:not(:disabled):hover {
            background: rgba($dangerButtonBg, $textButtonHoverBgOpacity);
            color: $dangerButtonBg;
            border: $outlinedButtonBorder;
        }

        &:not(:disabled):active {
            background: rgba($dangerButtonBg, $textButtonActiveBgOpacity);
            color: $dangerButtonBg;
            border: $outlinedButtonBorder;
        }
    }

    &.p-button-text {
        background-color: transparent;
        color: $dangerButtonBg;
        border-color: transparent;

        &:not(:disabled):hover {
            background: rgba($dangerButtonBg, $textButtonHoverBgOpacity);
            border-color: transparent;
            color: $dangerButtonBg;
        }

        &:not(:disabled):active {
            background: rgba($dangerButtonBg, $textButtonActiveBgOpacity);
            border-color: transparent;
            color: $dangerButtonBg;
        }
    }
}

.p-button.p-button-link {
    color: $linkButtonColor;
    background: transparent;
    border: transparent;

    &:not(:disabled):hover {
        background: transparent;
        color: $linkButtonHoverColor;
        border-color: transparent;

        .p-button-label {
            text-decoration: $linkButtonTextHoverDecoration;
        }
    }

    &:not(:disabled):focus {
        background: transparent;
        box-shadow: $linkButtonFocusShadow;
        border-color: transparent;
    }

    &:not(:disabled):active {
        background: transparent;
        color: $linkButtonColor;
        border-color: transparent;
    }
}
