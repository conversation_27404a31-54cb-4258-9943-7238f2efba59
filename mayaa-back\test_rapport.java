import dz.sonatrach.weblqs.mayaaback.service.RapportMensuelService;
import java.time.LocalDate;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Test simple pour générer un rapport PDF
 */
public class test_rapport {
    public static void main(String[] args) {
        System.out.println("=== TEST GENERATION RAPPORT PDF ===");
        
        try {
            // Créer le service
            RapportMensuelService service = new RapportMensuelService();
            
            // Générer le rapport pour novembre 2024
            LocalDate mois = LocalDate.of(2024, 11, 1);
            System.out.println("Génération du rapport pour: " + mois);
            
            byte[] pdfBytes = service.genererRapportMensuel(mois);
            
            // Sauvegarder le PDF
            String nomFichier = "rapport_test_novembre_2024.pdf";
            try (FileOutputStream fos = new FileOutputStream(nomFichier)) {
                fos.write(pdfBytes);
            }
            
            System.out.println("✅ SUCCÈS !");
            System.out.println("📄 PDF généré: " + nomFichier);
            System.out.println("📊 Taille: " + pdfBytes.length + " bytes");
            System.out.println("🎯 Le fichier a été créé dans le répertoire courant");
            
        } catch (Exception e) {
            System.err.println("❌ ERREUR: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
