package dz.sonatrach.weblqs.mayaaback.dto;

import dz.sonatrach.weblqs.mayaaback.model.AutoConsMens;
import dz.sonatrach.weblqs.mayaaback.model.ListUnite;
import dz.sonatrach.weblqs.mayaaback.model.GazTorcheeParCauseTorchage;
import dz.sonatrach.weblqs.mayaaback.model.IndicateurPeformanceUnite;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO simple pour la réponse consolidée du dashboard
 */
public class DashboardConsolideResponse {

    @JsonView(View.basic.class)
    private String periode;

    @JsonView(View.basic.class)
    private LocalDateTime derniereMiseAJour;

    @JsonView(View.basic.class)
    private List<ListUnite> unites;

    @JsonView(View.basic.class)
    private List<AutoConsMens> autoconsommation;

    @JsonView(View.basic.class)
    private List<GazTorcheeParCauseTorchage> gazTorche;

    @JsonView(View.basic.class)
    private List<IndicateurPeformanceUnite> indicateursPerformance;

    @JsonView(View.basic.class)
    private List<EvolutionMensuelleDto> evolutionMensuelle;

    @JsonView(View.basic.class)
    private TotauxDto totaux;

    @JsonView(View.basic.class)
    private StatistiquesDto statistiques;

    // Constructeurs
    public DashboardConsolideResponse() {
        this.derniereMiseAJour = LocalDateTime.now();
    }

    public DashboardConsolideResponse(String periode) {
        this.periode = periode;
        this.derniereMiseAJour = LocalDateTime.now();
    }

    // Méthode utilitaire
    public boolean isEmpty() {
        return (unites == null || unites.isEmpty()) &&
               (autoconsommation == null || autoconsommation.isEmpty()) &&
               (indicateursPerformance == null || indicateursPerformance.isEmpty());
    }

    // Getters et Setters
    public String getPeriode() { return periode; }
    public void setPeriode(String periode) { this.periode = periode; }

    public LocalDateTime getDerniereMiseAJour() { return derniereMiseAJour; }
    public void setDerniereMiseAJour(LocalDateTime derniereMiseAJour) { this.derniereMiseAJour = derniereMiseAJour; }

    public List<ListUnite> getUnites() { return unites; }
    public void setUnites(List<ListUnite> unites) { this.unites = unites; }

    public List<AutoConsMens> getAutoconsommation() { return autoconsommation; }
    public void setAutoconsommation(List<AutoConsMens> autoconsommation) { this.autoconsommation = autoconsommation; }

    public List<GazTorcheeParCauseTorchage> getGazTorche() { return gazTorche; }
    public void setGazTorche(List<GazTorcheeParCauseTorchage> gazTorche) { this.gazTorche = gazTorche; }

    public List<IndicateurPeformanceUnite> getIndicateursPerformance() { return indicateursPerformance; }
    public void setIndicateursPerformance(List<IndicateurPeformanceUnite> indicateursPerformance) { this.indicateursPerformance = indicateursPerformance; }

    public List<EvolutionMensuelleDto> getEvolutionMensuelle() { return evolutionMensuelle; }
    public void setEvolutionMensuelle(List<EvolutionMensuelleDto> evolutionMensuelle) { this.evolutionMensuelle = evolutionMensuelle; }

    public TotauxDto getTotaux() { return totaux; }
    public void setTotaux(TotauxDto totaux) { this.totaux = totaux; }

    public StatistiquesDto getStatistiques() { return statistiques; }
    public void setStatistiques(StatistiquesDto statistiques) { this.statistiques = statistiques; }

    // Classes internes simples
    public static class EvolutionMensuelleDto {
        @JsonView(View.basic.class)
        private String mois;
        @JsonView(View.basic.class)
        private Double autoconsommationNette;
        @JsonView(View.basic.class)
        private Double gazTorche;
        @JsonView(View.basic.class)
        private Double tauxAutoconsommationNette;
        @JsonView(View.basic.class)
        private Double tauxGazTorche;

        public EvolutionMensuelleDto() {}
        public EvolutionMensuelleDto(String mois, Double autoconsommationNette, Double gazTorche, 
                                   Double tauxAutoconsommationNette, Double tauxGazTorche) {
            this.mois = mois;
            this.autoconsommationNette = autoconsommationNette;
            this.gazTorche = gazTorche;
            this.tauxAutoconsommationNette = tauxAutoconsommationNette;
            this.tauxGazTorche = tauxGazTorche;
        }

        // Getters et setters
        public String getMois() { return mois; }
        public void setMois(String mois) { this.mois = mois; }
        public Double getAutoconsommationNette() { return autoconsommationNette; }
        public void setAutoconsommationNette(Double autoconsommationNette) { this.autoconsommationNette = autoconsommationNette; }
        public Double getGazTorche() { return gazTorche; }
        public void setGazTorche(Double gazTorche) { this.gazTorche = gazTorche; }
        public Double getTauxAutoconsommationNette() { return tauxAutoconsommationNette; }
        public void setTauxAutoconsommationNette(Double tauxAutoconsommationNette) { this.tauxAutoconsommationNette = tauxAutoconsommationNette; }
        public Double getTauxGazTorche() { return tauxGazTorche; }
        public void setTauxGazTorche(Double tauxGazTorche) { this.tauxGazTorche = tauxGazTorche; }
    }

    public static class TotauxDto {
        @JsonView(View.basic.class)
        private Double totalProductionReelle;
        @JsonView(View.basic.class)
        private Double totalObjectif;
        @JsonView(View.basic.class)
        private Double totalAutoconsommationNette;
        @JsonView(View.basic.class)
        private Double totalGazTorche;
        @JsonView(View.basic.class)
        private Double totalGnRecu;
        @JsonView(View.basic.class)
        private Double tauxGlobalRealisationObjectif;

        public TotauxDto() {}

        // Getters et setters
        public Double getTotalProductionReelle() { return totalProductionReelle; }
        public void setTotalProductionReelle(Double totalProductionReelle) { this.totalProductionReelle = totalProductionReelle; }
        public Double getTotalObjectif() { return totalObjectif; }
        public void setTotalObjectif(Double totalObjectif) { this.totalObjectif = totalObjectif; }
        public Double getTotalAutoconsommationNette() { return totalAutoconsommationNette; }
        public void setTotalAutoconsommationNette(Double totalAutoconsommationNette) { this.totalAutoconsommationNette = totalAutoconsommationNette; }
        public Double getTotalGazTorche() { return totalGazTorche; }
        public void setTotalGazTorche(Double totalGazTorche) { this.totalGazTorche = totalGazTorche; }
        public Double getTotalGnRecu() { return totalGnRecu; }
        public void setTotalGnRecu(Double totalGnRecu) { this.totalGnRecu = totalGnRecu; }
        public Double getTauxGlobalRealisationObjectif() { return tauxGlobalRealisationObjectif; }
        public void setTauxGlobalRealisationObjectif(Double tauxGlobalRealisationObjectif) { this.tauxGlobalRealisationObjectif = tauxGlobalRealisationObjectif; }
    }

    public static class StatistiquesDto {
        @JsonView(View.basic.class)
        private Integer nombreUnitesActives;
        @JsonView(View.basic.class)
        private Integer nombreUnitesArretTotal;
        @JsonView(View.basic.class)
        private Integer nombreUnitesMaintenance;

        public StatistiquesDto() {}
        public StatistiquesDto(Integer nombreUnitesActives, Integer nombreUnitesArretTotal, Integer nombreUnitesMaintenance) {
            this.nombreUnitesActives = nombreUnitesActives;
            this.nombreUnitesArretTotal = nombreUnitesArretTotal;
            this.nombreUnitesMaintenance = nombreUnitesMaintenance;
        }

        // Getters et setters
        public Integer getNombreUnitesActives() { return nombreUnitesActives; }
        public void setNombreUnitesActives(Integer nombreUnitesActives) { this.nombreUnitesActives = nombreUnitesActives; }
        public Integer getNombreUnitesArretTotal() { return nombreUnitesArretTotal; }
        public void setNombreUnitesArretTotal(Integer nombreUnitesArretTotal) { this.nombreUnitesArretTotal = nombreUnitesArretTotal; }
        public Integer getNombreUnitesMaintenance() { return nombreUnitesMaintenance; }
        public void setNombreUnitesMaintenance(Integer nombreUnitesMaintenance) { this.nombreUnitesMaintenance = nombreUnitesMaintenance; }
    }
}
