package dz.sonatrach.weblqs.mayaaback.search;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class AggregableRepositoryImpl<T> implements AggregableRepository<T> {
	
	private final EntityManager entityManager;
	private final SearchBuilder searchBuilder;

    public AggregableRepositoryImpl(EntityManager entityManager,SearchBuilder searchBuilder) {
        this.entityManager = entityManager;
        this.searchBuilder = searchBuilder;
    }

    @Override
    public List<Aggregation> getAggregations(
            Class<T> entityClass,
            List<Aggregation> aggregationRequests,
            PrimengQueries queries) {

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        List<Aggregation> aggregationResults = new ArrayList<>();

        for (Aggregation request : aggregationRequests) {
            String field = request.getField();
            String aggType = request.getAggregationType();
            boolean includeFilter = request.isIncludeFilter();

            // Base query for each aggregation
            CriteriaQuery<Object[]> aggQuery = criteriaBuilder.createQuery(Object[].class);
            CriteriaQuery<Object[]> aggQuery2 = criteriaBuilder.createQuery(Object[].class);
            Root<T> aggRoot = aggQuery.from(entityClass);
            Specification<T> filterSpec = searchBuilder.getFilterAggregSpec(entityClass, queries.getParsingResult(), field);
            // Apply filter if includeFilter is true
            if (includeFilter && filterSpec != null) {
                Predicate filterPredicate = filterSpec.toPredicate(aggRoot, aggQuery2, criteriaBuilder);
                
                if (filterPredicate != null) 
                	aggQuery.where(filterPredicate);                
            } 

            // Resolve nested field
            Expression<?> fieldExpression;
            fieldExpression = resolveNestedField(aggRoot, field);
           
            // Add group by and aggregation type
            aggQuery.groupBy(fieldExpression);

            Expression<?> aggregationExpression;
            switch (aggType.toLowerCase()) {
                case "count":
                    aggregationExpression = criteriaBuilder.count(fieldExpression);
                    break;
                case "average":
                    if (!(fieldExpression.getJavaType().isAssignableFrom(Number.class))) {
                        throw new IllegalArgumentException("Field " + field + " is not numeric for aggregation type 'average'.");
                    }
                    aggregationExpression = criteriaBuilder.avg((Expression<Number>) fieldExpression);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported aggregation type: " + aggType);
            }

            aggQuery.multiselect(fieldExpression, aggregationExpression);

            // Execute and process results
            List<Object[]> aggResult = entityManager.createQuery(aggQuery).getResultList();
            Map<Object, Object> aggregationMap = new HashMap<>();
            for (Object[] row : aggResult) {
                aggregationMap.put(row[0], row[1]); // [Group Value, Aggregated Value]
            }

            // Populate results in the AggregationResult object
            request.setResults(aggregationMap);
            aggregationResults.add(request);
        }

        return aggregationResults;
    }

        
    private Expression<?> resolveNestedField(Root<?> root, String fieldPath) {
        String[] fields = fieldPath.split("\\.");
        Path<?> path = root.get(fields[0]);
        for (int i = 1; i < fields.length; i++) {
            if (path == null) {
                throw new IllegalArgumentException("Invalid field path: " + fieldPath);
            }
            path = path.get(fields[i]);
        }
        return path;
    }

}
