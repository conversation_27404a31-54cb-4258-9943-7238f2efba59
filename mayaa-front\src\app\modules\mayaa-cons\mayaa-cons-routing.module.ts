import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { RealisationComponent } from './pages/realisation/realisation.component';
import { AutoconsommationComponent } from './pages/autoconsommation/autoconsommation.component';
import { ArretsConsolideComponent } from './pages/arrets-consolide/arrets-consolide.component';

const routes: Routes = [
  {
    path: 'realisation',
    data: {
      breadcrumb: 'Réalisation',
      //    permissions: ['$operateur.da.lister'],
      //  operateurChoosable: true,
    },
    component: RealisationComponent,
    //    canActivate: [AuthGuard, OperateurChoosableGuard],
  },
  {
    path: 'autoconsommation',
    data: {
      breadcrumb: 'Autoconsommation',
      //    permissions: ['$operateur.da.lister'],
      //  operateurChoosable: true,
    },
    component: AutoconsommationComponent,
    //    canActivate: [AuthGuard, OperateurChoosableGuard],
  },
  {
    path: 'arrets',
    data: {
      breadcrumb: 'Les Arrêts - Consolidé',
      //    permissions: ['$operateur.da.lister'],
      //  operateurChoosable: true,
    },
    component: ArretsConsolideComponent,
    //    canActivate: [AuthGuard, OperateurChoosableGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MayaaConsRoutingModule { }
