package dz.sonatrach.weblqs.mayaaback.controller;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import dz.sonatrach.weblqs.mayaaback.model.SituationTrainsArrets;
// import dz.sonatrach.weblqs.mayaaback.repo.SituationTrainsArretsRepository;

/**
 * Contrôleur pour l'accès aux données de la vue SITUATION_TRAINS_ARRETS.
 *
 * Endpoints principaux :
 *   GET /api/situation-trains-arrets/{unite}/{mois}
 *   GET /api/situation-trains-arrets/{unite}/{mois}/internes
 *   GET /api/situation-trains-arrets/{unite}/{mois}/externes
 *   GET /api/situation-trains-arrets/{unite}/{mois}/complexe
 *   GET /api/situation-trains-arrets/{unite}/{mois}/total-arrets
 *
 * - unite : code de l'unité (String, ex: "5X3")
 * - mois : période au format ddMMyyyy (ex: 01012024)
 *
 * Réponses :
 *   200 OK : données trouvées
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/situation-trains-arrets")
public class SituationTrainsArretsController {

    // Repository commenté temporairement - en attente de la création des vues/tables
    // @Autowired
    // private SituationTrainsArretsRepository situationTrainsArretsRepository;

    /**
     * Récupère la situation des trains pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des données de situation des trains
     */
    @GetMapping("/{unite}/{mois}")
    public ResponseEntity<List<SituationTrainsArrets>> getByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // Données simulées - remplacer par repository quand les vues seront créées
            // List<SituationTrainsArrets> result = situationTrainsArretsRepository.findByUniteAndMois(unite, date);
            List<SituationTrainsArrets> result = createMockSituationTrains(unite, date);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les causes internes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Données des causes internes
     */
    @GetMapping("/{unite}/{mois}/internes")
    public ResponseEntity<SituationTrainsArrets> getCausesInternesByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // Données simulées - remplacer par repository quand les vues seront créées
            // Optional<SituationTrainsArrets> result = situationTrainsArretsRepository.findCausesInternesByUniteAndMois(unite, date);
            Optional<SituationTrainsArrets> result = createMockSituationTrains(unite, date)
                    .stream()
                    .filter(s -> "INTERNES".equals(s.getTypeCause()))
                    .findFirst();
            
            return result.map(ResponseEntity::ok).orElse(ResponseEntity.noContent().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les causes externes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Données des causes externes
     */
    @GetMapping("/{unite}/{mois}/externes")
    public ResponseEntity<SituationTrainsArrets> getCausesExternesByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // Données simulées - remplacer par repository quand les vues seront créées
            // Optional<SituationTrainsArrets> result = situationTrainsArretsRepository.findCausesExternesByUniteAndMois(unite, date);
            Optional<SituationTrainsArrets> result = createMockSituationTrains(unite, date)
                    .stream()
                    .filter(s -> "EXTERNES".equals(s.getTypeCause()))
                    .findFirst();
            
            return result.map(ResponseEntity::ok).orElse(ResponseEntity.noContent().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère l'analyse du complexe pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Données d'analyse du complexe
     */
    @GetMapping("/{unite}/{mois}/complexe")
    public ResponseEntity<SituationTrainsArrets> getAnalyseComplexeByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // Données simulées - remplacer par repository quand les vues seront créées
            // Optional<SituationTrainsArrets> result = situationTrainsArretsRepository.findAnalyseComplexeByUniteAndMois(unite, date);
            SituationTrainsArrets complexe = createMockAnalyseComplexe(unite, date);
            
            return ResponseEntity.ok(complexe);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère le nombre total d'arrêts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Nombre total d'arrêts
     */
    @GetMapping("/{unite}/{mois}/total-arrets")
    public ResponseEntity<Integer> getTotalArretsByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            // Données simulées - remplacer par repository quand les vues seront créées
            // LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            // Integer total = situationTrainsArretsRepository.getTotalArretsByUniteAndMois(unite, date);
            Integer total = 45; // Total simulé
            
            return ResponseEntity.ok(total);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère l'évolution des données pour une période donnée
     * @param unite Code de l'unité
     * @param dateDebut Date de début au format ddMMyyyy
     * @param dateFin Date de fin au format ddMMyyyy
     * @return Liste des données sur la période
     */
    @GetMapping("/{unite}/evolution/{dateDebut}/{dateFin}")
    public ResponseEntity<List<SituationTrainsArrets>> getEvolutionByUniteAndPeriode(
            @PathVariable String unite,
            @PathVariable String dateDebut,
            @PathVariable String dateFin) {
        try {
            LocalDate debut = LocalDate.parse(dateDebut, DateTimeFormatter.ofPattern("ddMMyyyy"));
            LocalDate fin = LocalDate.parse(dateFin, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            // Données simulées - remplacer par repository quand les vues seront créées
            // List<SituationTrainsArrets> result = situationTrainsArretsRepository.findByUniteAndMoisBetween(unite, debut, fin);
            List<SituationTrainsArrets> result = createMockSituationTrains(unite, debut);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Méthodes privées pour créer des données mockées
    private List<SituationTrainsArrets> createMockSituationTrains(String unite, LocalDate date) {
        List<SituationTrainsArrets> result = new ArrayList<>();
        
        // Causes internes
        SituationTrainsArrets internes = new SituationTrainsArrets();
        internes.setId(1L);
        internes.setUnite(unite);
        internes.setMois(date);
        internes.setTypeCause("INTERNES");
        internes.setCausesPrincipales("Maintenance préventive, Défaillance compresseur, Problème turbine, Arrêt programmé");
        internes.setAnalyses("Les arrêts internes représentent 60% du total avec une prédominance des maintenances préventives programmées. " +
                           "Impact modéré sur la production grâce à une planification optimisée. Les défaillances d'équipements restent " +
                           "dans les normes acceptables avec un taux de disponibilité satisfaisant.");
        internes.setNombreArrets(27);
        result.add(internes);
        
        // Causes externes
        SituationTrainsArrets externes = new SituationTrainsArrets();
        externes.setId(2L);
        externes.setUnite(unite);
        externes.setMois(date);
        externes.setTypeCause("EXTERNES");
        externes.setCausesPrincipales("Panne réseau électrique, Arrêt fournisseur gaz, Conditions météorologiques, Maintenance réseau");
        externes.setAnalyses("Les arrêts externes représentent 40% du total avec des impacts significatifs sur la disponibilité. " +
                           "Nécessité d'améliorer la redondance des systèmes critiques et de renforcer les accords avec les fournisseurs. " +
                           "Les conditions météorologiques ont causé plusieurs arrêts non planifiés.");
        externes.setNombreArrets(18);
        result.add(externes);
        
        return result;
    }

    private SituationTrainsArrets createMockAnalyseComplexe(String unite, LocalDate date) {
        SituationTrainsArrets complexe = new SituationTrainsArrets();
        complexe.setId(999L);
        complexe.setUnite(unite);
        complexe.setMois(date);
        complexe.setTypeCause("COMPLEXE");
        complexe.setCausesPrincipales("Analyse globale du complexe " + unite);
        complexe.setAnalyses("Analyse consolidée de l'ensemble des trains du complexe " + unite + ". " +
                           "Performance globale satisfaisante avec des indicateurs dans les objectifs. " +
                           "Optimisation continue des processus de maintenance et amélioration de la fiabilité des équipements.");
        complexe.setNombreArrets(45);
        complexe.setAutoconsommationPourcentage(new BigDecimal("12.5"));
        complexe.setAutoconsommationNettePourcentage(new BigDecimal("11.8"));
        complexe.setGazTorchePourcentage(new BigDecimal("3.2"));
        complexe.setSiegesCausesGazTorche("Compresseur principal, Système de régulation, Turbine auxiliaire");
        complexe.setCausesRecurrentes("Maintenance préventive, Défaillance équipement, Problème réseau électrique");
        
        return complexe;
    }
}
