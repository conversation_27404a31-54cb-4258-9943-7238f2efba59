package dz.sonatrach.weblqs.mayaaback.dto;

import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO pour les données d'autoconsommation consolidées
 */
public class AutoconsommationResponse {

    @JsonView(View.basic.class)
    private String periode;

    @JsonView(View.basic.class)
    private LocalDateTime derniereMiseAJour;

    @JsonView(View.basic.class)
    private StatistiquesAutoconsommationDto statistiques;

    @JsonView(View.basic.class)
    private List<AutoconsommationUniteDto> unites;

    @JsonView(View.basic.class)
    private List<GazTorcheCauseDto> gazTorcheCauses;

    @JsonView(View.basic.class)
    private TotauxAutoconsommationDto totaux;

    @JsonView(View.basic.class)
    private List<EvolutionAutoconsommationDto> evolution;

    // Constructeurs
    public AutoconsommationResponse() {
        this.derniereMiseAJour = LocalDateTime.now();
    }

    public AutoconsommationResponse(String periode) {
        this.periode = periode;
        this.derniereMiseAJour = LocalDateTime.now();
    }

    // Méthode utilitaire
    public boolean isEmpty() {
        return (unites == null || unites.isEmpty()) &&
               (totaux == null);
    }

    // Getters et Setters
    public String getPeriode() { return periode; }
    public void setPeriode(String periode) { this.periode = periode; }

    public LocalDateTime getDerniereMiseAJour() { return derniereMiseAJour; }
    public void setDerniereMiseAJour(LocalDateTime derniereMiseAJour) { this.derniereMiseAJour = derniereMiseAJour; }

    public StatistiquesAutoconsommationDto getStatistiques() { return statistiques; }
    public void setStatistiques(StatistiquesAutoconsommationDto statistiques) { this.statistiques = statistiques; }

    public List<AutoconsommationUniteDto> getUnites() { return unites; }
    public void setUnites(List<AutoconsommationUniteDto> unites) { this.unites = unites; }

    public List<GazTorcheCauseDto> getGazTorcheCauses() { return gazTorcheCauses; }
    public void setGazTorcheCauses(List<GazTorcheCauseDto> gazTorcheCauses) { this.gazTorcheCauses = gazTorcheCauses; }

    public TotauxAutoconsommationDto getTotaux() { return totaux; }
    public void setTotaux(TotauxAutoconsommationDto totaux) { this.totaux = totaux; }

    public List<EvolutionAutoconsommationDto> getEvolution() { return evolution; }
    public void setEvolution(List<EvolutionAutoconsommationDto> evolution) { this.evolution = evolution; }

    /**
     * DTO pour les données d'autoconsommation d'une unité
     */
    public static class AutoconsommationUniteDto {
        @JsonView(View.basic.class)
        private String uniteCode;
        @JsonView(View.basic.class)
        private String unite;
        @JsonView(View.basic.class)
        private String statut;
        @JsonView(View.basic.class)
        private Double gnRecu;
        @JsonView(View.basic.class)
        private Double autoconsommationGlobale;
        @JsonView(View.basic.class)
        private Double autoconsommationNette;
        @JsonView(View.basic.class)
        private Double gazTorcheTotal;
        @JsonView(View.basic.class)
        private Double gazTorcheInterne;
        @JsonView(View.basic.class)
        private Double gazTorcheExterne;
        @JsonView(View.basic.class)
        private Double tauxAutoconsommationGlobale;
        @JsonView(View.basic.class)
        private Double tauxAutoconsommationNette;
        @JsonView(View.basic.class)
        private Double tauxGazTorche;
        @JsonView(View.basic.class)
        private Double tauxObjectif;
        @JsonView(View.basic.class)
        private Double ecartObjectif;

        public AutoconsommationUniteDto() {}

        // Getters et setters
        public String getUniteCode() { return uniteCode; }
        public void setUniteCode(String uniteCode) { this.uniteCode = uniteCode; }
        public String getUnite() { return unite; }
        public void setUnite(String unite) { this.unite = unite; }
        public String getStatut() { return statut; }
        public void setStatut(String statut) { this.statut = statut; }
        public Double getGnRecu() { return gnRecu; }
        public void setGnRecu(Double gnRecu) { this.gnRecu = gnRecu; }
        public Double getAutoconsommationGlobale() { return autoconsommationGlobale; }
        public void setAutoconsommationGlobale(Double autoconsommationGlobale) { this.autoconsommationGlobale = autoconsommationGlobale; }
        public Double getAutoconsommationNette() { return autoconsommationNette; }
        public void setAutoconsommationNette(Double autoconsommationNette) { this.autoconsommationNette = autoconsommationNette; }
        public Double getGazTorcheTotal() { return gazTorcheTotal; }
        public void setGazTorcheTotal(Double gazTorcheTotal) { this.gazTorcheTotal = gazTorcheTotal; }
        public Double getGazTorcheInterne() { return gazTorcheInterne; }
        public void setGazTorcheInterne(Double gazTorcheInterne) { this.gazTorcheInterne = gazTorcheInterne; }
        public Double getGazTorcheExterne() { return gazTorcheExterne; }
        public void setGazTorcheExterne(Double gazTorcheExterne) { this.gazTorcheExterne = gazTorcheExterne; }
        public Double getTauxAutoconsommationGlobale() { return tauxAutoconsommationGlobale; }
        public void setTauxAutoconsommationGlobale(Double tauxAutoconsommationGlobale) { this.tauxAutoconsommationGlobale = tauxAutoconsommationGlobale; }
        public Double getTauxAutoconsommationNette() { return tauxAutoconsommationNette; }
        public void setTauxAutoconsommationNette(Double tauxAutoconsommationNette) { this.tauxAutoconsommationNette = tauxAutoconsommationNette; }
        public Double getTauxGazTorche() { return tauxGazTorche; }
        public void setTauxGazTorche(Double tauxGazTorche) { this.tauxGazTorche = tauxGazTorche; }
        public Double getTauxObjectif() { return tauxObjectif; }
        public void setTauxObjectif(Double tauxObjectif) { this.tauxObjectif = tauxObjectif; }
        public Double getEcartObjectif() { return ecartObjectif; }
        public void setEcartObjectif(Double ecartObjectif) { this.ecartObjectif = ecartObjectif; }
    }

    /**
     * DTO pour les données de gaz torché par cause
     */
    public static class GazTorcheCauseDto {
        @JsonView(View.basic.class)
        private String cause;
        @JsonView(View.basic.class)
        private String type;
        @JsonView(View.basic.class)
        private Double quantite;
        @JsonView(View.basic.class)
        private Double pourcentage;

        public GazTorcheCauseDto() {}

        public GazTorcheCauseDto(String cause, String type, Double quantite, Double pourcentage) {
            this.cause = cause;
            this.type = type;
            this.quantite = quantite;
            this.pourcentage = pourcentage;
        }

        // Getters et setters
        public String getCause() { return cause; }
        public void setCause(String cause) { this.cause = cause; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Double getQuantite() { return quantite; }
        public void setQuantite(Double quantite) { this.quantite = quantite; }
        public Double getPourcentage() { return pourcentage; }
        public void setPourcentage(Double pourcentage) { this.pourcentage = pourcentage; }
    }

    /**
     * DTO pour les totaux d'autoconsommation consolidés
     */
    public static class TotauxAutoconsommationDto {
        @JsonView(View.basic.class)
        private Double totalGnRecu;
        @JsonView(View.basic.class)
        private Double totalAutoconsommationGlobale;
        @JsonView(View.basic.class)
        private Double totalAutoconsommationNette;
        @JsonView(View.basic.class)
        private Double totalGazTorche;
        @JsonView(View.basic.class)
        private Double totalGazTorcheInterne;
        @JsonView(View.basic.class)
        private Double totalGazTorcheExterne;
        @JsonView(View.basic.class)
        private Double tauxGlobalAutoconsommationGlobale;
        @JsonView(View.basic.class)
        private Double tauxGlobalAutoconsommationNette;
        @JsonView(View.basic.class)
        private Double tauxGlobalGazTorche;
        @JsonView(View.basic.class)
        private Double pourcentageGazTorcheInterne;
        @JsonView(View.basic.class)
        private Double pourcentageGazTorcheExterne;

        public TotauxAutoconsommationDto() {}

        // Getters et setters
        public Double getTotalGnRecu() { return totalGnRecu; }
        public void setTotalGnRecu(Double totalGnRecu) { this.totalGnRecu = totalGnRecu; }
        public Double getTotalAutoconsommationGlobale() { return totalAutoconsommationGlobale; }
        public void setTotalAutoconsommationGlobale(Double totalAutoconsommationGlobale) { this.totalAutoconsommationGlobale = totalAutoconsommationGlobale; }
        public Double getTotalAutoconsommationNette() { return totalAutoconsommationNette; }
        public void setTotalAutoconsommationNette(Double totalAutoconsommationNette) { this.totalAutoconsommationNette = totalAutoconsommationNette; }
        public Double getTotalGazTorche() { return totalGazTorche; }
        public void setTotalGazTorche(Double totalGazTorche) { this.totalGazTorche = totalGazTorche; }
        public Double getTotalGazTorcheInterne() { return totalGazTorcheInterne; }
        public void setTotalGazTorcheInterne(Double totalGazTorcheInterne) { this.totalGazTorcheInterne = totalGazTorcheInterne; }
        public Double getTotalGazTorcheExterne() { return totalGazTorcheExterne; }
        public void setTotalGazTorcheExterne(Double totalGazTorcheExterne) { this.totalGazTorcheExterne = totalGazTorcheExterne; }
        public Double getTauxGlobalAutoconsommationGlobale() { return tauxGlobalAutoconsommationGlobale; }
        public void setTauxGlobalAutoconsommationGlobale(Double tauxGlobalAutoconsommationGlobale) { this.tauxGlobalAutoconsommationGlobale = tauxGlobalAutoconsommationGlobale; }
        public Double getTauxGlobalAutoconsommationNette() { return tauxGlobalAutoconsommationNette; }
        public void setTauxGlobalAutoconsommationNette(Double tauxGlobalAutoconsommationNette) { this.tauxGlobalAutoconsommationNette = tauxGlobalAutoconsommationNette; }
        public Double getTauxGlobalGazTorche() { return tauxGlobalGazTorche; }
        public void setTauxGlobalGazTorche(Double tauxGlobalGazTorche) { this.tauxGlobalGazTorche = tauxGlobalGazTorche; }
        public Double getPourcentageGazTorcheInterne() { return pourcentageGazTorcheInterne; }
        public void setPourcentageGazTorcheInterne(Double pourcentageGazTorcheInterne) { this.pourcentageGazTorcheInterne = pourcentageGazTorcheInterne; }
        public Double getPourcentageGazTorcheExterne() { return pourcentageGazTorcheExterne; }
        public void setPourcentageGazTorcheExterne(Double pourcentageGazTorcheExterne) { this.pourcentageGazTorcheExterne = pourcentageGazTorcheExterne; }
    }

    /**
     * DTO pour l'évolution mensuelle de l'autoconsommation
     */
    public static class EvolutionAutoconsommationDto {
        @JsonView(View.basic.class)
        private String mois;
        @JsonView(View.basic.class)
        private Double autoconsommationNette;
        @JsonView(View.basic.class)
        private Double gazTorche;
        @JsonView(View.basic.class)
        private Double tauxAutoconsommationNette;
        @JsonView(View.basic.class)
        private Double tauxGazTorche;

        public EvolutionAutoconsommationDto() {}

        public EvolutionAutoconsommationDto(String mois, Double autoconsommationNette, Double gazTorche, 
                                          Double tauxAutoconsommationNette, Double tauxGazTorche) {
            this.mois = mois;
            this.autoconsommationNette = autoconsommationNette;
            this.gazTorche = gazTorche;
            this.tauxAutoconsommationNette = tauxAutoconsommationNette;
            this.tauxGazTorche = tauxGazTorche;
        }

        // Getters et setters
        public String getMois() { return mois; }
        public void setMois(String mois) { this.mois = mois; }
        public Double getAutoconsommationNette() { return autoconsommationNette; }
        public void setAutoconsommationNette(Double autoconsommationNette) { this.autoconsommationNette = autoconsommationNette; }
        public Double getGazTorche() { return gazTorche; }
        public void setGazTorche(Double gazTorche) { this.gazTorche = gazTorche; }
        public Double getTauxAutoconsommationNette() { return tauxAutoconsommationNette; }
        public void setTauxAutoconsommationNette(Double tauxAutoconsommationNette) { this.tauxAutoconsommationNette = tauxAutoconsommationNette; }
        public Double getTauxGazTorche() { return tauxGazTorche; }
        public void setTauxGazTorche(Double tauxGazTorche) { this.tauxGazTorche = tauxGazTorche; }
    }

    /**
     * DTO pour les statistiques d'autoconsommation
     */
    public static class StatistiquesAutoconsommationDto {
        @JsonView(View.basic.class)
        private Integer nombreUnitesAcceptables;
        @JsonView(View.basic.class)
        private Integer nombreUnitesASurveiller;
        @JsonView(View.basic.class)
        private Integer nombreUnitesCritiques;
        @JsonView(View.basic.class)
        private String performanceGlobale;

        public StatistiquesAutoconsommationDto() {}

        public StatistiquesAutoconsommationDto(Integer nombreUnitesAcceptables, Integer nombreUnitesASurveiller, 
                                             Integer nombreUnitesCritiques, String performanceGlobale) {
            this.nombreUnitesAcceptables = nombreUnitesAcceptables;
            this.nombreUnitesASurveiller = nombreUnitesASurveiller;
            this.nombreUnitesCritiques = nombreUnitesCritiques;
            this.performanceGlobale = performanceGlobale;
        }

        // Getters et setters
        public Integer getNombreUnitesAcceptables() { return nombreUnitesAcceptables; }
        public void setNombreUnitesAcceptables(Integer nombreUnitesAcceptables) { this.nombreUnitesAcceptables = nombreUnitesAcceptables; }
        public Integer getNombreUnitesASurveiller() { return nombreUnitesASurveiller; }
        public void setNombreUnitesASurveiller(Integer nombreUnitesASurveiller) { this.nombreUnitesASurveiller = nombreUnitesASurveiller; }
        public Integer getNombreUnitesCritiques() { return nombreUnitesCritiques; }
        public void setNombreUnitesCritiques(Integer nombreUnitesCritiques) { this.nombreUnitesCritiques = nombreUnitesCritiques; }
        public String getPerformanceGlobale() { return performanceGlobale; }
        public void setPerformanceGlobale(String performanceGlobale) { this.performanceGlobale = performanceGlobale; }
    }
}
