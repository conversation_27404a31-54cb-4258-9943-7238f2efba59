import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpResponse } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { environment } from '../../environments/environment';

/**
 * Intercepteur pour optimiser les performances des requêtes HTTP
 */
@Injectable()
export class PerformanceInterceptor implements HttpInterceptor {

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Ajouter des headers pour optimiser les performances
    const optimizedReq = req.clone({
      setHeaders: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=300', // Cache de 5 minutes par défaut
        'Accept': 'application/json'
      }
    });

    const startTime = Date.now();

    return next.handle(optimizedReq).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          const duration = Date.now() - startTime;

          // Logger les requêtes lentes (plus de 2 secondes)
          if (duration > 2000) {
            console.warn(`Requête lente détectée: ${req.method} ${req.url} - ${duration}ms`);
          }

          // Logger les réponses volumineuses (plus de 1MB)
          const responseSize = this.getResponseSize(event);
          if (responseSize > 1024 * 1024) {
            console.warn(`Réponse volumineuse détectée: ${req.url} - ${(responseSize / 1024 / 1024).toFixed(2)}MB`);
          }

          // Logger les performances en mode développement
          if (!environment.production) {
            console.log(`${req.method} ${req.url} - ${duration}ms - ${this.formatSize(responseSize)}`);
          }
        }
      })
    );
  }

  private getResponseSize(response: HttpResponse<any>): number {
    try {
      return JSON.stringify(response.body).length;
    } catch {
      return 0;
    }
  }

  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
