/**
 * Interface pour la synthèse des arrêts par train
 */
export interface SyntheseArrets {
  id: number;
  unite: string;
  codeTrain: string;
  mois: string;
  nbreHeures: number;
  map: number;
  acNette: number;
  gtInterne: number;
  gtExterne: number;
  plus24hInterne: number;
  plus24hExterne: number;
  moins24hInterne: number;
  moins24hExterne: number;
  moins5hInterne: number;
  moins5hExterne: number;
  sid: number;
  dei: number;
  av: number;
  ap: number;
}

/**
 * Interface pour la répartition des arrêts par siège
 */
export interface RepartitionArretsParSiege {
  id: number;
  unite: string;
  mois: string;
  siegeCause: string;
  typeSiege: 'INTERNE' | 'EXTERNE';
  quantiteArrets: number;
  pourcentage: number;
  numeroSiege: number;
}

/**
 * Interface pour la répartition des arrêts par cause
 */
export interface RepartitionArretsParCause {
  id: number;
  unite: string;
  mois: string;
  cause: string;
  typeCause: 'INTERNE' | 'EXTERNE' | 'CLASSE';
  quantiteArrets: number;
  pourcentage: number;
  numeroCause: number;
  classeCause?: string;
}

/**
 * Interface pour la situation des trains (arrêts)
 */
export interface SituationTrainsArrets {
  id: number;
  unite: string;
  mois: string;
  typeCause: 'INTERNES' | 'EXTERNES';
  causesPrincipales: string;
  analyses: string;
  nombreArrets: number;
  autoconsommationPourcentage?: number;
  autoconsommationNettePourcentage?: number;
  gazTorchePourcentage?: number;
  siegesCausesGazTorche?: string;
  causesRecurrentes?: string;
}

/**
 * Interface pour les données de graphique en secteurs
 */
export interface DonneesPieChart {
  label: string;
  value: number;
  pourcentage: number;
  color?: string;
}

/**
 * Interface pour les données d'évolution temporelle
 */
export interface DonneesEvolution {
  mois: string;
  valeur: number;
  type: string;
}

/**
 * Interface pour les options de filtre
 */
export interface FiltreArrets {
  unite: string;
  mois: string;
  train?: string;
  typeSiege?: 'INTERNE' | 'EXTERNE';
  typeCause?: 'INTERNE' | 'EXTERNE' | 'CLASSE';
}

/**
 * Interface pour les statistiques globales des arrêts
 */
export interface StatistiquesArrets {
  totalArrets: number;
  totalHeuresArret: number;
  pourcentageInternes: number;
  pourcentageExternes: number;
  trainLePlusAffecte: string;
  causePrincipale: string;
}

/**
 * Interface pour la réponse consolidée des arrêts
 */
export interface ReponseArrets {
  synthese: SyntheseArrets[];
  repartitionSiege: RepartitionArretsParSiege[];
  repartitionCause: RepartitionArretsParCause[];
  situationTrains: SituationTrainsArrets[];
  statistiques: StatistiquesArrets;
  trains: string[];
  sieges: string[];
  causes: string[];
}
