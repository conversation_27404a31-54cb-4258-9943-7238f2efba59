import { Component, OnInit, OnDestroy, effect, TrackByFunction } from '@angular/core';
import { Router } from '@angular/router';
import { MayaaUniteService } from '../../services/mayaa-unite.service';
import { AutoconsommationRecap } from '../../../../model/AutoconsommationData';
import { forkJoin, Subscription } from 'rxjs';
import { BilanGlobalComplexe, LossItem } from '../../../../model/BilanGlobalComplexe';
import { GazTorchDetails } from '../../../../model/GazTorchDetails';
import { AutoConsMens } from '../../../../model/AutoConsMens';
import { CalendarService } from '../../../../services/calendar.service';
import { ListUnite } from '../../../../model/ListUnite';

@Component({
  selector: 'app-bilan',
  templateUrl: './bilan.component.html',
  styleUrls: ['./bilan.component.scss']
  // Retour à la détection de changements par défaut pour éviter les problèmes d'affichage
  // changeDetection: ChangeDetectionStrategy.OnPush
})
export class BilanComponent implements OnInit, OnDestroy {
  chartData: any;
  chartOptions: any;
  pieChartData: any;
  pieChartOptions: any;

  // Variables pour l'autoconsommation
  tauxAutoconsommation: number = 0;
  tauxGazTorche: number = 0;
  tauxTotal: number = 0;
  tauxDesign: number = 0; // Sera récupéré depuis LIST_UNITE
  tauxObjectif: number = 0; // Sera récupéré depuis LIST_UNITE

  loading = false;
  error: string | null = null;
  bilan: BilanGlobalComplexe | null = null;

  // Nouvelles propriétés pour les données de gaz torché
  gazTorchDetails: GazTorchDetails[] = [];
  receptionGnMois: number = 0;

  // Fonctions trackBy pour optimiser les performances
  trackByPoste: TrackByFunction<any> = (index: number, item: any) => item.label || index;
  trackByLoss: TrackByFunction<any> = (index: number, item: any) => item.label || index;

  constructor(
    private mayaaUniteService: MayaaUniteService,
    private router: Router,
    private calendarService: CalendarService
  ) {
    // Effect pour écouter les changements de date/unité et recharger les données
    effect(() => {
      const unite = this.calendarService.selectedUnite();
      const pmois = this.calendarService.pmoisFormat();

      // Recharger les données quand la date ou l'unité change
      this.loadAutoconsommationData(unite, pmois);
      this.loadGazTorchData(pmois, unite);
    });
  }

  ngOnInit() {
    // Les données seront chargées automatiquement via l'effect dans le constructeur
    // qui écoute les changements du CalendarService
    const unite = this.calendarService.selectedUnite();
    const pmois = this.calendarService.pmoisFormat();

    // Recharger les données quand la date ou l'unité change
    this.loadAutoconsommationData(unite, pmois);
    this.loadGazTorchData(pmois, unite);
  }

  ngOnDestroy() {
    // Cleanup automatique des effects Angular
  }

  /**
   * Charge les données d'autoconsommation pour une unité et période données
   * @param unite Code de l'unité
   * @param pmois Période au format ddMMyyyy
   */
  loadAutoconsommationData(unite: string, pmois: string) {
    forkJoin({
      consommation: this.mayaaUniteService.getAutoconsommationData(
        unite,
        pmois
      ),
      ce: this.mayaaUniteService.getConsommationEnergetique(unite, pmois),
      objectifs: this.mayaaUniteService.getUniteObjectifs(unite, pmois),
    }).subscribe({
      next: ({ consommation, ce, objectifs }) => {
        this.prepareChartData(consommation);
        this.preparePieChartData(ce);
        this.calculateAutoconsommationRates(consommation);

        // Charger les taux objectif et design depuis LIST_UNITE
        if (objectifs) {
          this.tauxObjectif = objectifs.tauxAcObj;
          this.tauxDesign = objectifs.tauxAcDesign;
        } else {
          // Valeurs par défaut si pas de données trouvées
          console.warn(`Aucun objectif trouvé pour l'unité ${unite} et la période ${pmois}`);
          this.tauxObjectif = 0;
          this.tauxDesign = 0;
        }

        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des données:', error);
        // En cas d'erreur, utiliser la méthode par défaut
        this.mayaaUniteService
          .getAutoconsommationDataDefault()
          .subscribe((consommation) => {
            this.prepareChartData(consommation);
            this.calculateAutoconsommationRates(consommation);
            // Valeurs par défaut en cas d'erreur
            this.tauxObjectif = 0;
            this.tauxDesign = 0;
            this.loading = false;
          });
      },
    });
  }

  /**
   * Charge les données de gaz torché depuis le contrôleur
   * @param pmois Période au format ddMMyyyy
   * @param unite Code de l'unité
   */
  loadGazTorchData(pmois: string, unite: string) {
    this.loading = true;
    this.error = null;

    // Charger les données de gaz torché et la quantité de GN reçu en parallèle
    forkJoin({
      gazTorchDetails: this.mayaaUniteService.getGazTorchDetails(pmois, unite),
      autoConsMens: this.mayaaUniteService.getBilanUnite(
        pmois.substring(2, 4), // mois
        pmois.substring(4, 8), // année
        unite
      ),
    }).subscribe({
      next: ({ gazTorchDetails, autoConsMens }) => {
        this.gazTorchDetails = gazTorchDetails;
        this.receptionGnMois = autoConsMens.receptionGnMois || 0;
        this.loading = false;
      },
      error: (err) => {
        console.error(
          'Erreur lors du chargement des données de gaz torché:',
          err
        );
        this.error = err.message;
        this.loading = false;
      },
    });
  }
  /** Somme des valeurs de pertes (calculée depuis les données réelles, en Cm³) */
  get totalLossValue(): number {
    return (
      this.gazTorchDetails.reduce(
        (sum, item) => sum + (item.quantiteGazTorchee || 0),
        0
      ) / 1000
    );
  }

  /** Somme des pourcentages de pertes (calculée par rapport au GN reçu) */
  get totalLossPercent(): number {
    if (this.receptionGnMois === 0) return 0;
    // Convertir les quantités de gaz torché (m³) en Cm³ pour le calcul du pourcentage
    const totalOriginalCm3 =
      this.gazTorchDetails.reduce(
        (sum, item) => sum + (item.quantiteGazTorchee || 0),
        0
      ) / 1000;
    return (totalOriginalCm3 * 100) / this.receptionGnMois;
  }

  get totalNetValue(): number {
    return this.bilan
      ? this.bilan.postes.reduce((sum, l) => sum + l.value, 0)
      : 0;
  }

  /** Somme des pourcentages de pertes */
  get totalNetPercent(): number {
    return this.bilan
      ? this.bilan.postes.reduce((sum, l) => sum + l.percent, 0)
      : 0;
  }

  /**
   * Convertit les données de gaz torché en format LossItem pour l'affichage
   * Divise les quantités par 1000 et trie par ordre décroissant
   */
  get gazTorchLossItems(): LossItem[] {
    return this.gazTorchDetails
      .map((item) => {
        const quantiteCm3 = (item.quantiteGazTorchee || 0) / 1000; // Conversion en Cm³
        const percentValue =
          this.receptionGnMois > 0
            ? (((item.quantiteGazTorchee || 0) / 1000) * 100) /
              this.receptionGnMois
            : 0; // Conversion m³ -> Cm³ pour le %

        return {
          label: item.libTypeDysfonct || 'Gaz torché',
          value: quantiteCm3,
          percent: percentValue,
          category: 'GazTorchés' as const,
          causeType: this.getCauseTypeFromCode(item.codeCauseTorchage!),
        };
      })
      .sort((a, b) => b.value - a.value); // Tri par ordre décroissant de quantité
  }

  /**
   * Détermine le type de cause (Interne/Externe) à partir du code
   * @param codeCauseTorchage Code de cause (I pour Interne, E pour Externe)
   */
  private getCauseTypeFromCode(
    codeCauseTorchage: string
  ): 'Interne' | 'Externe' {
    if (!codeCauseTorchage) return 'Interne';
    return codeCauseTorchage.toUpperCase().startsWith('I')
      ? 'Interne'
      : 'Externe';
  }

  private calculateAutoconsommationRates(data: AutoconsommationRecap) {
    // Calculer le taux d'autoconsommation (global, pas de distinction de source)
    this.tauxAutoconsommation = data.data
      .filter((item) => item.category === 'consommation')
      .reduce((sum, item) => sum + item.pourcentageGN, 0);

    // Calculer le taux de gaz torché (somme interne + externe)
    this.tauxGazTorche = data.data
      .filter((item) => item.category === 'gazTorches')
      .reduce((sum, item) => sum + item.pourcentageGN, 0);

    this.tauxTotal = this.tauxAutoconsommation + this.tauxGazTorche;
  }

  getProgressBarColor(value: number, target: number): string {
    const ratio = (value / target) * 100;
    if (ratio < 100) {
      return 'p-progressbar-good';
    } else if (ratio <= 105) {
      return 'p-progressbar-warning';
    } else {
      return 'p-progressbar-danger';
    }
  }
  getTauxACColor(value: number, target: number): string {
    const ratio = (value / target) * 100;
    if (ratio < 100) {
      return 'text-green-500';
    } else if (ratio <= 105) {
      return 'text-orange-500';
    } else {
      return 'text-red-500';
    }
  }

  /**
   * Calcule l'écart en points de pourcentage entre la valeur actuelle et la cible
   * @param value Valeur actuelle
   * @param target Valeur cible
   * @returns Écart en points de pourcentage (positif = dépassement, négatif = sous-performance)
   */
  getEcartPoints(value: number, target: number): number {
    return value - target;
  }

  /**
   * Retourne la couleur CSS pour l'affichage de l'écart
   * @param ecart Écart en points de pourcentage
   * @returns Classe CSS pour la couleur
   */
  getEcartColor(ecart: number): string {
    if (ecart <= 0) {
      return 'text-green-500'; // Vert si on est en dessous ou égal à la cible (bon)
    } else if (ecart <= 0.5) {
      return 'text-orange-500'; // Orange si légèrement au-dessus
    } else {
      return 'text-red-500'; // Rouge si nettement au-dessus (mauvais)
    }
  }

  private prepareChartData(data: AutoconsommationRecap) {
    // Préparer les données pour le graphique
    // Consommation nette : une seule barre (globale)
    // Gaz torchés : deux barres (interne/externe)

    const consommationNette = data.data.find(
      (item) => item.category === 'consommation'
    );
    const gazTorchesInterne = data.data.find(
      (item) => item.category === 'gazTorches' && item.sourceType === 'interne'
    );
    const gazTorchesExterne = data.data.find(
      (item) => item.category === 'gazTorches' && item.sourceType === 'externe'
    );

    this.chartData = {
      labels: ['Consommation Nette', 'Gaz Torchés'],
      datasets: [
        {
          label: 'Global/Interne',
          data: [consommationNette?.cm3GN || 0, gazTorchesInterne?.cm3GN || 0],
          backgroundColor: '#4CAF50',
        },
        {
          label: 'Externe',
          data: [
            0, // Pas de consommation nette externe
            gazTorchesExterne?.cm3GN || 0,
          ],
          backgroundColor: '#2196F3',
        },
      ],
    };

    this.chartOptions = {
      indexAxis: 'y',
      plugins: {
        title: {
          display: true,
          text: `Autoconsomation ${data.mois} ${data.annee}`,
          font: {
            size: 16,
          },
        },
        tooltip: {
          mode: 'index',
          intersect: true,
          callbacks: {
            label: function (context: any) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.x !== null) {
                label +=
                  new Intl.NumberFormat('fr-FR').format(context.parsed.x) +
                  ' Cm³';
              }
              return label;
            },
          },
        },
        legend: {
          labels: {
            color: '#495057',
          },
        },
      },

      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          stacked: true,
          ticks: {
            color: '#495057',
            callback: function (value: any) {
              return new Intl.NumberFormat('fr-FR').format(value) + ' Cm³';
            },
          },
          grid: {
            color: '#ebedef',
          },
        },
        y: {
          stacked: true,
          ticks: {
            color: '#495057',
          },
          grid: {
            color: '#ebedef',
          },
        },
      },
    };
  }

  private preparePieChartData(data: AutoconsommationRecap) {
    this.pieChartData = {
      labels: data.data.map((item) => item.label),
      datasets: [
        {
          data: data.data.map((item) => item.cm3GN),
          backgroundColor: ['#FF9800', '#9C27B0'],
        },
      ],
    };

    const totalCE = data.data.reduce((sum, item) => sum + item.cm3GN, 0);

    this.pieChartOptions = {
      plugins: {
        title: {
          display: true,
          text: `Répartition CE (eq GN) - ${data.mois} ${data.annee}`,
          font: {
            size: 16,
          },
        },
        tooltip: {
          callbacks: {
            label: function (context: any) {
              const value = context.raw;
              const percentage = ((value / totalCE) * 100).toFixed(1);
              return `${context.label}: ${new Intl.NumberFormat('fr-FR').format(
                value
              )} Cm³ (${percentage}%)`;
            },
          },
        },
        legend: {
          position: 'bottom',
        },
      },
      responsive: true,
      cutout: '50%',
    };
  }
}
