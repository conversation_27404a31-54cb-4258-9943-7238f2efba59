package dz.sonatrach.weblqs.mayaaback.service;

import dz.sonatrach.weblqs.mayaaback.dto.DashboardConsolideResponse;
import dz.sonatrach.weblqs.mayaaback.dto.DashboardConsolideResponse.*;
import dz.sonatrach.weblqs.mayaaback.model.AutoConsMens;
import dz.sonatrach.weblqs.mayaaback.model.ListUnite;
import dz.sonatrach.weblqs.mayaaback.model.GazTorcheeParCauseTorchage;
import dz.sonatrach.weblqs.mayaaback.model.IndicateurPeformanceUnite;
import dz.sonatrach.weblqs.mayaaback.repo.AutoConsMensuelRepository;
import dz.sonatrach.weblqs.mayaaback.repo.ListUniteRepository;
import dz.sonatrach.weblqs.mayaaback.repo.GazTorcheeParCauseTorchageRepository;
import dz.sonatrach.weblqs.mayaaback.repo.IndicateurPeformanceUniteRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * Service pour la consolidation des données du dashboard
 */
@Service
public class DashboardService {

    @Autowired
    private ListUniteRepository listUniteRepository;

    @Autowired
    private AutoConsMensuelRepository autoConsMensuelRepository;

    @Autowired
    private GazTorcheeParCauseTorchageRepository gazTorcheeRepository;

    @Autowired
    private IndicateurPeformanceUniteRepository indicateurPerformanceRepository;

    private static final List<String> UNITES_PRINCIPALES = Arrays.asList("5X1", "5X2", "5X3", "5X8");

    /**
     * Récupère toutes les données consolidées pour une période donnée
     */
    @Cacheable(value = "dashboard-consolide", key = "#pmois")
    public DashboardConsolideResponse getDashboardConsolide(LocalDate pmois) {
        String pmoisStr = pmois.format(DateTimeFormatter.ofPattern("ddMMyyyy"));
        DashboardConsolideResponse response = new DashboardConsolideResponse(pmoisStr);

        try {
            System.out.println("=== DEBUT CONSOLIDATION DASHBOARD ===");
            System.out.println("Période: " + pmoisStr);

            // 1. Récupérer les unités
            List<ListUnite> unites = listUniteRepository.findByPmois(pmois);
            response.setUnites(unites);
            System.out.println("Unités trouvées: " + unites.size());

            // 2. Récupérer les données d'autoconsommation (optimisé - une seule requête)
            List<AutoConsMens> autoconsommation = autoConsMensuelRepository.findByPmoisAndUniteIn(pmois, UNITES_PRINCIPALES);
            response.setAutoconsommation(autoconsommation);
            System.out.println("Autoconsommation trouvée: " + autoconsommation.size() + " entrées");

            // 3. Récupérer les données de gaz torché (optimisé - une seule requête)
            List<GazTorcheeParCauseTorchage> gazTorche = gazTorcheeRepository.findByPmoisAndUniteIn(pmois, UNITES_PRINCIPALES);
            response.setGazTorche(gazTorche);
            System.out.println("Gaz torché trouvé: " + gazTorche.size() + " entrées");

            // 4. Récupérer les indicateurs de performance (optimisé - une seule requête)
            List<IndicateurPeformanceUnite> indicateurs = indicateurPerformanceRepository.findByMoisAndUniteIn(pmois, UNITES_PRINCIPALES);
            response.setIndicateursPerformance(indicateurs);
            System.out.println("Indicateurs trouvés: " + indicateurs.size());

            // 5. Calculer les totaux
            TotauxDto totaux = calculerTotaux(autoconsommation, indicateurs);
            response.setTotaux(totaux);

            // 6. Calculer les statistiques
            StatistiquesDto statistiques = calculerStatistiques(autoconsommation);
            response.setStatistiques(statistiques);

            // 7. Évolution mensuelle vide pour l'instant
            response.setEvolutionMensuelle(new ArrayList<>());

            System.out.println("=== FIN CONSOLIDATION DASHBOARD ===");
            return response;

        } catch (Exception e) {
            System.err.println("Erreur générale lors de la consolidation: " + e.getMessage());
            e.printStackTrace();
            return response;
        }
    }

    private TotauxDto calculerTotaux(List<AutoConsMens> autoconsommation, List<IndicateurPeformanceUnite> indicateurs) {
        TotauxDto totaux = new TotauxDto();

        try {
            double totalAutoconsommationNette = autoconsommation.stream()
                .mapToDouble(ac -> ac.getAutoConsMoisNet() != null ? ac.getAutoConsMoisNet().doubleValue() : 0)
                .sum();
            
            double totalGazTorche = autoconsommation.stream()
                .mapToDouble(ac -> ac.getGazTorcheeMois() != null ? ac.getGazTorcheeMois().doubleValue() : 0)
                .sum();
            
            double totalGnRecu = autoconsommation.stream()
                .mapToDouble(ac -> ac.getReceptionGnMois() != null ? ac.getReceptionGnMois().doubleValue() : 0)
                .sum();

            double totalProductionReelle = indicateurs.stream()
                .mapToDouble(ind -> ind.getProduction() != null ? ind.getProduction().doubleValue() : 0)
                .sum();
            
            double totalObjectif = indicateurs.stream()
                .mapToDouble(ind -> ind.getProductionDesign() != null ? ind.getProductionDesign().doubleValue() : 0)
                .sum();

            double tauxGlobalRealisationObjectif = totalObjectif > 0 ? (totalProductionReelle / totalObjectif) * 100 : 0;

            totaux.setTotalAutoconsommationNette(totalAutoconsommationNette);
            totaux.setTotalGazTorche(totalGazTorche);
            totaux.setTotalGnRecu(totalGnRecu);
            totaux.setTotalProductionReelle(totalProductionReelle);
            totaux.setTotalObjectif(totalObjectif);
            totaux.setTauxGlobalRealisationObjectif(tauxGlobalRealisationObjectif);

            System.out.println("Totaux calculés - AutoCons: " + totalAutoconsommationNette + ", GazTorche: " + totalGazTorche + ", Production: " + totalProductionReelle);

        } catch (Exception e) {
            System.err.println("Erreur calcul totaux: " + e.getMessage());
        }

        return totaux;
    }

    private StatistiquesDto calculerStatistiques(List<AutoConsMens> autoconsommation) {
        int nombreUnitesActives = 0;
        int nombreUnitesArretTotal = 0;

        try {
            for (AutoConsMens autoCons : autoconsommation) {
                double gnRecu = autoCons.getReceptionGnMois() != null ? autoCons.getReceptionGnMois().doubleValue() : 0;
                if (gnRecu > 0) {
                    nombreUnitesActives++;
                } else {
                    nombreUnitesArretTotal++;
                }
            }
        } catch (Exception e) {
            System.err.println("Erreur calcul statistiques: " + e.getMessage());
        }

        return new StatistiquesDto(nombreUnitesActives, nombreUnitesArretTotal, 0);
    }
}
