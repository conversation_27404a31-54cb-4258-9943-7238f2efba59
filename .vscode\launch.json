{"version": "0.2.0", "configurations": [{"type": "java", "name": "Debug <PERSON><PERSON>", "request": "launch", "mainClass": "dz.sonatrach.weblqs.mayaaback.MayaaBackApplication", "projectName": "mayaa_back", "args": "", "vmArgs": "-Dspring.profiles.active=local -Dspring.config.location=src/main/resources/application.properties,src/main/resources/application-local.properties", "cwd": "${workspaceFolder}/mayaa-back"}, {"name": "Debug Mayaa Frontend", "type": "chrome", "request": "launch", "preLaunchTask": "Start Angular Dev Server", "postDebugTask": "Terminate Angular Dev Server", "url": "http://localhost:4200/", "webRoot": "${workspaceFolder}/mayaa-front", "sourceMapPathOverrides": {"webpack:/*": "${workspaceFolder}/mayaa-front/*", "/./*": "${workspaceFolder}/mayaa-front/src/*", "/src/*": "${workspaceFolder}/mayaa-front/src/*", "/*": "*", "/./~/*": "${workspaceFolder}/mayaa-front/node_modules/*"}, "smartStep": true, "skipFiles": ["<node_internals>/**", "${workspaceFolder}/mayaa-front/node_modules/**"], "trace": true}, {"name": "Debug Frontend Tests", "type": "chrome", "request": "launch", "preLaunchTask": "Start Angular Test Server", "url": "http://localhost:9876/debug.html", "webRoot": "${workspaceFolder}/mayaa-front", "sourceMapPathOverrides": {"webpack:/*": "${workspaceFolder}/mayaa-front/*", "/./*": "${workspaceFolder}/mayaa-front/src/*", "/src/*": "${workspaceFolder}/mayaa-front/src/*", "/*": "*", "/./~/*": "${workspaceFolder}/mayaa-front/node_modules/*"}}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/mayaa-front", "sourceMapPathOverrides": {"webpack:/*": "${workspaceFolder}/mayaa-front/*", "/./*": "${workspaceFolder}/mayaa-front/src/*", "/src/*": "${workspaceFolder}/mayaa-front/src/*", "/*": "*", "/./~/*": "${workspaceFolder}/mayaa-front/node_modules/*"}}], "compounds": [{"name": "Debug Full Stack (Frontend + Backend)", "configurations": ["Debug <PERSON><PERSON>", "Debug Mayaa Frontend"], "stopAll": true}]}