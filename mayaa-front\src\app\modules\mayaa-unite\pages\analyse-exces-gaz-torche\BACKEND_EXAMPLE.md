# Exemple d'implémentation Backend - Gaz Torché

## Contrôleur Java (Spring Boot)

```java
@RestController
@RequestMapping("/api/mayaa-unite")
@CrossOrigin(origins = "*")
public class AnalyseExcesGazTorcheController {

    @Autowired
    private AnalyseExcesGazTorcheService analyseExcesGazTorcheService;

    /**
     * Récupère les données d'analyse d'excès de gaz torché
     */
    @GetMapping("/analyse-exces-gaz-torche")
    public ResponseEntity<List<AnalyseExcesGazTorche>> getGazTorcheData(
            @RequestParam String unite,
            @RequestParam String mois) {
        
        try {
            List<AnalyseExcesGazTorche> data = analyseExcesGazTorcheService.getGazTorcheData(unite, mois);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère les données avec statistiques
     */
    @GetMapping("/analyse-exces-gaz-torche/stats")
    public ResponseEntity<AnalyseExcesGazTorcheResponse> getGazTorcheDataWithStats(
            @RequestParam String unite,
            @RequestParam String mois) {
        
        try {
            AnalyseExcesGazTorcheResponse response = analyseExcesGazTorcheService.getGazTorcheDataWithStats(unite, mois);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère les statistiques globales
     */
    @GetMapping("/analyse-exces-gaz-torche/global-stats")
    public ResponseEntity<GlobalStatistics> getGlobalStatistics(
            @RequestParam String unite,
            @RequestParam String mois) {
        
        try {
            GlobalStatistics stats = analyseExcesGazTorcheService.getGlobalStatistics(unite, mois);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
```

## Modèles Java (DTOs)

```java
// AnalyseExcesGazTorche.java
public class AnalyseExcesGazTorche {
    private String codeAc;
    private String problemeSpecifique;
    private String intitule;
    private Double ac; // 10³ CM³ GN
    private String classeCauses;
    private String causes;
    private String actions;
    private String classes;
    private String etat;
    private String numero;
    private String trainName;
    
    // Constructeurs, getters et setters
}

// AnalyseExcesGazTorcheResponse.java
public class AnalyseExcesGazTorcheResponse {
    private List<AnalyseExcesGazTorche> trains;
    private Double totalAC;
    private Integer totalItems;
    private Integer trainsCount;
    
    // Constructeurs, getters et setters
}

// TrainStatistics.java
public class TrainStatistics {
    private String trainName;
    private Double totalAC;
    private Integer itemCount;
    private Map<String, Integer> statusDistribution;
    private Map<String, Integer> criticalityDistribution;
    
    // Constructeurs, getters et setters
}

// GlobalStatistics.java
public class GlobalStatistics {
    private Double totalAC;
    private Integer totalItems;
    private Integer trainsCount;
    private Map<String, Integer> statusDistribution;
    private Map<String, Integer> criticalityDistribution;
    private List<TrainStatistics> trainStatistics;
    
    // Constructeurs, getters et setters
}
```

## Service Java

```java
@Service
public class AnalyseExcesGazTorcheService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<AnalyseExcesGazTorche> getGazTorcheData(String unite, String mois) {
        String sql = """
            SELECT 
                CODE_AC,
                PROBLEME_SPECIFIQUE,
                INTITULE,
                AC,
                CLASSE_CAUSES,
                CAUSES,
                ACTIONS,
                CLASSES,
                ETAT,
                NUMERO,
                TRAIN_NAME
            FROM ANALYSE_EXCES_GAZ_TORCHE
            WHERE UNITE = ? AND MOIS = ?
            ORDER BY TRAIN_NAME, AC DESC
        """;
        
        return jdbcTemplate.query(sql, new Object[]{unite, mois}, 
            (rs, rowNum) -> {
                AnalyseExcesGazTorche item = new AnalyseExcesGazTorche();
                item.setCodeAc(rs.getString("CODE_AC"));
                item.setProblemeSpecifique(rs.getString("PROBLEME_SPECIFIQUE"));
                item.setIntitule(rs.getString("INTITULE"));
                item.setAc(rs.getDouble("AC"));
                item.setClasseCauses(rs.getString("CLASSE_CAUSES"));
                item.setCauses(rs.getString("CAUSES"));
                item.setActions(rs.getString("ACTIONS"));
                item.setClasses(rs.getString("CLASSES"));
                item.setEtat(rs.getString("ETAT"));
                item.setNumero(rs.getString("NUMERO"));
                item.setTrainName(rs.getString("TRAIN_NAME"));
                return item;
            });
    }

    public AnalyseExcesGazTorcheResponse getGazTorcheDataWithStats(String unite, String mois) {
        List<AnalyseExcesGazTorche> trains = getGazTorcheData(unite, mois);
        
        Double totalAC = trains.stream()
            .mapToDouble(AnalyseExcesGazTorche::getAc)
            .sum();
            
        Set<String> uniqueTrains = trains.stream()
            .map(AnalyseExcesGazTorche::getTrainName)
            .collect(Collectors.toSet());
        
        AnalyseExcesGazTorcheResponse response = new AnalyseExcesGazTorcheResponse();
        response.setTrains(trains);
        response.setTotalAC(totalAC);
        response.setTotalItems(trains.size());
        response.setTrainsCount(uniqueTrains.size());
        
        return response;
    }

    public GlobalStatistics getGlobalStatistics(String unite, String mois) {
        List<AnalyseExcesGazTorche> data = getGazTorcheData(unite, mois);
        
        GlobalStatistics stats = new GlobalStatistics();
        
        // Calculs globaux
        Double totalAC = data.stream().mapToDouble(AnalyseExcesGazTorche::getAc).sum();
        Set<String> uniqueTrains = data.stream()
            .map(AnalyseExcesGazTorche::getTrainName)
            .collect(Collectors.toSet());
        
        stats.setTotalAC(totalAC);
        stats.setTotalItems(data.size());
        stats.setTrainsCount(uniqueTrains.size());
        
        // Distributions globales
        Map<String, Integer> statusDist = data.stream()
            .collect(Collectors.groupingBy(
                AnalyseExcesGazTorche::getEtat,
                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
            ));
        stats.setStatusDistribution(statusDist);
        
        Map<String, Integer> criticalityDist = data.stream()
            .collect(Collectors.groupingBy(
                AnalyseExcesGazTorche::getClasses,
                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
            ));
        stats.setCriticalityDistribution(criticalityDist);
        
        // Statistiques par train
        List<TrainStatistics> trainStats = uniqueTrains.stream()
            .map(trainName -> {
                List<AnalyseExcesGazTorche> trainData = data.stream()
                    .filter(item -> trainName.equals(item.getTrainName()))
                    .collect(Collectors.toList());
                
                TrainStatistics trainStat = new TrainStatistics();
                trainStat.setTrainName(trainName);
                trainStat.setTotalAC(trainData.stream().mapToDouble(AnalyseExcesGazTorche::getAc).sum());
                trainStat.setItemCount(trainData.size());
                
                Map<String, Integer> trainStatusDist = trainData.stream()
                    .collect(Collectors.groupingBy(
                        AnalyseExcesGazTorche::getEtat,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                    ));
                trainStat.setStatusDistribution(trainStatusDist);
                
                Map<String, Integer> trainCriticalityDist = trainData.stream()
                    .collect(Collectors.groupingBy(
                        AnalyseExcesGazTorche::getClasses,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                    ));
                trainStat.setCriticalityDistribution(trainCriticalityDist);
                
                return trainStat;
            })
            .collect(Collectors.toList());
        
        stats.setTrainStatistics(trainStats);
        
        return stats;
    }
}
```

## Structure de base de données

```sql
-- Table pour l'analyse d'excès de gaz torché
CREATE TABLE ANALYSE_EXCES_GAZ_TORCHE (
    ID NUMBER PRIMARY KEY,
    UNITE VARCHAR2(10) NOT NULL,
    MOIS VARCHAR2(7) NOT NULL, -- Format YYYY-MM
    CODE_AC VARCHAR2(20),
    PROBLEME_SPECIFIQUE VARCHAR2(500),
    INTITULE VARCHAR2(200),
    AC NUMBER(10,2), -- 10³ CM³ GN
    CLASSE_CAUSES VARCHAR2(100),
    CAUSES VARCHAR2(1000),
    ACTIONS VARCHAR2(1000),
    CLASSES VARCHAR2(50), -- Critique, Majeur, Mineur
    ETAT VARCHAR2(50), -- En cours, Terminé, Planifié, Annulé
    NUMERO VARCHAR2(20),
    TRAIN_NAME VARCHAR2(50) NOT NULL,
    CREATED_DATE DATE DEFAULT SYSDATE,
    UPDATED_DATE DATE DEFAULT SYSDATE
);

-- Index pour les performances
CREATE INDEX IDX_ANALYSE_EXCES_GT_UNITE_MOIS ON ANALYSE_EXCES_GAZ_TORCHE(UNITE, MOIS);
CREATE INDEX IDX_ANALYSE_EXCES_GT_TRAIN ON ANALYSE_EXCES_GAZ_TORCHE(TRAIN_NAME);
CREATE INDEX IDX_ANALYSE_EXCES_GT_AC ON ANALYSE_EXCES_GAZ_TORCHE(AC DESC);

-- Contraintes
ALTER TABLE ANALYSE_EXCES_GAZ_TORCHE ADD CONSTRAINT CHK_CLASSES 
    CHECK (CLASSES IN ('Critique', 'Majeur', 'Mineur'));
    
ALTER TABLE ANALYSE_EXCES_GAZ_TORCHE ADD CONSTRAINT CHK_ETAT 
    CHECK (ETAT IN ('En cours', 'Terminé', 'Planifié', 'Annulé'));
```

## Requêtes SQL Optimisées

```sql
-- Requête principale avec statistiques
SELECT 
    CODE_AC,
    PROBLEME_SPECIFIQUE,
    INTITULE,
    AC,
    CLASSE_CAUSES,
    CAUSES,
    ACTIONS,
    CLASSES,
    ETAT,
    NUMERO,
    TRAIN_NAME,
    -- Statistiques par train
    SUM(AC) OVER (PARTITION BY TRAIN_NAME) AS TRAIN_TOTAL_AC,
    COUNT(*) OVER (PARTITION BY TRAIN_NAME) AS TRAIN_ITEM_COUNT,
    -- Statistiques globales
    SUM(AC) OVER () AS GLOBAL_TOTAL_AC,
    COUNT(*) OVER () AS GLOBAL_ITEM_COUNT,
    COUNT(DISTINCT TRAIN_NAME) OVER () AS TRAINS_COUNT
FROM ANALYSE_EXCES_GAZ_TORCHE
WHERE UNITE = ? AND MOIS = ?
ORDER BY TRAIN_NAME, AC DESC;

-- Requête pour les distributions
SELECT 
    ETAT,
    COUNT(*) as COUNT_ETAT,
    CLASSES,
    COUNT(*) as COUNT_CLASSES,
    TRAIN_NAME
FROM ANALYSE_EXCES_GAZ_TORCHE
WHERE UNITE = ? AND MOIS = ?
GROUP BY ETAT, CLASSES, TRAIN_NAME;
```

## Vue matérialisée (optionnel pour performance)

```sql
-- Vue matérialisée pour les statistiques pré-calculées
CREATE MATERIALIZED VIEW MV_ANALYSE_EXCES_GT_STATS
BUILD IMMEDIATE
REFRESH COMPLETE ON DEMAND
AS
SELECT 
    UNITE,
    MOIS,
    TRAIN_NAME,
    COUNT(*) AS ITEM_COUNT,
    SUM(AC) AS TOTAL_AC,
    AVG(AC) AS AVG_AC,
    MAX(AC) AS MAX_AC,
    MIN(AC) AS MIN_AC,
    COUNT(CASE WHEN CLASSES = 'Critique' THEN 1 END) AS CRITIQUE_COUNT,
    COUNT(CASE WHEN CLASSES = 'Majeur' THEN 1 END) AS MAJEUR_COUNT,
    COUNT(CASE WHEN CLASSES = 'Mineur' THEN 1 END) AS MINEUR_COUNT,
    COUNT(CASE WHEN ETAT = 'En cours' THEN 1 END) AS EN_COURS_COUNT,
    COUNT(CASE WHEN ETAT = 'Terminé' THEN 1 END) AS TERMINE_COUNT,
    COUNT(CASE WHEN ETAT = 'Planifié' THEN 1 END) AS PLANIFIE_COUNT
FROM ANALYSE_EXCES_GAZ_TORCHE
GROUP BY UNITE, MOIS, TRAIN_NAME;

-- Index sur la vue matérialisée
CREATE INDEX IDX_MV_ANALYSE_EXCES_GT_STATS ON MV_ANALYSE_EXCES_GT_STATS(UNITE, MOIS);
```
