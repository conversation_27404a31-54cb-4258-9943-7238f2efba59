import { NgModule,LOCALE_ID, APP_INITIALIZER  } from '@angular/core';
import { PathLocationStrategy, LocationStrategy, HashLocationStrategy } from '@angular/common';
import { AppComponent } from './app.component';
import { AppLayoutModule } from './layout/app.layout.module';
import { AppRoutingModule } from './app-routing.module';
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import {initKeycloak} from './init-keycloak';
import { KeycloakService, KeycloakAngularModule } from 'keycloak-angular';

registerLocaleData(localeFr);
@NgModule({
  declarations: [AppComponent],
  imports: [AppRoutingModule, AppLayoutModule, KeycloakAngularModule],
  providers: [
    { provide: LOCALE_ID, useValue: 'fr-FR' },
    {
      provide: APP_INITIALIZER,
      useFactory: initKeycloak,
      multi: true,
      deps: [KeycloakService],
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
