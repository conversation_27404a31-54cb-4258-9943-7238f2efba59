{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-sidebar.mjs"], "sourcesContent": ["import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"*\"];\nconst _c1 = (a0, a1, a2, a3, a4, a5) => ({\n  \"p-sidebar\": true,\n  \"p-sidebar-active\": a0,\n  \"p-sidebar-left\": a1,\n  \"p-sidebar-right\": a2,\n  \"p-sidebar-top\": a3,\n  \"p-sidebar-bottom\": a4,\n  \"p-sidebar-full\": a5\n});\nconst _c2 = (a0, a1) => ({\n  transform: a0,\n  transition: a1\n});\nconst _c3 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction Sidebar_div_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Sidebar_div_0_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headlessTemplate);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_button_2_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sidebar-close-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"closeicon\");\n  }\n}\nfunction Sidebar_div_0_ng_template_3_button_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Sidebar_div_0_ng_template_3_button_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Sidebar_div_0_ng_template_3_button_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Sidebar_div_0_ng_template_3_button_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtemplate(1, Sidebar_div_0_ng_template_3_button_2_span_2_1_Template, 1, 0, null, 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"data-pc-section\", \"closeicon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.closeIconTemplate);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function Sidebar_div_0_ng_template_3_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown.enter\", function Sidebar_div_0_ng_template_3_button_2_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    });\n    i0.ɵɵtemplate(1, Sidebar_div_0_ng_template_3_button_2_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 11)(2, Sidebar_div_0_ng_template_3_button_2_span_2_Template, 2, 2, \"span\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaCloseLabel)(\"data-pc-section\", \"closebutton\")(\"data-pc-group-section\", \"iconcontainer\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.closeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closeIconTemplate);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_ng_container_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵtemplate(2, Sidebar_div_0_ng_template_3_ng_container_6_ng_container_2_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"footer\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nfunction Sidebar_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, Sidebar_div_0_ng_template_3_ng_container_1_Template, 1, 0, \"ng-container\", 5)(2, Sidebar_div_0_ng_template_3_button_2_Template, 3, 5, \"button\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵprojection(4);\n    i0.ɵɵtemplate(5, Sidebar_div_0_ng_template_3_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, Sidebar_div_0_ng_template_3_ng_container_6_Template, 3, 2, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"header\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCloseIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.footerTemplate);\n  }\n}\nfunction Sidebar_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 0);\n    i0.ɵɵlistener(\"@panelState.start\", function Sidebar_div_0_Template_div_animation_panelState_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@panelState.done\", function Sidebar_div_0_Template_div_animation_panelState_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    })(\"keydown\", function Sidebar_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeyDown($event));\n    });\n    i0.ɵɵtemplate(2, Sidebar_div_0_ng_container_2_Template, 2, 1, \"ng-container\", 4)(3, Sidebar_div_0_ng_template_3_Template, 7, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notHeadless_r4 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction6(9, _c1, ctx_r1.visible, ctx_r1.position === \"left\" && !ctx_r1.fullScreen, ctx_r1.position === \"right\" && !ctx_r1.fullScreen, ctx_r1.position === \"top\" && !ctx_r1.fullScreen, ctx_r1.position === \"bottom\" && !ctx_r1.fullScreen, ctx_r1.fullScreen))(\"@panelState\", i0.ɵɵpureFunction1(19, _c3, i0.ɵɵpureFunction2(16, _c2, ctx_r1.transformOptions, ctx_r1.transitionOptions)))(\"ngStyle\", ctx_r1.style);\n    i0.ɵɵattribute(\"data-pc-name\", \"sidebar\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.headlessTemplate)(\"ngIfElse\", notHeadless_r4);\n  }\n}\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * Sidebar is a panel component displayed as an overlay at the edges of the screen.\n * @group Components\n */\nclass Sidebar {\n  document;\n  el;\n  renderer;\n  cd;\n  config;\n  /**\n   *  Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Whether to block scrolling of the document when sidebar is active.\n   * @group Props\n   */\n  blockScroll = false;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Aria label of the close icon.\n   * @group Props\n   */\n  ariaCloseLabel;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether an overlay mask is displayed behind the sidebar.\n   * @group Props\n   */\n  modal = true;\n  /**\n   * Whether to dismiss sidebar on click of the mask.\n   * @group Props\n   */\n  dismissible = true;\n  /**\n   * Whether to display the close icon.\n   * @group Props\n   */\n  showCloseIcon = true;\n  /**\n   * Specifies if pressing escape key should hide the sidebar.\n   * @group Props\n   */\n  closeOnEscape = true;\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Specifies the visibility of the dialog.\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(val) {\n    this._visible = val;\n  }\n  /**\n   * Specifies the position of the sidebar, valid values are \"left\", \"right\", \"bottom\" and \"top\".\n   * @group Props\n   */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    this._position = value;\n    switch (value) {\n      case 'left':\n        this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n        break;\n      case 'right':\n        this.transformOptions = 'translate3d(100%, 0px, 0px)';\n        break;\n      case 'bottom':\n        this.transformOptions = 'translate3d(0px, 100%, 0px)';\n        break;\n      case 'top':\n        this.transformOptions = 'translate3d(0px, -100%, 0px)';\n        break;\n    }\n  }\n  /**\n   * Adds a close icon to the header to hide the dialog.\n   * @group Props\n   */\n  get fullScreen() {\n    return this._fullScreen;\n  }\n  set fullScreen(value) {\n    this._fullScreen = value;\n    if (value) this.transformOptions = 'none';\n  }\n  templates;\n  /**\n   * Callback to invoke when dialog is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when dialog is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * Callback to invoke when dialog visibility is changed.\n   * @param {boolean} value - Visible value.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  initialized;\n  _visible;\n  _position = 'left';\n  _fullScreen = false;\n  container;\n  transformOptions = 'translate3d(-100%, 0px, 0px)';\n  mask;\n  maskClickListener;\n  documentEscapeListener;\n  animationEndListener;\n  contentTemplate;\n  headerTemplate;\n  footerTemplate;\n  closeIconTemplate;\n  headlessTemplate;\n  constructor(document, el, renderer, cd, config) {\n    this.document = document;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n  }\n  ngAfterViewInit() {\n    this.initialized = true;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n        case 'closeicon':\n          this.closeIconTemplate = item.template;\n          break;\n        case 'headless':\n          this.headlessTemplate = item.template;\n          break;\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  onKeyDown(event) {\n    if (event.code === 'Escape') {\n      this.hide(false);\n    }\n  }\n  show() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n    }\n    if (this.modal) {\n      this.enableModality();\n    }\n    this.onShow.emit({});\n    this.visibleChange.emit(true);\n  }\n  hide(emit = true) {\n    if (emit) {\n      this.onHide.emit({});\n    }\n    if (this.modal) {\n      this.disableModality();\n    }\n  }\n  close(event) {\n    this.hide(false);\n    this.visibleChange.emit(false);\n    event.preventDefault();\n  }\n  enableModality() {\n    const activeDrawers = this.document.querySelectorAll('.p-sidebar-active');\n    const activeDrawersLength = activeDrawers.length;\n    const zIndex = activeDrawersLength == 1 ? String(parseInt(this.container.style.zIndex) - 1) : String(parseInt(activeDrawers[0].style.zIndex) - 1);\n    if (!this.mask) {\n      this.mask = this.renderer.createElement('div');\n      this.renderer.setStyle(this.mask, 'zIndex', zIndex);\n      DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n      if (this.dismissible) {\n        this.maskClickListener = this.renderer.listen(this.mask, 'click', event => {\n          if (this.dismissible) {\n            this.close(event);\n          }\n        });\n      }\n      this.renderer.appendChild(this.document.body, this.mask);\n      if (this.blockScroll) {\n        DomHandler.blockBodyScroll();\n      }\n    }\n  }\n  disableModality() {\n    if (this.mask) {\n      DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n      this.animationEndListener = this.renderer.listen(this.mask, 'animationend', this.destroyModal.bind(this));\n    }\n  }\n  destroyModal() {\n    this.unbindMaskClickListener();\n    if (this.mask) {\n      this.renderer.removeChild(this.document.body, this.mask);\n    }\n    if (this.blockScroll) {\n      DomHandler.unblockBodyScroll();\n    }\n    this.unbindAnimationEndListener();\n    this.mask = null;\n  }\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.container = event.element;\n        this.appendContainer();\n        this.show();\n        if (this.closeOnEscape) {\n          this.bindDocumentEscapeListener();\n        }\n        break;\n    }\n  }\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.hide(true);\n        ZIndexUtils.clear(this.container);\n        this.unbindGlobalListeners();\n        break;\n    }\n  }\n  appendContainer() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n  bindDocumentEscapeListener() {\n    const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n    this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n      if (event.which == 27) {\n        if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container)) {\n          this.close(event);\n        }\n      }\n    });\n  }\n  unbindDocumentEscapeListener() {\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n      this.documentEscapeListener = null;\n    }\n  }\n  unbindMaskClickListener() {\n    if (this.maskClickListener) {\n      this.maskClickListener();\n      this.maskClickListener = null;\n    }\n  }\n  unbindGlobalListeners() {\n    this.unbindMaskClickListener();\n    this.unbindDocumentEscapeListener();\n  }\n  unbindAnimationEndListener() {\n    if (this.animationEndListener && this.mask) {\n      this.animationEndListener();\n      this.animationEndListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.initialized = false;\n    if (this.visible && this.modal) {\n      this.destroyModal();\n    }\n    if (this.appendTo && this.container) {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.container = null;\n    this.unbindGlobalListeners();\n    this.unbindAnimationEndListener();\n  }\n  static ɵfac = function Sidebar_Factory(t) {\n    return new (t || Sidebar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Sidebar,\n    selectors: [[\"p-sidebar\"]],\n    contentQueries: function Sidebar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      appendTo: \"appendTo\",\n      blockScroll: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"blockScroll\", \"blockScroll\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      ariaCloseLabel: \"ariaCloseLabel\",\n      autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      modal: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"modal\", \"modal\", booleanAttribute],\n      dismissible: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"dismissible\", \"dismissible\", booleanAttribute],\n      showCloseIcon: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showCloseIcon\", \"showCloseIcon\", booleanAttribute],\n      closeOnEscape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"closeOnEscape\", \"closeOnEscape\", booleanAttribute],\n      transitionOptions: \"transitionOptions\",\n      visible: \"visible\",\n      position: \"position\",\n      fullScreen: \"fullScreen\"\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\",\n      visibleChange: \"visibleChange\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 1,\n    consts: [[\"container\", \"\"], [\"notHeadless\", \"\"], [\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\", \"class\", \"keydown\", 4, \"ngIf\"], [\"role\", \"complementary\", 3, \"keydown\", \"ngClass\", \"ngStyle\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngTemplateOutlet\"], [1, \"p-sidebar-header\"], [\"type\", \"button\", \"class\", \"p-sidebar-close p-sidebar-icon p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-sidebar-content\"], [4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-sidebar-close\", \"p-sidebar-icon\", \"p-link\", 3, \"click\", \"keydown.enter\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-sidebar-close-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-sidebar-close-icon\"], [1, \"p-sidebar-footer\"]],\n    template: function Sidebar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Sidebar_div_0_Template, 5, 21, \"div\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.visible);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, TimesIcon],\n    styles: [\"@layer primeng{.p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;-webkit-transition:none;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Sidebar, [{\n    type: Component,\n    args: [{\n      selector: 'p-sidebar',\n      template: `\n        <div\n            #container\n            [ngClass]=\"{\n                'p-sidebar': true,\n                'p-sidebar-active': visible,\n                'p-sidebar-left': position === 'left' && !fullScreen,\n                'p-sidebar-right': position === 'right' && !fullScreen,\n                'p-sidebar-top': position === 'top' && !fullScreen,\n                'p-sidebar-bottom': position === 'bottom' && !fullScreen,\n                'p-sidebar-full': fullScreen\n            }\"\n            *ngIf=\"visible\"\n            [@panelState]=\"{ value: 'visible', params: { transform: transformOptions, transition: transitionOptions } }\"\n            (@panelState.start)=\"onAnimationStart($event)\"\n            (@panelState.done)=\"onAnimationEnd($event)\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            role=\"complementary\"\n            [attr.data-pc-name]=\"'sidebar'\"\n            [attr.data-pc-section]=\"'root'\"\n            (keydown)=\"onKeyDown($event)\"\n        >\n            <ng-container *ngIf=\"headlessTemplate; else notHeadless\">\n                <ng-container *ngTemplateOutlet=\"headlessTemplate\"></ng-container>\n            </ng-container>\n            <ng-template #notHeadless>\n                <div class=\"p-sidebar-header\" [attr.data-pc-section]=\"'header'\">\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <button\n                        type=\"button\"\n                        class=\"p-sidebar-close p-sidebar-icon p-link\"\n                        (click)=\"close($event)\"\n                        (keydown.enter)=\"close($event)\"\n                        [attr.aria-label]=\"ariaCloseLabel\"\n                        *ngIf=\"showCloseIcon\"\n                        pRipple\n                        [attr.data-pc-section]=\"'closebutton'\"\n                        [attr.data-pc-group-section]=\"'iconcontainer'\"\n                    >\n                        <TimesIcon *ngIf=\"!closeIconTemplate\" [styleClass]=\"'p-sidebar-close-icon'\" [attr.data-pc-section]=\"'closeicon'\" />\n                        <span *ngIf=\"closeIconTemplate\" class=\"p-sidebar-close-icon\" [attr.data-pc-section]=\"'closeicon'\">\n                            <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                </div>\n                <div class=\"p-sidebar-content\" [attr.data-pc-section]=\"'content'\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n                <ng-container *ngIf=\"footerTemplate\">\n                    <div class=\"p-sidebar-footer\" [attr.data-pc-section]=\"'footer'\">\n                        <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                    </div>\n                </ng-container>\n            </ng-template>\n        </div>\n    `,\n      animations: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto;flex-grow:1}.p-sidebar-header{display:flex;align-items:center}.p-sidebar-footer{margin-top:auto}.p-sidebar-icon{display:flex;align-items:center;justify-content:center;margin-left:auto}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;-webkit-transition:none;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.PrimeNGConfig\n  }], {\n    appendTo: [{\n      type: Input\n    }],\n    blockScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    ariaCloseLabel: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    modal: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    dismissible: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showCloseIcon: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    closeOnEscape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }]\n  });\n})();\nclass SidebarModule {\n  static ɵfac = function SidebarModule_Factory(t) {\n    return new (t || SidebarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SidebarModule,\n    declarations: [Sidebar],\n    imports: [CommonModule, RippleModule, SharedModule, TimesIcon],\n    exports: [Sidebar, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RippleModule, SharedModule, TimesIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SidebarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RippleModule, SharedModule, TimesIcon],\n      exports: [Sidebar, SharedModule],\n      declarations: [Sidebar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Sidebar, SidebarModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACvC,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,kBAAkB;AACpB;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,YAAY;AACd;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,gBAAgB,CAAC;AAC9F,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,gBAAgB;AAAA,EAC3D;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,sBAAsB;AAClD,IAAG,YAAY,mBAAmB,WAAW;AAAA,EAC/C;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AAAC;AACxF,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,aAAa;AAAA,EAC5G;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,MAAM,CAAC;AACtF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,WAAW;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,iBAAiB;AAAA,EAC5D;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,sEAAsE,QAAQ;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,MAAM,CAAC;AAAA,IAC5C,CAAC,EAAE,iBAAiB,SAAS,8EAA8E,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,MAAM,CAAC;AAAA,IAC5C,CAAC;AACD,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,aAAa,EAAE,EAAE,GAAG,sDAAsD,GAAG,GAAG,QAAQ,EAAE;AAC5K,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,cAAc,EAAE,mBAAmB,aAAa,EAAE,yBAAyB,eAAe;AAC9H,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,iBAAiB;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,iBAAiB;AAAA,EAChD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,gBAAgB,CAAC;AAC5G,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,YAAY,mBAAmB,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,cAAc;AAAA,EACzD;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,UAAU,CAAC;AAClK,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAC7F,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,QAAQ;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,cAAc;AACvD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa;AAC1C,IAAG,UAAU;AACb,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,eAAe;AACxD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc;AAAA,EAC7C;AACF;AACA,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,qBAAqB,SAAS,iEAAiE,QAAQ;AACnH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,oBAAoB,SAAS,gEAAgE,QAAQ;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,WAAW,SAAS,8CAA8C,QAAQ;AAC3E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACjL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAoB,YAAY,CAAC;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,SAAS,OAAO,aAAa,UAAU,CAAC,OAAO,YAAY,OAAO,aAAa,WAAW,CAAC,OAAO,YAAY,OAAO,aAAa,SAAS,CAAC,OAAO,YAAY,OAAO,aAAa,YAAY,CAAC,OAAO,YAAY,OAAO,UAAU,CAAC,EAAE,eAAkB,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,OAAO,kBAAkB,OAAO,iBAAiB,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK;AACrb,IAAG,YAAY,gBAAgB,SAAS,EAAE,mBAAmB,MAAM;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,gBAAgB,EAAE,YAAY,cAAc;AAAA,EAC3E;AACF;AACA,IAAM,gBAAgB,UAAU,CAAC,MAAM;AAAA,EACrC,WAAW;AAAA,EACX,SAAS;AACX,CAAC,GAAG,QAAQ,gBAAgB,CAAC,CAAC;AAC9B,IAAM,gBAAgB,UAAU,CAAC,QAAQ,kBAAkB,MAAM;AAAA,EAC/D,WAAW;AAAA,EACX,SAAS;AACX,CAAC,CAAC,CAAC,CAAC;AAKJ,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AACnB,QAAI;AAAO,WAAK,mBAAmB;AAAA,EACrC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,gBAAgB,IAAI,aAAa;AAAA,EACjC;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,cAAc;AAAA,EACd;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,IAAI,UAAU,IAAI,QAAQ;AAC9C,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,KAAK;AAC9B;AAAA,QACF,KAAK;AACH,eAAK,mBAAmB,KAAK;AAC7B;AAAA,QACF;AACE,eAAK,kBAAkB,KAAK;AAC5B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,OAAO;AACf,QAAI,MAAM,SAAS,UAAU;AAC3B,WAAK,KAAK,KAAK;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,KAAK,YAAY;AACnB,kBAAY,IAAI,SAAS,KAAK,WAAW,KAAK,cAAc,KAAK,OAAO,OAAO,KAAK;AAAA,IACtF;AACA,QAAI,KAAK,OAAO;AACd,WAAK,eAAe;AAAA,IACtB;AACA,SAAK,OAAO,KAAK,CAAC,CAAC;AACnB,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B;AAAA,EACA,KAAK,OAAO,MAAM;AAChB,QAAI,MAAM;AACR,WAAK,OAAO,KAAK,CAAC,CAAC;AAAA,IACrB;AACA,QAAI,KAAK,OAAO;AACd,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,SAAK,KAAK,KAAK;AACf,SAAK,cAAc,KAAK,KAAK;AAC7B,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,iBAAiB;AACf,UAAM,gBAAgB,KAAK,SAAS,iBAAiB,mBAAmB;AACxE,UAAM,sBAAsB,cAAc;AAC1C,UAAM,SAAS,uBAAuB,IAAI,OAAO,SAAS,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,IAAI,OAAO,SAAS,cAAc,CAAC,EAAE,MAAM,MAAM,IAAI,CAAC;AAChJ,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,OAAO,KAAK,SAAS,cAAc,KAAK;AAC7C,WAAK,SAAS,SAAS,KAAK,MAAM,UAAU,MAAM;AAClD,iBAAW,mBAAmB,KAAK,MAAM,kFAAkF;AAC3H,UAAI,KAAK,aAAa;AACpB,aAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,MAAM,SAAS,WAAS;AACzE,cAAI,KAAK,aAAa;AACpB,iBAAK,MAAM,KAAK;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,IAAI;AACvD,UAAI,KAAK,aAAa;AACpB,mBAAW,gBAAgB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,MAAM;AACb,iBAAW,SAAS,KAAK,MAAM,2BAA2B;AAC1D,WAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,MAAM,gBAAgB,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,IAC1G;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,wBAAwB;AAC7B,QAAI,KAAK,MAAM;AACb,WAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,IAAI;AAAA,IACzD;AACA,QAAI,KAAK,aAAa;AACpB,iBAAW,kBAAkB;AAAA,IAC/B;AACA,SAAK,2BAA2B;AAChC,SAAK,OAAO;AAAA,EACd;AAAA,EACA,iBAAiB,OAAO;AACtB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,YAAY,MAAM;AACvB,aAAK,gBAAgB;AACrB,aAAK,KAAK;AACV,YAAI,KAAK,eAAe;AACtB,eAAK,2BAA2B;AAAA,QAClC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,aAAK,KAAK,IAAI;AACd,oBAAY,MAAM,KAAK,SAAS;AAChC,aAAK,sBAAsB;AAC3B;AAAA,IACJ;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,aAAa;AAAQ,aAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,SAAS;AAAA;AAAO,mBAAW,YAAY,KAAK,WAAW,KAAK,QAAQ;AAAA,IACvJ;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,UAAM,iBAAiB,KAAK,KAAK,KAAK,GAAG,cAAc,gBAAgB,KAAK;AAC5E,SAAK,yBAAyB,KAAK,SAAS,OAAO,gBAAgB,WAAW,WAAS;AACrF,UAAI,MAAM,SAAS,IAAI;AACrB,YAAI,SAAS,KAAK,UAAU,MAAM,MAAM,MAAM,YAAY,IAAI,KAAK,SAAS,GAAG;AAC7E,eAAK,MAAM,KAAK;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAC5B,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,SAAK,wBAAwB;AAC7B,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,wBAAwB,KAAK,MAAM;AAC1C,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc;AACnB,QAAI,KAAK,WAAW,KAAK,OAAO;AAC9B,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,KAAK,YAAY,KAAK,WAAW;AACnC,WAAK,SAAS,YAAY,KAAK,GAAG,eAAe,KAAK,SAAS;AAAA,IACjE;AACA,QAAI,KAAK,aAAa,KAAK,YAAY;AACrC,kBAAY,MAAM,KAAK,SAAS;AAAA,IAClC;AACA,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAC3B,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,GAAG;AACxC,WAAO,KAAK,KAAK,UAAY,kBAAkB,QAAQ,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,aAAa,CAAC;AAAA,EACvN;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,aAAa,CAAI,WAAa,4BAA4B,eAAe,eAAe,gBAAgB;AAAA,MACxG,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,gBAAgB;AAAA,MACrG,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,eAAe;AAAA,MACpG,OAAO,CAAI,WAAa,4BAA4B,SAAS,SAAS,gBAAgB;AAAA,MACtF,aAAa,CAAI,WAAa,4BAA4B,eAAe,eAAe,gBAAgB;AAAA,MACxG,eAAe,CAAI,WAAa,4BAA4B,iBAAiB,iBAAiB,gBAAgB;AAAA,MAC9G,eAAe,CAAI,WAAa,4BAA4B,iBAAiB,iBAAiB,gBAAgB;AAAA,MAC9G,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,QAAQ,iBAAiB,GAAG,WAAW,WAAW,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,iBAAiB,GAAG,WAAW,WAAW,SAAS,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,QAAQ,UAAU,SAAS,yCAAyC,WAAW,IAAI,GAAG,SAAS,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,WAAW,IAAI,GAAG,mBAAmB,kBAAkB,UAAU,GAAG,SAAS,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,SAAS,wBAAwB,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,IACvrB,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,wBAAwB,GAAG,IAAI,OAAO,CAAC;AAAA,MAC1D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,OAAO;AAAA,MACnC;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,MAAS,kBAAqB,SAAY,QAAQ,SAAS;AAAA,IAC/F,QAAQ,CAAC,iuCAAiuC;AAAA,IAC1uC,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,cAAc,CAAC,WAAW,mBAAmB,CAAC,aAAa,aAAa,CAAC,CAAC,GAAG,WAAW,mBAAmB,CAAC,aAAa,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACjK;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0DV,YAAY,CAAC,QAAQ,cAAc,CAAC,WAAW,mBAAmB,CAAC,aAAa,aAAa,CAAC,CAAC,GAAG,WAAW,mBAAmB,CAAC,aAAa,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MAChK,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,iuCAAiuC;AAAA,IAC5uC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAe;AAAA,EAClC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,OAAO;AAAA,IACtB,SAAS,CAAC,cAAc,cAAc,cAAc,SAAS;AAAA,IAC7D,SAAS,CAAC,SAAS,YAAY;AAAA,EACjC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,cAAc,WAAW,YAAY;AAAA,EAC7E,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,cAAc,SAAS;AAAA,MAC7D,SAAS,CAAC,SAAS,YAAY;AAAA,MAC/B,cAAc,CAAC,OAAO;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}