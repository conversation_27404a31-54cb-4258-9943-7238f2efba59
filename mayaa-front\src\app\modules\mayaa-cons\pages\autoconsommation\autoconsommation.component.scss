.autoconsommation-container {
  padding: 1rem;
  background-color: #f8fafc;
  min-height: 100vh;

  .autoconsommation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;

    .autoconsommation-title {
      font-size: 2rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }

    .autoconsommation-info {
      .last-update {
        color: #64748b;
        font-size: 0.875rem;
      }
    }
  }

  .loading-container,
  .error-container {
    margin-bottom: 2rem;
  }

  .autoconsommation-content {
    .stats-card {
      height: 100%;
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &.autoconsommation-card {
        .card-mini-header {
          padding: 0.75rem 1rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 8px 8px 0 0;

          h4 {
            margin: 0;
            font-size: 0.875rem;
            font-weight: 600;
          }
        }

        .stats-content-vertical {
          padding: 1.5rem 1rem;
          text-align: center;

          .main-value {
            margin-bottom: 1rem;

            .value-number {
              display: block;
              font-size: 2rem;
              font-weight: 700;
              color: #1e293b;
              line-height: 1;
            }

            .value-unit {
              font-size: 0.75rem;
              color: #64748b;
              font-weight: 500;
            }
          }

          .percentage-value {
            .percentage {
              display: block;
              font-size: 1.25rem;
              font-weight: 600;
              margin-bottom: 0.25rem;
            }

            .percentage-label {
              font-size: 0.75rem;
              color: #64748b;
            }
          }
        }
      }
    }

    .card-header {
      padding: 1rem;
      border-bottom: 1px solid #e2e8f0;

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.25rem 0;
      }

      .card-subtitle {
        color: #64748b;
        font-size: 0.875rem;
      }
    }

    .chart-container {
      padding: 1rem;
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .totals-summary {
      padding: 1rem;
      background-color: #f8fafc;
      border-radius: 8px;
      margin: 1rem;

      .total-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e2e8f0;

        &:last-child {
          border-bottom: none;
        }

        .total-label {
          font-weight: 500;
          color: #475569;
        }

        .total-value {
          font-weight: 600;
          font-size: 1rem;
        }
      }
    }

    .unite-info {
      strong {
        color: #1e293b;
        font-size: 0.875rem;
      }

      small {
        font-size: 0.75rem;
      }
    }
  }
}

// Classes utilitaires pour les couleurs
.text-green-600 {
  color: #059669 !important;
}

.text-red-600 {
  color: #dc2626 !important;
}

.text-orange-500 {
  color: #f97316 !important;
}

.text-orange-600 {
  color: #ea580c !important;
}

.text-blue-600 {
  color: #2563eb !important;
}

.text-purple-600 {
  color: #9333ea !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

.bg-green-100 {
  background-color: #dcfce7 !important;
}

.bg-red-100 {
  background-color: #fee2e2 !important;
}

.bg-orange-100 {
  background-color: #ffedd5 !important;
}

.bg-blue-100 {
  background-color: #dbeafe !important;
}

// Responsive design
@media (max-width: 768px) {
  .autoconsommation-container {
    padding: 0.5rem;

    .autoconsommation-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .autoconsommation-title {
        font-size: 1.5rem;
      }
    }

    .stats-card {
      &.autoconsommation-card {
        .stats-content-vertical {
          padding: 1rem 0.75rem;

          .main-value {
            .value-number {
              font-size: 1.5rem;
            }
          }
        }
      }
    }

    .chart-container {
      min-height: 250px;
      padding: 0.5rem;
    }
  }
}

// Améliorations pour les petits écrans
@media (max-width: 576px) {
  .autoconsommation-container {
    .autoconsommation-header {
      .autoconsommation-title {
        font-size: 1.25rem;
      }
    }

    .stats-card {
      &.autoconsommation-card {
        .stats-content-vertical {
          padding: 0.75rem 0.5rem;

          .main-value {
            .value-number {
              font-size: 1.25rem;
            }
          }
        }
      }
    }

    .chart-container {
      min-height: 200px;
      padding: 0.25rem;
    }
  }

  :host ::ng-deep {
    .p-datatable {
      font-size: 0.75rem;

      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 0.5rem;
      }
    }
  }
}
