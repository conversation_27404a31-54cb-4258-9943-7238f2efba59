import { computed, Injectable, signal } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { KeycloakProfile } from 'keycloak-js';
import { interval, takeWhile } from 'rxjs';
import { RUserService } from '../ruser.service';
import { KUser } from '../../model/KUser';
import { RUser } from '../../model/RUser';
import { KeycloakAuthService } from './keycloak-auth.service';
import { UserActivityService } from './user-activity.service';
import { UserPermissionService } from './user-permission.service';

@Injectable({ providedIn: 'root' })
export class UserProfileService {
  // Signal pour l'utilisateur connecté
  private connectedUserSignal = signal<RUser>({});
  public keyCloakUser: KUser = new KUser();
  // Signal en lecture seule pour l'accès externe
  public readonly connectedUser = computed(() => this.connectedUserSignal());

  constructor(
    private keycloak: KeycloakService,
    private ruserService: RUserService,
    private keycloakAuth: KeycloakAuthService,
    private userActivity: UserActivityService,
    private userPermission: UserPermissionService
  ) {}

  public async initSession(): Promise<void> {
    if (!this.keycloakAuth.isLoggedIn) return;
    await this.initUserProfile();

    // Vérification périodique de l'activité
    interval(30000)
      .pipe(takeWhile(() => this.keycloakAuth.isLoggedIn))
      .subscribe(() => {
        if (!this.userActivity.isUserActive()) {
          this.keycloakAuth.logout();
        }
      });
  }

  public async initUserProfile(): Promise<void> {
    const profile = await this.keycloak.loadUserProfile();
    const tokenParsed = this.keycloak.getKeycloakInstance().tokenParsed;

    if (!profile || !tokenParsed) {
      throw new Error('Unable to load user profile');
    }

    this.keyCloakUser = this.mapProfileToUser(profile, tokenParsed);
    await this.userPermission.initUserPermissions(tokenParsed);
    await this.syncUserWithDB(this.keyCloakUser);
  }

  private mapProfileToUser(profile: KeycloakProfile, tokenParsed: any): KUser {
    const user = new KUser();
    user.kid = profile.id;
    user.username = profile.username;
    user.nomComplet = `${profile.firstName} ${profile.lastName}`;
    user.groups = tokenParsed['unites'];
    user.departement = tokenParsed['Departement'];
    user.intitule = tokenParsed['intitule'];
    return user;
  }

  private async syncUserWithDB(user: KUser): Promise<void> {
    if (!user.kid) return;

    const tokenName = user.kid;
    if (!localStorage.getItem(tokenName)) {
      this.ruserService.addKuserToDB().subscribe((res) => {
        localStorage.setItem(tokenName, 'true');
        this.connectedUserSignal.set(res);
      });
    } else {
      this.ruserService.getUserByKId(user.kid).subscribe((user) => {
        this.connectedUserSignal.set(user);
      });
    }
  }
}
