/**
 * Interface pour les données de consommation énergétique
 * Correspond à la vue CONSOMMATION_ENERGETIQUE
 */
export interface ConsommationEnergetique {
  /** Identifiant unique */
  id: number;

  /** Code de l'unité */
  unite: string;

  /** Mo<PERSON> de référence (format ISO date string) */
  mois: string;

  /** Consommation électricité Sonelgaz */
  ceElectriciteSonalgaz: number;

  /** Consommation électricité Kahrama */
  ceKahrama: number;
}

/**
 * Interface pour les données de consommation énergétique formatées pour l'affichage
 */
export interface ConsommationEnergetiqueDisplay {
  /** Mois (nom du mois) */
  mois: string;

  /** Année */
  annee: string;

  /** Données de consommation */
  data: ConsommationEnergetiqueItem[];
}

/**
 * Interface pour un élément de consommation énergétique
 */
export interface ConsommationEnergetiqueItem {
  /** Libellé de la consommation (ex: "CE Sonelgaz", "CE Kahrama") */
  label: string;

  /** Valeur de consommation */
  valeur: number;

  /** Pourcentage par rapport au total (optionnel) */
  pourcentage?: number;

  /** Type de source */
  sourceType: 'externe';

  /** Catégorie */
  category: 'CE';
}
