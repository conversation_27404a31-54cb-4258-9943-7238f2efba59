import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AnalyseExcesComponent } from './analyse-exces.component';
import { AnalyseExcesService } from '../../services/analyse-exces.service';
import { AnalyseExcesUtilite, AnalyseExcesTrain } from '../../../../model/analyse-exces.model';

// Modules PrimeNG nécessaires pour les tests
import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { MessageModule } from 'primeng/message';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

describe('AnalyseExcesComponent', () => {
  let component: AnalyseExcesComponent;
  let fixture: ComponentFixture<AnalyseExcesComponent>;
  let mockAnalyseExcesService: jasmine.SpyObj<AnalyseExcesService>;

  // Données mock pour les tests
  const mockUtilitesData: AnalyseExcesUtilite[] = [
    {
      codeAc: 'UT001',
      problemeSpecifique: 'Test problème utilité',
      intitule: 'Test utilité',
      ac: 150.5,
      classeCauses: 'Technique',
      causes: 'Test cause',
      actions: 'Test action',
      classes: 'Critique',
      etat: 'En cours',
      numero: '001'
    }
  ];

  const mockTrainsData: AnalyseExcesTrain[] = [
    {
      codeAc: 'TR001',
      problemeSpecifique: 'Test problème train',
      intitule: 'Test train',
      ac: 245.8,
      classeCauses: 'Technique',
      causes: 'Test cause train',
      actions: 'Test action train',
      classes: 'Critique',
      etat: 'En cours',
      numero: '001',
      trainName: 'Train 100'
    }
  ];

  beforeEach(async () => {
    // Création du spy pour le service
    const spy = jasmine.createSpyObj('AnalyseExcesService', [
      'getAnalyseExcesData',
      'getUtilitesData',
      'getTrainsData'
    ]);

    await TestBed.configureTestingModule({
      declarations: [AnalyseExcesComponent],
      imports: [
        TabViewModule,
        TableModule,
        MessageModule,
        ProgressSpinnerModule
      ],
      providers: [
        { provide: AnalyseExcesService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AnalyseExcesComponent);
    component = fixture.componentInstance;
    mockAnalyseExcesService = TestBed.inject(AnalyseExcesService) as jasmine.SpyObj<AnalyseExcesService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.utilitesData).toEqual([]);
    expect(component.trainsData).toEqual([]);
    expect(component.loading).toBeFalse();
    expect(component.currentUnite).toBe('GL1K');
    expect(component.currentMois).toBe('2024-01');
  });

  it('should load data on init', () => {
    // Configuration du mock
    mockAnalyseExcesService.getAnalyseExcesData.and.returnValue(of({
      utilites: mockUtilitesData,
      trains: mockTrainsData
    }));

    // Appel de ngOnInit
    component.ngOnInit();

    // Vérifications
    expect(mockAnalyseExcesService.getAnalyseExcesData).toHaveBeenCalledWith('GL1K', '2024-01');
    expect(component.utilitesData).toEqual(mockUtilitesData);
    expect(component.trainsData).toEqual(mockTrainsData);
    expect(component.loading).toBeFalse();
  });

  it('should handle loading state correctly', () => {
    // Configuration du mock avec délai
    mockAnalyseExcesService.getAnalyseExcesData.and.returnValue(of({
      utilites: mockUtilitesData,
      trains: mockTrainsData
    }));

    // Vérification de l'état initial
    expect(component.loading).toBeFalse();

    // Déclenchement du chargement
    component.loadData();

    // Vérification que loading est activé pendant le chargement
    expect(component.loading).toBeTrue();
  });

  it('should get unique trains correctly', () => {
    component.trainsData = [
      { ...mockTrainsData[0], trainName: 'Train 100' },
      { ...mockTrainsData[0], trainName: 'Train 200' },
      { ...mockTrainsData[0], trainName: 'Train 100' } // Doublon
    ];

    const uniqueTrains = component.getUniqueTrains();

    expect(uniqueTrains).toEqual(['Train 100', 'Train 200']);
    expect(uniqueTrains.length).toBe(2);
  });

  it('should filter train data by train name', () => {
    component.trainsData = [
      { ...mockTrainsData[0], trainName: 'Train 100' },
      { ...mockTrainsData[0], trainName: 'Train 200' },
      { ...mockTrainsData[0], trainName: 'Train 100' }
    ];

    const train100Data = component.getTrainData('Train 100');
    const train200Data = component.getTrainData('Train 200');

    expect(train100Data.length).toBe(2);
    expect(train200Data.length).toBe(1);
    expect(train100Data.every(item => item.trainName === 'Train 100')).toBeTrue();
    expect(train200Data.every(item => item.trainName === 'Train 200')).toBeTrue();
  });

  it('should refresh data when parameters change', () => {
    mockAnalyseExcesService.getAnalyseExcesData.and.returnValue(of({
      utilites: mockUtilitesData,
      trains: mockTrainsData
    }));

    component.refreshData('GL2K', '2024-02');

    expect(component.currentUnite).toBe('GL2K');
    expect(component.currentMois).toBe('2024-02');
    expect(mockAnalyseExcesService.getAnalyseExcesData).toHaveBeenCalledWith('GL2K', '2024-02');
  });

  it('should load utilites data separately', () => {
    mockAnalyseExcesService.getUtilitesData.and.returnValue(of(mockUtilitesData));

    component.loadUtilitesData();

    expect(mockAnalyseExcesService.getUtilitesData).toHaveBeenCalledWith('GL1K', '2024-01');
    expect(component.utilitesData).toEqual(mockUtilitesData);
  });

  it('should load trains data separately', () => {
    mockAnalyseExcesService.getTrainsData.and.returnValue(of(mockTrainsData));

    component.loadTrainsData();

    expect(mockAnalyseExcesService.getTrainsData).toHaveBeenCalledWith('GL1K', '2024-01');
    expect(component.trainsData).toEqual(mockTrainsData);
  });

  it('should handle tab change event', () => {
    spyOn(console, 'log');
    const mockEvent = { index: 1 };

    component.onTabChange(mockEvent);

    expect(console.log).toHaveBeenCalledWith('Onglet changé:', 1);
  });

  it('should have correct column configurations', () => {
    expect(component.utilitesColumns.length).toBe(10);
    expect(component.trainsColumns.length).toBe(10);

    // Vérification de quelques colonnes importantes
    const codeAcColumn = component.utilitesColumns.find(col => col.field === 'codeAc');
    expect(codeAcColumn).toBeDefined();
    expect(codeAcColumn?.header).toBe('CODE AC');
    expect(codeAcColumn?.sortable).toBeTrue();

    const acColumn = component.utilitesColumns.find(col => col.field === 'ac');
    expect(acColumn).toBeDefined();
    expect(acColumn?.header).toBe('AC (10³ CM³ GN)');
    expect(acColumn?.sortable).toBeTrue();
  });
});
