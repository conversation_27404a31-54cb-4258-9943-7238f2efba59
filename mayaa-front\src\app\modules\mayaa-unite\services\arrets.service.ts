import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import {
  SyntheseArrets,
  RepartitionArretsParSiege,
  RepartitionArretsParCause,
  SituationTrainsArrets,
  ReponseArrets,
  StatistiquesArrets,
  FiltreArrets
} from '../../../model/arrets.interface';

@Injectable({
  providedIn: 'root'
})
export class ArretsService {
  private readonly baseUrl = 'http://localhost:8889/api';

  constructor(private http: HttpClient) {}

  /**
   * Récupère la synthèse des arrêts pour une unité et un mois donnés
   */
  getSyntheseArrets(unite: string, mois: string): Observable<SyntheseArrets[]> {
    const url = `${this.baseUrl}/synthese-arrets/${unite}/${mois}`;
    return this.http.get<SyntheseArrets[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la synthèse des arrêts:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la synthèse des arrêts pour un train spécifique
   */
  getSyntheseArretsParTrain(unite: string, mois: string, train: string): Observable<SyntheseArrets[]> {
    const url = `${this.baseUrl}/synthese-arrets/${unite}/${mois}/${train}`;
    return this.http.get<SyntheseArrets[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la synthèse des arrêts par train:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les trains disponibles pour une unité et un mois donnés
   */
  getTrainsDisponibles(unite: string, mois: string): Observable<string[]> {
    const url = `${this.baseUrl}/synthese-arrets/${unite}/${mois}/trains`;
    return this.http.get<string[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des trains:', error);
        return of(['COMPLEXE']);
      })
    );
  }

  /**
   * Récupère la répartition des arrêts par siège
   */
  getRepartitionParSiege(unite: string, mois: string): Observable<RepartitionArretsParSiege[]> {
    const url = `${this.baseUrl}/repartition-arrets-siege/${unite}/${mois}`;
    return this.http.get<RepartitionArretsParSiege[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la répartition par siège:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la répartition des arrêts par siège pour un type spécifique
   */
  getRepartitionParSiegeParType(unite: string, mois: string, type: 'INTERNE' | 'EXTERNE'): Observable<RepartitionArretsParSiege[]> {
    const url = `${this.baseUrl}/repartition-arrets-siege/${unite}/${mois}/type/${type}`;
    return this.http.get<RepartitionArretsParSiege[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la répartition par siège par type:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les données pour les graphiques en secteurs (sièges)
   */
  getDonneesPieChartSiege(unite: string, mois: string, type: 'INTERNE' | 'EXTERNE'): Observable<RepartitionArretsParSiege[]> {
    const url = `${this.baseUrl}/repartition-arrets-siege/${unite}/${mois}/pie-chart/${type}`;
    return this.http.get<RepartitionArretsParSiege[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des données pie chart siège:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la répartition des arrêts par cause
   */
  getRepartitionParCause(unite: string, mois: string): Observable<RepartitionArretsParCause[]> {
    const url = `${this.baseUrl}/repartition-arrets-cause/${unite}/${mois}`;
    return this.http.get<RepartitionArretsParCause[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la répartition par cause:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la répartition des arrêts par cause pour un type spécifique
   */
  getRepartitionParCauseParType(unite: string, mois: string, type: 'INTERNE' | 'EXTERNE' | 'CLASSE'): Observable<RepartitionArretsParCause[]> {
    const url = `${this.baseUrl}/repartition-arrets-cause/${unite}/${mois}/type/${type}`;
    return this.http.get<RepartitionArretsParCause[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la répartition par cause par type:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les données pour les graphiques en secteurs (causes)
   */
  getDonneesPieChartCause(unite: string, mois: string, type: 'INTERNE' | 'EXTERNE' | 'CLASSE'): Observable<RepartitionArretsParCause[]> {
    const url = `${this.baseUrl}/repartition-arrets-cause/${unite}/${mois}/pie-chart/${type}`;
    return this.http.get<RepartitionArretsParCause[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des données pie chart cause:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la situation des trains
   */
  getSituationTrains(unite: string, mois: string): Observable<SituationTrainsArrets[]> {
    const url = `${this.baseUrl}/situation-trains-arrets/${unite}/${mois}`;
    return this.http.get<SituationTrainsArrets[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la situation des trains:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les causes internes pour la situation des trains
   */
  getCausesInternes(unite: string, mois: string): Observable<SituationTrainsArrets> {
    const url = `${this.baseUrl}/situation-trains-arrets/${unite}/${mois}/internes`;
    return this.http.get<SituationTrainsArrets>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des causes internes:', error);
        return of({} as SituationTrainsArrets);
      })
    );
  }

  /**
   * Récupère les causes externes pour la situation des trains
   */
  getCausesExternes(unite: string, mois: string): Observable<SituationTrainsArrets> {
    const url = `${this.baseUrl}/situation-trains-arrets/${unite}/${mois}/externes`;
    return this.http.get<SituationTrainsArrets>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des causes externes:', error);
        return of({} as SituationTrainsArrets);
      })
    );
  }

  /**
   * Récupère l'analyse du complexe
   */
  getAnalyseComplexe(unite: string, mois: string): Observable<SituationTrainsArrets> {
    const url = `${this.baseUrl}/situation-trains-arrets/${unite}/${mois}/complexe`;
    return this.http.get<SituationTrainsArrets>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de l\'analyse du complexe:', error);
        return of({} as SituationTrainsArrets);
      })
    );
  }

  /**
   * Récupère le nombre total d'arrêts
   */
  getTotalArrets(unite: string, mois: string): Observable<number> {
    const url = `${this.baseUrl}/situation-trains-arrets/${unite}/${mois}/total-arrets`;
    return this.http.get<number>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération du total des arrêts:', error);
        return of(0);
      })
    );
  }

  /**
   * Récupère les sièges disponibles
   */
  getSiegesDisponibles(unite: string, mois: string): Observable<string[]> {
    const url = `${this.baseUrl}/repartition-arrets-siege/${unite}/${mois}/sieges`;
    return this.http.get<string[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des sièges:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les causes disponibles
   */
  getCausesDisponibles(unite: string, mois: string): Observable<string[]> {
    const url = `${this.baseUrl}/repartition-arrets-cause/${unite}/${mois}/causes`;
    return this.http.get<string[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des causes:', error);
        return of([]);
      })
    );
  }

  /**
   * Formate une date au format attendu par l'API (ddMMyyyy)
   */
  private formatDateForApi(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  /**
   * Convertit une date au format API vers un objet Date
   */
  private parseApiDate(dateStr: string): Date {
    const day = parseInt(dateStr.substring(0, 2));
    const month = parseInt(dateStr.substring(2, 4)) - 1;
    const year = parseInt(dateStr.substring(4, 8));
    return new Date(year, month, day);
  }
}
