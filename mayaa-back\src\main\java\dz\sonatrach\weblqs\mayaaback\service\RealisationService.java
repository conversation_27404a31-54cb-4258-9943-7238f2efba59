package dz.sonatrach.weblqs.mayaaback.service;

import dz.sonatrach.weblqs.mayaaback.dto.RealisationResponse;
import dz.sonatrach.weblqs.mayaaback.dto.RealisationResponse.*;
import dz.sonatrach.weblqs.mayaaback.model.IndicateurPeformanceUnite;
import dz.sonatrach.weblqs.mayaaback.repo.IndicateurPeformanceUniteRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service pour les données de réalisation
 */
@Service
public class RealisationService {

    @Autowired
    private IndicateurPeformanceUniteRepository indicateurPerformanceRepository;

    private static final List<String> UNITES_PRINCIPALES = Arrays.asList("5X1", "5X2", "5X3", "5X8");

    /**
     * Récupère toutes les données de réalisation consolidées
     */
    public RealisationResponse getDonneesRealisation(LocalDate pmois) {
        String pmoisStr = pmois.format(DateTimeFormatter.ofPattern("ddMMyyyy"));
        RealisationResponse response = new RealisationResponse(pmoisStr);

        try {
            System.out.println("=== SERVICE REALISATION - DONNEES CONSOLIDEES ===");
            System.out.println("Période: " + pmoisStr);

            // Récupération des données
            response.setStatistiques(getStatistiquesGenerales(pmois));
            response.setUnites(getProductionParUnite(pmois));
            response.setTotaux(getTotauxRealisation(pmois));
            response.setEvolution(getEvolutionProduction(pmois, 6));

            System.out.println("Données de réalisation consolidées avec succès");
            return response;

        } catch (Exception e) {
            System.err.println("Erreur lors de la consolidation des données de réalisation: " + e.getMessage());
            e.printStackTrace();
            return response; // Retourne une réponse vide plutôt que null
        }
    }

    /**
     * Récupère les statistiques générales
     */
    public StatistiquesGeneralesDto getStatistiquesGenerales(LocalDate pmois) {
        try {
            System.out.println("=== CALCUL STATISTIQUES GENERALES ===");

            // Calcul basé sur les données réelles des indicateurs de performance
            List<IndicateurPeformanceUnite> indicateurs = indicateurPerformanceRepository.findByMoisOrderByUnite(pmois);

            int nombreUnitesActives = 0;
            int nombreUnitesArretTotal = 0;
            int nombreUnitesMaintenance = 0;

            for (IndicateurPeformanceUnite indicateur : indicateurs) {
                if (UNITES_PRINCIPALES.contains(indicateur.getUnite())) {
                    String statut = determinerStatutUnite(indicateur);
                    switch (statut) {
                        case "ACTIF":
                            nombreUnitesActives++;
                            break;
                        case "ARRET_TOTAL":
                            nombreUnitesArretTotal++;
                            break;
                        case "MAINTENANCE":
                            nombreUnitesMaintenance++;
                            break;
                    }
                }
            }

            int nombreTotalUnites = nombreUnitesActives + nombreUnitesArretTotal + nombreUnitesMaintenance;
            
            StatistiquesGeneralesDto statistiques = new StatistiquesGeneralesDto(
                nombreUnitesActives, nombreUnitesArretTotal, nombreUnitesMaintenance, nombreTotalUnites
            );
            
            System.out.println("Statistiques calculées: " + nombreUnitesActives + " actives, " + 
                             nombreUnitesArretTotal + " arrêtées, " + nombreUnitesMaintenance + " maintenance");
            
            return statistiques;
            
        } catch (Exception e) {
            System.err.println("Erreur lors du calcul des statistiques générales: " + e.getMessage());
            e.printStackTrace();
            return new StatistiquesGeneralesDto(0, 0, 0, 0);
        }
    }

    /**
     * Récupère les données de production par unité
     */
    public List<RealisationUniteDto> getProductionParUnite(LocalDate pmois) {
        try {
            System.out.println("=== DONNEES PRODUCTION PAR UNITE ===");

            List<IndicateurPeformanceUnite> indicateurs = indicateurPerformanceRepository.findByMoisOrderByUnite(pmois);
            List<RealisationUniteDto> unites = new ArrayList<>();
            
            for (IndicateurPeformanceUnite indicateur : indicateurs) {
                if (UNITES_PRINCIPALES.contains(indicateur.getUnite())) {
                    RealisationUniteDto unite = new RealisationUniteDto();
                    
                    unite.setUniteCode(indicateur.getUnite());
                    unite.setUnite("Unité " + indicateur.getUnite());
                    unite.setStatut(determinerStatutUnite(indicateur));
                    
                    // Production et objectifs
                    unite.setProductionReelle(indicateur.getProduction() != null ? indicateur.getProduction().doubleValue() : 0.0);
                    unite.setObjectifProduction(indicateur.getProductionDesign() != null ? indicateur.getProductionDesign().doubleValue() : 0.0);
                    unite.setPrevisionProduction(unite.getProductionReelle() * 1.02); // Estimation
                    
                    // Calculs des écarts et taux
                    if (unite.getObjectifProduction() > 0) {
                        unite.setTauxRealisationObjectif((unite.getProductionReelle() / unite.getObjectifProduction()) * 100);
                        unite.setEcartObjectif(unite.getTauxRealisationObjectif() - 100);
                    } else {
                        unite.setTauxRealisationObjectif(0.0);
                        unite.setEcartObjectif(0.0);
                    }
                    
                    // Calculs GN basés sur des ratios réalistes
                    // Ratio typique : 1 tonne GNL ≈ 1.4-1.5 tonnes GN
                    double ratioGnGnl = 1.45; // Ratio réaliste GN/GNL
                    double rendementTransformation = 0.85; // Rendement de transformation 85%

                    // GN reçu = Production GNL * ratio + pertes
                    unite.setGnRecu(unite.getProductionReelle() * ratioGnGnl / rendementTransformation);

                    // GN transformé = GN reçu * rendement
                    unite.setGnTransforme(unite.getGnRecu() * rendementTransformation);

                    if (unite.getGnRecu() > 0) {
                        unite.setPourcentageGnTransforme((unite.getGnTransforme() / unite.getGnRecu()) * 100);
                    } else {
                        unite.setPourcentageGnTransforme(0.0);
                    }
                    
                    unites.add(unite);
                }
            }
            
            System.out.println("Données de production récupérées pour " + unites.size() + " unités");
            return unites;
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération de la production par unité: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Récupère les totaux de réalisation
     */
    public TotauxRealisationDto getTotauxRealisation(LocalDate pmois) {
        try {
            System.out.println("=== CALCUL TOTAUX REALISATION ===");
            
            List<RealisationUniteDto> unites = getProductionParUnite(pmois);
            TotauxRealisationDto totaux = new TotauxRealisationDto();
            
            double totalProductionReelle = 0.0;
            double totalObjectif = 0.0;
            double totalPrevision = 0.0;
            double totalGnRecu = 0.0;
            double totalGnTransforme = 0.0;
            
            for (RealisationUniteDto unite : unites) {
                totalProductionReelle += unite.getProductionReelle() != null ? unite.getProductionReelle() : 0.0;
                totalObjectif += unite.getObjectifProduction() != null ? unite.getObjectifProduction() : 0.0;
                totalPrevision += unite.getPrevisionProduction() != null ? unite.getPrevisionProduction() : 0.0;
                totalGnRecu += unite.getGnRecu() != null ? unite.getGnRecu() : 0.0;
                totalGnTransforme += unite.getGnTransforme() != null ? unite.getGnTransforme() : 0.0;
            }
            
            totaux.setTotalProductionReelle(totalProductionReelle);
            totaux.setTotalObjectif(totalObjectif);
            totaux.setTotalPrevision(totalPrevision);
            totaux.setTotalGnRecu(totalGnRecu);
            totaux.setTotalGnTransforme(totalGnTransforme);
            
            // Calculs des pourcentages
            if (totalObjectif > 0) {
                totaux.setTauxGlobalRealisationObjectif((totalProductionReelle / totalObjectif) * 100);
            } else {
                totaux.setTauxGlobalRealisationObjectif(0.0);
            }
            
            if (totalGnRecu > 0) {
                totaux.setPourcentageGlobalGnTransforme((totalGnTransforme / totalGnRecu) * 100);
            } else {
                totaux.setPourcentageGlobalGnTransforme(0.0);
            }
            
            System.out.println("Totaux calculés - Production: " + totalProductionReelle + 
                             ", Objectif: " + totalObjectif + 
                             ", Taux: " + totaux.getTauxGlobalRealisationObjectif() + "%");
            
            return totaux;
            
        } catch (Exception e) {
            System.err.println("Erreur lors du calcul des totaux de réalisation: " + e.getMessage());
            e.printStackTrace();
            return new TotauxRealisationDto();
        }
    }

    /**
     * Récupère l'évolution mensuelle de la production
     */
    public List<EvolutionProductionDto> getEvolutionProduction(LocalDate pmois, Integer nombreMois) {
        try {
            System.out.println("=== EVOLUTION PRODUCTION ===");
            System.out.println("Période de fin: " + pmois + ", Nombre de mois: " + nombreMois);
            
            List<EvolutionProductionDto> evolution = new ArrayList<>();
            
            // Génération de données d'évolution (à remplacer par de vraies données)
            for (int i = nombreMois - 1; i >= 0; i--) {
                LocalDate moisCourant = pmois.minusMonths(i);
                String moisStr = moisCourant.format(DateTimeFormatter.ofPattern("MMM yyyy"));
                
                // Simulation de données - à remplacer par de vraies requêtes
                double production = 85000 + (Math.random() * 15000); // Entre 85k et 100k
                double objectif = 95000 + (Math.random() * 10000);   // Entre 95k et 105k
                double tauxRealisation = (production / objectif) * 100;
                
                evolution.add(new EvolutionProductionDto(moisStr, production, objectif, tauxRealisation));
            }
            
            System.out.println("Évolution générée pour " + evolution.size() + " mois");
            return evolution;
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la génération de l'évolution: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Détermine le statut d'une unité basé sur ses indicateurs de performance
     */
    private String determinerStatutUnite(IndicateurPeformanceUnite indicateur) {
        // Logique basée sur la production et les trains en marche
        boolean aProduction = indicateur.getProduction() != null && indicateur.getProduction().doubleValue() > 0;
        boolean aTrainsEnMarche = indicateur.getTrainMarche() != null && indicateur.getTrainMarche().doubleValue() > 0;

        if (aProduction && aTrainsEnMarche) {
            return "ACTIF";
        } else if (!aTrainsEnMarche) {
            return "ARRET_TOTAL";
        } else {
            return "MAINTENANCE"; // Trains en marche mais pas de production
        }
    }
}
