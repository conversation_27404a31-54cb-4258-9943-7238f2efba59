.indicateurs-performance-container {
  padding: 1rem;

  .card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .card-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          font-size: 1.5rem;
        }
      }
    }

    .card-content {
      padding: 1.5rem;
    }
  }

  // Loading state
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;

    .loading-text {
      margin-top: 1rem;
      color: #6b7280;
      font-size: 0.875rem;
    }
  }

  // Error state
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;

    .error-icon {
      font-size: 3rem;
      color: #ef4444;
      margin-bottom: 1rem;
    }

    .error-text {
      color: #6b7280;
      margin-bottom: 1rem;
    }
  }

  // Content styles
  .content-container {
    .table-container {
      margin-bottom: 3rem;
    }
  }

  // Table styles
  .table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;

    ::ng-deep .modern-table {
      .p-datatable-header {
        background: #f9fafb;
        border-bottom: 2px solid #e5e7eb;
      }

      .p-datatable-thead > tr > th {
        background: #f8fafc;
        color: #374151;
        font-weight: 600;
        font-size: 0.875rem;
        padding: 1rem 0.75rem;
        border-bottom: 2px solid #e5e7eb;
        border-right: 1px solid #e5e7eb;

        &.performance-header {
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          color: white;
        }

        &.consumption-header {
          background: linear-gradient(135deg, #10b981, #047857);
          color: white;
        }

        &.row-header {
          background: #1f2937;
          color: white;
          width: 80px;
        }
      }

      .p-datatable-tbody > tr {
        transition: all 0.2s ease;

        &:hover {
          background-color: #f8fafc;
        }

        &.design-row {
          background-color: #fef3c7;

          &:hover {
            background-color: #fde68a;
          }
        }

        &.reelle-row {
          background-color: #dbeafe;

          &:hover {
            background-color: #bfdbfe;
          }
        }

        > td {
          padding: 0.875rem 0.75rem;
          border-bottom: 1px solid #e5e7eb;
          border-right: 1px solid #e5e7eb;
          font-size: 0.875rem;

          &.row-label {
            background: #1f2937;
            color: white;
            font-weight: 600;
            text-align: center;

            .row-type-badge {
              display: inline-block;
              padding: 0.25rem 0.75rem;
              border-radius: 9999px;
              font-size: 0.75rem;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.05em;

              &.design {
                background: #fbbf24;
                color: #92400e;
              }

              &.reelle {
                background: #60a5fa;
                color: #1e40af;
              }
            }
          }

          &.number-cell {
            font-family: 'Courier New', monospace;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .indicateurs-performance-container {
    padding: 0.5rem;

    .card {
      .card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;

        .card-title {
          font-size: 1rem;
          text-align: center;
        }
      }

      .card-content {
        padding: 1rem;
      }
    }

    .table-container {
      ::ng-deep .modern-table {
        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
          padding: 0.5rem 0.25rem;
          font-size: 0.75rem;
        }
      }
    }

    // Styles responsive pour le graphique
    .bilan-chart-container {
      margin-top: 2rem;
      padding: 1rem;

      .chart-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;

        .chart-title {
          font-size: 1rem;
        }
      }

      .gn-summary .summary-card {
        min-width: auto;
        width: 100%;

        .summary-icon i {
          font-size: 2rem;
        }

        .summary-info .summary-value {
          font-size: 1.25rem;
        }
      }

      .chart-section {
        .chart-wrapper {
          padding: 1rem;

          ::ng-deep p-chart {
            width: 100% !important;
            height: 300px !important;
          }
        }

        .chart-data-table {
          ::ng-deep .p-datatable {
            .p-datatable-thead > tr > th,
            .p-datatable-tbody > tr > td {
              padding: 0.5rem 0.25rem;
              font-size: 0.75rem;
            }
          }
        }
      }
    }

    // Styles responsive pour les graphiques côte à côte
    .charts-section {
      .charts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .chart-item {
          .chart-item-title {
            font-size: 0.875rem;
          }

          .chart-wrapper {
            padding: 1rem;

            ::ng-deep p-chart {
              width: 100% !important;
              height: 250px !important;
            }
          }
        }
      }

      .chart-data-table {
        ::ng-deep .p-datatable {
          .p-datatable-tbody > tr {
            &.sub-row td {
              padding-left: 1rem;
              font-size: 0.7rem;
            }
          }
        }
      }
    }

    // Styles responsive pour le tableau des chaudières
    .chaudieres-table-container {
      margin-top: 2rem;
      padding: 1rem;

      .table-header .table-title {
        font-size: 1rem;
        text-align: center;
      }

      .chaudieres-table {
        ::ng-deep .chaudieres-table-style {
          .p-datatable-thead > tr > th,
          .p-datatable-tbody > tr > td,
          .p-datatable-tfoot > tr > td {
            padding: 0.5rem 0.25rem;
            font-size: 0.75rem;
          }

          .chaudieres-badge {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
          }
        }
      }
    }
  }
}

// Styles pour le graphique Bilan GN-GNL
.bilan-chart-container {
  margin-top: 3rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;

    .chart-title {
      margin: 0;
      color: #1f2937;
      font-size: 1.25rem;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        color: #10b981;
        font-size: 1.5rem;
      }
    }

    .chart-actions {
      display: flex;
      align-items: center;
    }
  }

  .gn-summary {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;

    .summary-card {
      display: flex;
      align-items: center;
      padding: 1.5rem;
      border-radius: 12px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      min-width: 250px;

      .summary-icon {
        margin-right: 1rem;

        i {
          font-size: 2.5rem;
          opacity: 0.8;
        }
      }

      .summary-info {
        h5 {
          margin: 0 0 0.5rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          opacity: 0.9;
        }

        .summary-value {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 700;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }

  .charts-section {
    animation: fadeIn 0.3s ease-in-out;

    .charts-subtitle {
      text-align: center;
      margin-bottom: 2rem;

      p {
        margin: 0;
        color: #6b7280;
        font-size: 0.875rem;
        font-style: italic;
      }
    }

    .charts-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;

      .chart-item {
        .chart-item-title {
          text-align: center;
          margin: 0 0 1rem 0;
          color: #374151;
          font-size: 1rem;
          font-weight: 600;
        }

        .chart-wrapper {
          display: flex;
          justify-content: center;
          padding: 1.5rem;
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          ::ng-deep .p-chart {
            .p-chart-canvas {
              border-radius: 8px;
            }
          }
        }
      }
    }

    .chart-data-table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e5e7eb;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      ::ng-deep .p-datatable {
        .p-datatable-thead > tr > th {
          background: #f8fafc;
          color: #374151;
          font-weight: 600;
          font-size: 0.875rem;
          padding: 1rem 0.75rem;
          border-bottom: 2px solid #e5e7eb;
        }

        .p-datatable-tbody > tr {
          transition: all 0.2s ease;

          &:hover {
            background-color: #f8fafc;
          }

          &.main-row {
            background-color: #f9fafb;
            font-weight: 600;
          }

          &.sub-row {
            background-color: #fefefe;

            td {
              padding-left: 2rem;
              font-size: 0.8rem;
              color: #6b7280;
            }

            &:hover {
              background-color: #f3f4f6;
            }
          }

          > td {
            padding: 0.875rem 0.75rem;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.875rem;

            &.number-cell {
              font-family: 'Courier New', monospace;
              font-weight: 500;
            }

            &.sub-item {
              position: relative;

              .sub-indicator {
                color: #9ca3af;
                margin-right: 0.5rem;
                font-family: monospace;
              }
            }

            .color-indicator {
              display: inline-block;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              margin-right: 0.5rem;
              vertical-align: middle;
            }
          }
        }
      }
    }
  }
}

// Styles pour le tableau des chaudières
.chaudieres-table-container {
  margin-top: 3rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;

  .table-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;

    .table-title {
      margin: 0;
      color: #1f2937;
      font-size: 1.25rem;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        color: #f59e0b;
        font-size: 1.5rem;
      }
    }
  }

  .chaudieres-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    ::ng-deep .chaudieres-table-style {
      .p-datatable-thead > tr > th {
        background: #f8fafc;
        color: #374151;
        font-weight: 600;
        font-size: 0.875rem;
        padding: 1rem 0.75rem;
        border-bottom: 2px solid #e5e7eb;
        border-right: 1px solid #e5e7eb;

        &.chaudieres-header {
          background: linear-gradient(135deg, #f59e0b, #d97706);
          color: white;
          font-weight: 700;
        }

        &.arret-header {
          background: linear-gradient(135deg, #ef4444, #dc2626);
          color: white;
          font-weight: 600;
        }
      }

      .p-datatable-tbody > tr {
        transition: all 0.2s ease;

        &:hover {
          background-color: #f8fafc;
        }

        &.chaudieres-row > td {
          padding: 1rem 0.75rem;
          border-bottom: 1px solid #e5e7eb;
          border-right: 1px solid #e5e7eb;
          font-size: 0.875rem;

          &.number-cell {
            font-family: 'Courier New', monospace;
            font-weight: 500;
          }

          &.chaudieres-name {
            background: #f9fafb;
            font-weight: 600;
          }
        }
      }

      .p-datatable-tfoot > tr.totals-row > td {
        background: linear-gradient(135deg, #1f2937, #374151);
        color: white;
        font-weight: 700;
        padding: 1rem 0.75rem;
        border-right: 1px solid #4b5563;
        font-size: 0.875rem;

        &.number-cell {
          font-family: 'Courier New', monospace;
        }
      }
    }

    .chaudieres-badge {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.abb {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
      }

      &.ihi {
        background: linear-gradient(135deg, #10b981, #047857);
        color: white;
      }
    }
  }
}

// Animation pour la transition entre graphiques
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
