/**
 * Interface pour les données d'évolution unifiée des causes de gaz torché par train
 */
export interface EvolutionCausesTrain {
  id: number;
  unite: string;
  codeTrain: string;
  mois: string; // Format ISO date string
  siegeCause: string | null;
  classeCause: string | null;
  quantiteGazTorchee: number;
}

/**
 * Type pour différencier les modes d'affichage
 */
export type CauseDisplayMode = 'sieges' | 'classes';

/**
 * Interface pour les données mensuelles organisées par train et cause
 */
export interface EvolutionCauseData {
  train: string;
  cause: string;
  mode: CauseDisplayMode;
  quantitesMensuelles: { [mois: string]: number }; // mois (1-12) -> quantité
  totalAnnuel: number;
}

/**
 * Interface pour les données d'un train spécifique
 */
export interface TrainCauseEvolutionData {
  codeTrain: string;
  causes: CauseItemEvolutionData[];
  totalAnnuel: number;
}

/**
 * Interface pour les données d'une cause spécifique
 */
export interface CauseItemEvolutionData {
  libelleCause: string;
  mode: CauseDisplayMode;
  quantitesMensuelles: MonthlyQuantity[];
  totalAnnuel: number;
}

/**
 * Interface pour les quantités mensuelles
 */
export interface MonthlyQuantity {
  mois: number; // 1-12
  nomMois: string; // J, F, M, A, M, J, J, A, S, O, N, D
  quantite: number;
}

/**
 * Interface pour la réponse complète de l'API
 */
export interface EvolutionCausesResponse {
  trains: TrainCauseEvolutionData[];
  annee: number;
  unite: string;
  mode: CauseDisplayMode;
  totalGlobal: number;
}

/**
 * Interface pour les paramètres de requête
 */
export interface EvolutionCausesParams {
  unite: string;
  annee: number;
  train?: string; // Optionnel, "COMPLEXE" par défaut
  mode: CauseDisplayMode;
}

/**
 * Interface pour les colonnes du tableau d'évolution
 */
export interface EvolutionCausesTableColumn {
  field: string;
  header: string;
  width?: string;
  sortable?: boolean;
  type?: 'text' | 'number';
}

/**
 * Interface pour les données formatées du tableau
 */
export interface EvolutionCausesTableRow {
  cause: string;
  mode: CauseDisplayMode;
  janvier?: number;
  fevrier?: number;
  mars?: number;
  avril?: number;
  mai?: number;
  juin?: number;
  juillet?: number;
  aout?: number;
  septembre?: number;
  octobre?: number;
  novembre?: number;
  decembre?: number;
  annee?: number;
}

/**
 * Énumération pour les mois
 */
export enum MoisAnnee {
  JANVIER = 1,
  FEVRIER = 2,
  MARS = 3,
  AVRIL = 4,
  MAI = 5,
  JUIN = 6,
  JUILLET = 7,
  AOUT = 8,
  SEPTEMBRE = 9,
  OCTOBRE = 10,
  NOVEMBRE = 11,
  DECEMBRE = 12
}

/**
 * Mapping des noms de mois courts
 */
export const MOIS_LABELS: { [key: number]: string } = {
  1: 'J',
  2: 'F',
  3: 'M',
  4: 'A',
  5: 'M',
  6: 'J',
  7: 'J',
  8: 'A',
  9: 'S',
  10: 'O',
  11: 'N',
  12: 'D'
};

/**
 * Mapping des noms complets de mois
 */
export const MOIS_COMPLETS: { [key: number]: string } = {
  1: 'Janvier',
  2: 'Février',
  3: 'Mars',
  4: 'Avril',
  5: 'Mai',
  6: 'Juin',
  7: 'Juillet',
  8: 'Août',
  9: 'Septembre',
  10: 'Octobre',
  11: 'Novembre',
  12: 'Décembre'
};

/**
 * Interface pour les statistiques d'un train
 */
export interface TrainCauseStatistics {
  codeTrain: string;
  nombreCauses: number;
  totalQuantite: number;
  pourcentageTotal: number;
}

/**
 * Interface pour les statistiques globales
 */
export interface GlobalCauseStatistics {
  nombreTrains: number;
  nombreCausesTotal: number;
  totalGlobal: number;
  repartitionParTrain: TrainCauseStatistics[];
  moisLePlusActif: {
    mois: number;
    nomMois: string;
    total: number;
  };
  mode: CauseDisplayMode;
}

/**
 * Interface pour les options du switch button
 */
export interface CauseModeOption {
  label: string;
  value: CauseDisplayMode;
  icon: string;
}

/**
 * Options disponibles pour le switch button
 */
export const CAUSE_MODE_OPTIONS: CauseModeOption[] = [
  {
    label: 'Sièges de Causes',
    value: 'sieges',
    icon: 'pi pi-sitemap'
  },
  {
    label: 'Classes de Causes',
    value: 'classes',
    icon: 'pi pi-tags'
  }
];

/**
 * Interface pour les options de train
 */
export interface TrainOption {
  label: string;
  value: string;
  isComplexe: boolean;
}

/**
 * Fonction utilitaire pour créer les options de train
 */
export function createTrainOptions(trains: string[]): TrainOption[] {
  return trains.map(train => ({
    label: train === 'COMPLEXE' ? 'Complexe (Ensemble de l\'unité)' : `Train ${train}`,
    value: train,
    isComplexe: train === 'COMPLEXE'
  }));
}
