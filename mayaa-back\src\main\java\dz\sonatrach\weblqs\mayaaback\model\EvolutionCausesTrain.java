package dz.sonatrach.weblqs.mayaaback.model;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Entité pour la vue EVOLUTION_CAUSES_TRAIN
 * Représente l'évolution unifiée des sièges et classes de causes de gaz torché par train
 */
@Entity
@Table(name = "EVOLUTION_CAUSES_TRAIN")
public class EvolutionCausesTrain implements Serializable {
    
    @Id
    @Column(name = "ID")
    private Long id;
    
    @Column(name = "UNITE", length = 10)
    private String unite;
    
    @Column(name = "CODE_TRAIN", length = 10)
    private String codeTrain;
    
    @Column(name = "MOIS")
    private LocalDate mois;
    
    @Column(name = "SIEGE_CAUSE", length = 120)
    private String siegeCause;
    
    @Column(name = "CLASSE_CAUSE", length = 90)
    private String classeCause;
    
    @Column(name = "QUANTITE_GAZ_TORCHEE")
    private BigDecimal quantiteGazTorchee;

    // Constructeurs
    public EvolutionCausesTrain() {}

    public EvolutionCausesTrain(Long id, String unite, String codeTrain, LocalDate mois,
                               String siegeCause, String classeCause, BigDecimal quantiteGazTorchee) {
        this.id = id;
        this.unite = unite;
        this.codeTrain = codeTrain;
        this.mois = mois;
        this.siegeCause = siegeCause;
        this.classeCause = classeCause;
        this.quantiteGazTorchee = quantiteGazTorchee;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public String getCodeTrain() {
        return codeTrain;
    }

    public void setCodeTrain(String codeTrain) {
        this.codeTrain = codeTrain;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public String getSiegeCause() {
        return siegeCause;
    }

    public void setSiegeCause(String siegeCause) {
        this.siegeCause = siegeCause;
    }

    public String getClasseCause() {
        return classeCause;
    }

    public void setClasseCause(String classeCause) {
        this.classeCause = classeCause;
    }

    public BigDecimal getQuantiteGazTorchee() {
        return quantiteGazTorchee;
    }

    public void setQuantiteGazTorchee(BigDecimal quantiteGazTorchee) {
        this.quantiteGazTorchee = quantiteGazTorchee;
    }

    @Override
    public String toString() {
        return "EvolutionCausesTrain{" +
                "id=" + id +
                ", unite='" + unite + '\'' +
                ", codeTrain='" + codeTrain + '\'' +
                ", mois=" + mois +
                ", siegeCause='" + siegeCause + '\'' +
                ", classeCause='" + classeCause + '\'' +
                ", quantiteGazTorchee=" + quantiteGazTorchee +
                '}';
    }
}
