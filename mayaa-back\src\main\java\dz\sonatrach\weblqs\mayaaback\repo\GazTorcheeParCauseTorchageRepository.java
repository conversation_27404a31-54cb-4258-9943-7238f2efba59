package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.GazTorcheeParCauseTorchage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface GazTorcheeParCauseTorchageRepository extends JpaRepository<GazTorcheeParCauseTorchage, Long> {
    List<GazTorcheeParCauseTorchage> findByUniteAndPmois(String unite, LocalDate pmois);
    List<GazTorcheeParCauseTorchage> findByPmois(LocalDate pmois);

    /**
     * Trouve les données de gaz torché pour un mois et plusieurs unités données (optimisé).
     * @param pmois Le mois de référence
     * @param unites Liste des codes d'unités
     * @return Liste des données de gaz torché
     */
    List<GazTorcheeParCauseTorchage> findByPmoisAndUniteIn(LocalDate pmois, List<String> unites);
}
