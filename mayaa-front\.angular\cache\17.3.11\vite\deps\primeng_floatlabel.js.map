{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-floatlabel.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { RouterModule } from '@angular/router';\n\n/**\n * FloatLabel appears on top of the input field when focused.\n * @group Components\n */\nconst _c0 = [\"*\"];\nclass FloatLabel {\n  static ɵfac = function FloatLabel_Factory(t) {\n    return new (t || FloatLabel)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: FloatLabel,\n    selectors: [[\"p-floatLabel\"]],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"p-float-label\"]],\n    template: function FloatLabel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabel, [{\n    type: Component,\n    args: [{\n      selector: 'p-floatLabel',\n      template: `\n        <span class=\"p-float-label\">\n            <ng-content></ng-content>\n        </span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, null);\n})();\nclass FloatLabelModule {\n  static ɵfac = function FloatLabelModule_Factory(t) {\n    return new (t || FloatLabelModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FloatLabelModule,\n    declarations: [FloatLabel],\n    imports: [CommonModule, SharedModule, RouterModule],\n    exports: [FloatLabel, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, RouterModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabelModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RouterModule],\n      exports: [FloatLabel, SharedModule],\n      declarations: [FloatLabel]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FloatLabel, FloatLabelModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,GAAG;AAC3C,WAAO,KAAK,KAAK,aAAY;AAAA,EAC/B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC;AAAA,IAC7B,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,GAAG;AACjD,WAAO,KAAK,KAAK,mBAAkB;AAAA,EACrC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,UAAU;AAAA,IACzB,SAAS,CAAC,cAAc,cAAc,YAAY;AAAA,IAClD,SAAS,CAAC,YAAY,YAAY;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,cAAc,YAAY;AAAA,EAClE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,YAAY;AAAA,MAClD,SAAS,CAAC,YAAY,YAAY;AAAA,MAClC,cAAc,CAAC,UAAU;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}