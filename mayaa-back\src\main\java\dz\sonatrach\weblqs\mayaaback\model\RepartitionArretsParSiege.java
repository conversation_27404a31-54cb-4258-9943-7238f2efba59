package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Entité pour la répartition des arrêts par siège (internes/externes)
 * Représente les données pour les graphiques en secteurs de répartition par siège
 */
@Entity
@Table(name = "REPARTITION_ARRETS_PAR_SIEGE")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class RepartitionArretsParSiege implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE", length = 10)
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "MOIS")
    @JsonView(View.basic.class)
    private LocalDate mois;

    @Column(name = "SIEGE_CAUSE", length = 120)
    @JsonView(View.basic.class)
    private String siegeCause;

    @Column(name = "TYPE_SIEGE", length = 20)
    @JsonView(View.basic.class)
    private String typeSiege; // "INTERNE" ou "EXTERNE"

    @Column(name = "QUANTITE_ARRETS")
    @JsonView(View.basic.class)
    private BigDecimal quantiteArrets;

    @Column(name = "POURCENTAGE")
    @JsonView(View.basic.class)
    private BigDecimal pourcentage;

    @Column(name = "NUMERO_SIEGE")
    @JsonView(View.basic.class)
    private Integer numeroSiege; // 1, 2, 3, 4, 5, 6

    // Constructeurs
    public RepartitionArretsParSiege() {}

    public RepartitionArretsParSiege(String unite, LocalDate mois, String siegeCause, String typeSiege) {
        this.unite = unite;
        this.mois = mois;
        this.siegeCause = siegeCause;
        this.typeSiege = typeSiege;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public String getSiegeCause() {
        return siegeCause;
    }

    public void setSiegeCause(String siegeCause) {
        this.siegeCause = siegeCause;
    }

    public String getTypeSiege() {
        return typeSiege;
    }

    public void setTypeSiege(String typeSiege) {
        this.typeSiege = typeSiege;
    }

    public BigDecimal getQuantiteArrets() {
        return quantiteArrets;
    }

    public void setQuantiteArrets(BigDecimal quantiteArrets) {
        this.quantiteArrets = quantiteArrets;
    }

    public BigDecimal getPourcentage() {
        return pourcentage;
    }

    public void setPourcentage(BigDecimal pourcentage) {
        this.pourcentage = pourcentage;
    }

    public Integer getNumeroSiege() {
        return numeroSiege;
    }

    public void setNumeroSiege(Integer numeroSiege) {
        this.numeroSiege = numeroSiege;
    }
}
