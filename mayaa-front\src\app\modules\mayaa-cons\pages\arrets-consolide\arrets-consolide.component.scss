// Styles pour le composant arrêts consolidé

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0.5rem;
  color: white;
}

.stat-card {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  backdrop-filter: blur(10px);

  .stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &.total {
    background: rgba(255, 255, 255, 0.15);
  }

  &.heures {
    background: rgba(255, 255, 255, 0.12);
  }

  &.map {
    background: rgba(255, 255, 255, 0.18);
  }

  &.unites {
    background: rgba(255, 255, 255, 0.14);
  }
}

.chart-container {
  height: 350px;
  margin-bottom: 1rem;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.badge-interne {
  background-color: #dcfce7;
  color: #166534;
}

.badge-externe {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-default {
  background-color: #f3f4f6;
  color: #374151;
}

.text-center {
  text-align: center;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

:host ::ng-deep {
  .p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.5rem;
  }

  .p-datatable .p-datatable-thead > tr > th {
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    text-align: center;
  }

  .p-card .p-card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #cbd5e1;
    font-weight: 600;
  }

  .p-chart {
    width: 100%;
    height: 100%;
  }

  // Styles pour les graphiques en secteurs
  .p-chart canvas {
    max-height: 300px;
  }

  // Animation de chargement
  .p-progressspinner {
    width: 50px;
    height: 50px;
  }

  // Responsive design
  @media (max-width: 768px) {
    .stats-overview {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.5rem;
      padding: 0.5rem;
    }

    .stat-card {
      padding: 0.75rem;

      .stat-value {
        font-size: 1.5rem;
      }

      .stat-label {
        font-size: 0.75rem;
      }
    }

    .chart-container {
      height: 250px;
    }
  }

  // Amélioration de l'accessibilité
  .p-datatable-tbody > tr:hover {
    background-color: #f1f5f9;
  }

  .badge:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}
