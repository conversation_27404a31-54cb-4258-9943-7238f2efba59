/**
 * Interface pour la synthèse consolidée des arrêts
 */
export interface SyntheseArretsConsolide {
  unite: string;
  nomUnite: string;
  totalArrets: number;
  totalHeuresArret: number;
  pourcentageInternes: number;
  pourcentageExternes: number;
  mapMoyenne: number;
  acNetteMoyenne: number;
  gtTotalInterne: number;
  gtTotalExterne: number;
  trainLePlusAffecte: string;
  causePrincipale: string;
}

/**
 * Interface pour la répartition consolidée par siège
 */
export interface RepartitionSiegeConsolide {
  siegeCause: string;
  typeSiege: 'INTERNE' | 'EXTERNE';
  totalQuantite: number;
  pourcentageGlobal: number;
  unites: {
    unite: string;
    quantite: number;
    pourcentage: number;
  }[];
}

/**
 * Interface pour la répartition consolidée par cause
 */
export interface RepartitionCauseConsolide {
  cause: string;
  typeCause: 'INTERNE' | 'EXTERNE' | 'CLASSE';
  totalQuantite: number;
  pourcentageGlobal: number;
  unites: {
    unite: string;
    quantite: number;
    pourcentage: number;
  }[];
}

/**
 * Interface pour la situation consolidée des trains
 */
export interface SituationTrainsConsolide {
  typeCause: 'INTERNES' | 'EXTERNES';
  causesPrincipalesGlobales: string[];
  analysesConsolidees: string;
  totalArrets: number;
  unitesAffectees: {
    unite: string;
    nombreArrets: number;
    causesPrincipales: string[];
    analyses: string;
  }[];
  tendances: {
    causesRecurrentes: string[];
    unitesLePlusAffectees: string[];
    evolutionMensuelle: {
      mois: string;
      totalArrets: number;
    }[];
  };
}

/**
 * Interface pour les statistiques globales consolidées
 */
export interface StatistiquesGlobalesArrets {
  totalArretsComplexe: number;
  totalHeuresArretComplexe: number;
  moyennePourcentageInternes: number;
  moyennePourcentageExternes: number;
  uniteAvecLePlusDArretsInternes: string;
  uniteAvecLePlusDArretsExternes: string;
  causeLaPlusRecurrente: string;
  siegeLePlusAffecte: string;
  tendanceEvolution: 'HAUSSE' | 'BAISSE' | 'STABLE';
  comparaisonMoisPrecedent: {
    variationTotalArrets: number;
    variationHeuresArret: number;
    variationPourcentageInternes: number;
    variationPourcentageExternes: number;
  };
}

/**
 * Interface pour la réponse consolidée complète
 */
export interface ReponseArretsConsolide {
  synthese: SyntheseArretsConsolide[];
  repartitionSiege: RepartitionSiegeConsolide[];
  repartitionCause: RepartitionCauseConsolide[];
  situationTrains: SituationTrainsConsolide[];
  statistiquesGlobales: StatistiquesGlobalesArrets;
  periode: string;
  unites: string[];
  derniereMAJ: string;
}
