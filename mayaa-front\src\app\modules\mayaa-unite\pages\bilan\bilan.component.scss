::ng-deep .p-progressbar-danger .p-progressbar-value {
    background-color: red !important;
  }
  ::ng-deep .p-progressbar-good .p-progressbar-value {
    background-color: green !important;
  }

  ::ng-deep .p-progressbar-warning .p-progressbar-value {
    background-color: orange !important;
  }

.card {
  height: 250px;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;

  :deep(canvas) {
    height: 200px !important;
    width: 100% !important;
  }
}

// Style pour les graphiques pour qu'ils s'adaptent à la hauteur de la carte
:deep(.p-chart) {
  flex: 1;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Ajustements spécifiques pour les titres des graphiques
:deep(.p-chart) {
  .p-chart-title {
    margin-bottom: 0.5rem;
  }
}

:host {
  display: block;
  padding: 1rem;
}

.spinner {
  margin: 2rem auto;
  display: block;
}

.error-message {
  margin: 1rem 0;
}

.summary-card {
  margin-bottom: 1.5rem;

  .header-title {
    font-size: 1.25rem;
    font-weight: bold;
    text-align: center;
  }

  .p-grid {
    margin: 1rem 0;
  }

  .summary-cell {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    padding: 1rem;
    text-align: center;

    h5 {
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    .value {
      font-size: 1.5rem;
      font-weight: bold;
    }
    .percent {
      color: #007ad9;
      margin-top: 0.25rem;
      font-size: 1rem;
    }
    small {
      display: block;
      margin-top: 0.5rem;
      color: #666;
    }
  }
}
.losses-accordion {
  margin-top: 1.5rem;
}

.loss-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
}

.loss-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.loss-label {
  font-weight: 600;
  min-width: 180px;
}

.loss-value {
  flex: 1;
}

