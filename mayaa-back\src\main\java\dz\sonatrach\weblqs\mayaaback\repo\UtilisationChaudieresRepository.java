package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.UtilisationChaudieres;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface UtilisationChaudieresRepository extends JpaRepository<UtilisationChaudieres, Long> {
    List<UtilisationChaudieres> findByUniteAndMois(String unite, LocalDate mois);
    List<UtilisationChaudieres> findByMois(LocalDate mois);
    List<UtilisationChaudieres> findByUnite(String unite);
}
