.evolution-causes-container {
  padding: 1rem;
  
  .page-header {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      flex-wrap: wrap;
      gap: 1rem;
      
      .page-title {
        margin: 0;
        color: #2c3e50;
        font-size: 1.5rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        i {
          color: #3498db;
        }
      }
      
      .header-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
        
        // Style pour le switch button
        ::ng-deep p-selectButton {
          .p-selectbutton {
            .p-button {
              padding: 0.5rem 1rem;
              border-radius: 6px;
              
              &.p-highlight {
                background: #3498db;
                border-color: #3498db;
              }
              
              i {
                margin-right: 0.5rem;
              }
            }
          }
        }
        
        // Style pour le dropdown des trains
        .train-option {
          display: flex;
          align-items: center;
          
          i {
            color: #7f8c8d;
            margin-right: 0.5rem;
            
            &.pi-building {
              color: #e67e22;
            }
            
            &.pi-cog {
              color: #3498db;
            }
          }
        }
      }
    }
    
    .context-info {
      display: flex;
      gap: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #ecf0f1;
      flex-wrap: wrap;
      
      .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        .info-label {
          font-weight: 600;
          color: #7f8c8d;
        }
        
        .info-value {
          color: #2c3e50;
          font-weight: 500;
        }
      }
    }
  }
  
  .statistics-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
    
    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ecf0f1;
        color: #3498db;
        font-size: 1.5rem;
      }
      
      .stat-content {
        .stat-value {
          font-size: 1.8rem;
          font-weight: 700;
          color: #2c3e50;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 0.9rem;
          color: #7f8c8d;
          margin-top: 0.25rem;
        }
      }
      
      &.total-card {
        .stat-icon {
          background: #fdf2e9;
          color: #e67e22;
        }
        
        .stat-value {
          color: #e67e22;
        }
      }
    }
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    p {
      margin-top: 1rem;
      color: #7f8c8d;
      font-size: 1.1rem;
    }
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .evolution-table {
      .cause-cell {
        font-weight: 500;
        color: #2c3e50;
        display: flex;
        align-items: center;
        
        .cause-icon {
          margin-right: 0.5rem;
          color: #7f8c8d;
        }
      }
      
      .numeric-cell {
        text-align: right;
        color: #7f8c8d;
        font-family: 'Courier New', monospace;
        
        &.has-value {
          color: #2c3e50;
          font-weight: 500;
        }
      }
    }
  }
  
  .legend-container {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      .legend-icon {
        color: #7f8c8d;
      }
      
      .legend-label {
        font-weight: 600;
        color: #2c3e50;
      }
      
      .legend-description {
        color: #7f8c8d;
      }
    }
  }
}
