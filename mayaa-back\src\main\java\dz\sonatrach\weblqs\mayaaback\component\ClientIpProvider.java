package dz.sonatrach.weblqs.mayaaback.component;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.RequestScope;

import jakarta.servlet.http.HttpServletRequest;

@Component
@RequestScope
public class ClientIpProvider {

    private final String clientIp;

    public ClientIpProvider(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader != null) {
            this.clientIp = xForwardedForHeader.split(",")[0];
        } else {
            this.clientIp = request.getRemoteAddr();
        }
    }

    public String getClientIp() {
        return clientIp;
    }
}
