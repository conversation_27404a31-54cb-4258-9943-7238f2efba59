package dz.sonatrach.weblqs.mayaaback.model;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@Entity
@Table(name = "NOMBRE_ARRET_TRAINS_PAR_TYPE")
public class NombreArretTrainsParType implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "UNITE", length = 3)
    private String unite;

    @Column(name = "TRAIN", length = 4)
    private String train;

    @Column(name = "PDATE")
    private LocalDate pdate;

    @Column(name = "PNUM_TYPEA", length = 10)
    private String pnumTypea;

    @Column(name = "TYPE_ARRET", length = 100)
    private String typeArret;

    @Column(name = "NBRE_ARRET")
    private Integer nbreArret;

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUnite() { return unite; }
    public void setUnite(String unite) { this.unite = unite; }


    public String getTrain() { return train; }
    public void setTrain(String train) { this.train = train; }

    public LocalDate getPdate() { return pdate; }
    public void setPdate(LocalDate pdate) { this.pdate = pdate; }

    public String getPnumTypea() { return pnumTypea; }
    public void setPnumTypea(String pnumTypea) { this.pnumTypea = pnumTypea; }

    public String getTypeArret() { return typeArret; }
    public void setTypeArret(String typeArret) { this.typeArret = typeArret; }

    public Integer getNbreArret() { return nbreArret; }
    public void setNbreArret(Integer nbreArret) { this.nbreArret = nbreArret; }
}
