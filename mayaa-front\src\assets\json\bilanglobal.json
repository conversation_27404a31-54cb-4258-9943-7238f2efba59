[{"complexId": "GL2Z", "period": "2024-10", "consommationEnergetiqueGlobale": {"value": 48904.897, "unit": "m³ eq GN", "percent": 18.16}, "autoconsommationBrute": {"value": 46615.666, "unit": "m³ eq GN", "percent": 17.31, "designTarget": 11, "internalTarget": 16, "deviationFromDesign": 6.31, "deviationFromLastMonth": -5.81}, "ceSonelgaz": {"value": 2239.627, "unit": "m³ eq GN", "percent": 0.83}, "ceKahrama": {"value": 49.604, "unit": "m³ eq GN", "percent": 0.018}, "totalAcCauseInterne": {"value": 46614.127, "unit": "m³ eq GN", "percent": 17.31}, "totalAcCauseExterne": {"value": 1.539, "unit": "m³ eq GN", "percent": 0.0006}, "autoconsommationNette": {"value": 46615.666, "unit": "m³ eq GN", "percent": 16.92}, "losses": [{"label": "Gaz torchés", "value": 1049.073, "percent": 0.39, "category": "GazTorchés", "causeType": "Interne"}, {"label": "Dégivrage", "value": 219.145, "percent": 0.08, "category": "Dégivrage", "causeType": "Interne"}, {"label": "Arrêt train 200", "value": 1.539, "percent": 0.0006, "category": "<PERSON><PERSON><PERSON><PERSON>", "causeType": "Externe"}]}, {"complexId": "GL2Z", "period": "2024-11", "consommationEnergetiqueGlobale": {"value": 49500.338, "unit": "m³ eq GN", "percent": 17.09}, "autoconsommationBrute": {"value": 47600.0, "unit": "m³ eq GN", "percent": 18.0, "designTarget": 11, "internalTarget": 17, "deviationFromDesign": 7.0, "deviationFromLastMonth": 0.69}, "ceSonelgaz": {"value": 2300.0, "unit": "m³ eq GN", "percent": 0.85}, "ceKahrama": {"value": 50.0, "unit": "m³ eq GN", "percent": 0.02}, "totalAcCauseInterne": {"value": 47598.461, "unit": "m³ eq GN", "percent": 17.97}, "totalAcCauseExterne": {"value": 1.539, "unit": "m³ eq GN", "percent": 0.0006}, "autoconsommationNette": {"value": 47600.0, "unit": "m³ eq GN", "percent": 17.64}, "losses": [{"label": "Gaz torchés", "value": 1240.0, "percent": 2.25, "category": "GazTorchés", "causeType": "Interne"}, {"label": "Excès boil-off", "value": 230.0, "percent": 0.42, "category": "<PERSON><PERSON><PERSON><PERSON>", "causeType": "Interne"}, {"label": "Déclenchement compresseur", "value": 89.796, "percent": 0.03, "category": "CompresseurRegénération", "causeType": "Interne"}]}, {"complexId": "GL2Z", "period": "2024-12", "consommationEnergetiqueGlobale": {"value": 48904.897, "unit": "m³ eq GN", "percent": 18.16}, "autoconsommationBrute": {"value": 46615.666, "unit": "m³ eq GN", "percent": 17.31, "designTarget": 11, "internalTarget": 16, "deviationFromDesign": 6.31, "deviationFromLastMonth": -5.81}, "ceSonelgaz": {"value": 2239.627, "unit": "m³ eq GN", "percent": 0.83}, "ceKahrama": {"value": 49.604, "unit": "m³ eq GN", "percent": 0.018}, "totalAcCauseInterne": {"value": 46614.127, "unit": "m³ eq GN", "percent": 17.31}, "totalAcCauseExterne": {"value": 1.539, "unit": "m³ eq GN", "percent": 0.0006}, "autoconsommationNette": {"value": 46615.666, "unit": "m³ eq GN", "percent": 16.92}, "postes": [{"label": "Poste 1", "value": 12000.073, "percent": 3.39, "category": "poste1", "causeType": "Interne"}, {"label": "Poste 2", "value": 283.515, "percent": 0.11, "category": "poste2", "causeType": "Interne"}, {"label": "Poste 3", "value": 187.439, "percent": 0.07, "category": "poste3", "causeType": "Externe"}, {"label": "Poste 4", "value": 1872.439, "percent": 2.33, "category": "poste4", "causeType": "Externe"}], "losses": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": 1049.073, "percent": 0.39, "category": "arrets", "causeType": "Interne"}, {"label": "Dégivrage", "value": 283.515, "percent": 0.11, "category": "degivrage", "causeType": "Interne"}, {"label": "Excès fuel gaz", "value": 187.439, "percent": 0.07, "category": "ExcèsFuelGaz", "causeType": "Externe"}]}, {"complexId": "GL3X", "period": "2024-10", "consommationEnergetiqueGlobale": {"value": 47500.0, "unit": "m³ eq GN", "percent": 17.5}, "autoconsommationBrute": {"value": 44000.0, "unit": "m³ eq GN", "percent": 16.0, "designTarget": 11, "internalTarget": 15, "deviationFromDesign": 5.0, "deviationFromLastMonth": null}, "ceSonelgaz": {"value": 2100.0, "unit": "m³ eq GN", "percent": 0.76}, "ceKahrama": {"value": 45.0, "unit": "m³ eq GN", "percent": 0.02}, "totalAcCauseInterne": {"value": 43998.461, "unit": "m³ eq GN", "percent": 16.0}, "totalAcCauseExterne": {"value": 1.539, "unit": "m³ eq GN", "percent": 0.0006}, "autoconsommationNette": {"value": 44000.0, "unit": "m³ eq GN", "percent": 15.24}, "losses": [{"label": "Gaz torchés", "value": 950.0, "percent": 0.3, "category": "GazTorchés", "causeType": "Interne"}, {"label": "Dégivrage", "value": 200.0, "percent": 0.05, "category": "Dégivrage", "causeType": "Interne"}]}, {"complexId": "GL3X", "period": "2024-11", "consommationEnergetiqueGlobale": {"value": 48000.0, "unit": "m³ eq GN", "percent": 17.0}, "autoconsommationBrute": {"value": 45500.0, "unit": "m³ eq GN", "percent": 16.5, "designTarget": 11, "internalTarget": 15.5, "deviationFromDesign": 5.5, "deviationFromLastMonth": 0.5}, "ceSonelgaz": {"value": 2150.0, "unit": "m³ eq GN", "percent": 0.8}, "ceKahrama": {"value": 47.0, "unit": "m³ eq GN", "percent": 0.02}, "totalAcCauseInterne": {"value": 45498.461, "unit": "m³ eq GN", "percent": 16.47}, "totalAcCauseExterne": {"value": 1.539, "unit": "m³ eq GN", "percent": 0.0006}, "autoconsommationNette": {"value": 45500.0, "unit": "m³ eq GN", "percent": 15.8}, "losses": [{"label": "Gaz torchés", "value": 1125.0, "percent": 0.59, "category": "GazTorchés", "causeType": "Interne"}, {"label": "Excès boil-off", "value": 210.0, "percent": 0.04, "category": "<PERSON><PERSON><PERSON><PERSON>", "causeType": "Interne"}]}, {"complexId": "GL3X", "period": "2024-12", "consommationEnergetiqueGlobale": {"value": 48200.0, "unit": "m³ eq GN", "percent": 17.2}, "autoconsommationBrute": {"value": 46000.0, "unit": "m³ eq GN", "percent": 16.9, "designTarget": 11, "internalTarget": 16, "deviationFromDesign": 5.9, "deviationFromLastMonth": 0.4}, "ceSonelgaz": {"value": 2180.0, "unit": "m³ eq GN", "percent": 0.82}, "ceKahrama": {"value": 48.0, "unit": "m³ eq GN", "percent": 0.02}, "totalAcCauseInterne": {"value": 45998.461, "unit": "m³ eq GN", "percent": 16.88}, "totalAcCauseExterne": {"value": 1.539, "unit": "m³ eq GN", "percent": 0.0006}, "autoconsommationNette": {"value": 46000.0, "unit": "m³ eq GN", "percent": 16.5}, "losses": [{"label": "Gaz torchés", "value": 1000.0, "percent": 0.36, "category": "GazTorchés", "causeType": "Interne"}, {"label": "<PERSON><PERSON> réfrig<PERSON>", "value": 250.0, "percent": 0.06, "category": "PertesRéfrigérants", "causeType": "Interne"}]}]