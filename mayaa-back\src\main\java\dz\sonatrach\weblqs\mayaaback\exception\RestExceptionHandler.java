package dz.sonatrach.weblqs.mayaaback.exception;

import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;
import static org.springframework.http.HttpStatus.UNAUTHORIZED;

import org.apache.catalina.connector.ClientAbortException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import lombok.extern.slf4j.Slf4j;

@Order(Ordered.LOWEST_PRECEDENCE)
@ControllerAdvice
@Slf4j
public class RestExceptionHandler extends ResponseEntityExceptionHandler {

	//@Autowired
	//UtilisateurService utilisateurService;

	@ExceptionHandler(BadRequestException.class)
	protected ResponseEntity<Object> handleBadRequestException(BadRequestException ex) {
		ErrorEntity error = new ErrorEntity(BAD_REQUEST, ex.getMessage(), ex.silentMode);
		// ex.printStackTrace();
		return buildResponseEntity(error);
	}

	@ExceptionHandler(UnAuthorizedException.class)
	protected ResponseEntity<Object> handleUnAuthorizedException(UnAuthorizedException ex) {
		ErrorEntity error = new ErrorEntity(UNAUTHORIZED, "Accès refusé !");
		// ex.printStackTrace();
		return buildResponseEntity(error);
	}
/*
	@ExceptionHandler(AccessDeniedException.class)
	protected ResponseEntity<Object> handleUnAuthorizedException(AccessDeniedException ex) {
		ErrorEntity error = new ErrorEntity(UNAUTHORIZED, "Accès refusé !");
		printMethodLineNumber(ex);
		return buildResponseEntity(error);
	}
	*/

	@ExceptionHandler(NotFoundException.class)
	protected ResponseEntity<Object> handleNotFoundException(NotFoundException ex) {
		ErrorEntity error = new ErrorEntity(NOT_FOUND, "Ressource introuvable !");
		// ex.printStackTrace();
		return buildResponseEntity(error);
	}

	@ExceptionHandler(Exception.class)
	protected ResponseEntity<Object> handleNotAuthorizedException(Exception ex) {
		if (ex instanceof ClientAbortException) {
			System.out.println("Client closed connection");
			return ResponseEntity.noContent().build();
		}
		ErrorEntity error = new ErrorEntity(INTERNAL_SERVER_ERROR, ex.getMessage(), ex);
		ex.printStackTrace();
		/*
		Sentry.configureScope(scope -> {
			User user = new User();
			user.setName(utilisateurService.getUserInfos().getNomComplet());
			user.setUsername(utilisateurService.getUserInfos().getUsername());
			scope.setUser(user);
		});
		Sentry.captureException(ex);
		*/
		return buildResponseEntity(error);
	}

	private ResponseEntity<Object> buildResponseEntity(ErrorEntity entity) {
		return new ResponseEntity<>(entity, entity.getStatus());
	}

	private void printMethodLineNumber(Exception ex) {
		StackTraceElement[] stackTrace = ex.getStackTrace();
		if (stackTrace.length > 0) {
			int lineNumber = stackTrace[0].getLineNumber();
			String className = stackTrace[0].getClassName();
			String methodName = stackTrace[0].getMethodName();
			logger.error("Exception thrown in class: " + className + ", method: " + methodName + ", line number: "
					+ lineNumber);
		}
	}

}
