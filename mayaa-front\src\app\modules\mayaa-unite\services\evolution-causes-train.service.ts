import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  EvolutionCausesTrain,
  EvolutionCausesResponse,
  TrainCauseEvolutionData,
  CauseItemEvolutionData,
  MonthlyQuantity,
  GlobalCauseStatistics,
  TrainCauseStatistics,
  CauseDisplayMode,
  EvolutionCausesParams,
  TrainOption,
  createTrainOptions,
  MOIS_LABELS,
  MOIS_COMPLETS
} from '../../../model/evolution-causes-train.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class EvolutionCausesTrainService {
  private baseUrl = environment.apiUrl+ '/api/evolution-causes-train';

  constructor(private http: HttpClient) { }

  /**
   * Récupère les données d'évolution des causes pour une unité, une année et un train
   * @param params Paramètres de la requête
   * @returns Observable contenant les données d'évolution organisées
   */
  getEvolutionCausesData(params: EvolutionCausesParams): Observable<EvolutionCausesResponse> {
    const train = params.train || 'COMPLEXE';
    const url = `${this.baseUrl}/${params.unite}/${params.annee}/${train}`;

    return this.http.get<EvolutionCausesTrain[]>(url).pipe(
      map(data => this.transformDataToResponse(data, params)),
      catchError(error => {
        console.error('Erreur lors de la récupération des données d\'évolution:', error);
        return of(this.getEmptyResponse(params));
      })
    );
  }

  /**
   * Récupère les trains disponibles pour une unité et un mois
   * @param unite Code de l'unité
   * @param mois Mois au format ddMMyyyy
   * @returns Observable contenant les options de trains avec "COMPLEXE" en premier
   */
  getTrainsDisponibles(unite: string, mois: string): Observable<TrainOption[]> {
    const url = `${this.baseUrl}/${unite}/${mois}/trains`;

    return this.http.get<string[]>(url).pipe(
      map(trains => createTrainOptions(trains)),
      catchError(error => {
        console.error('Erreur lors de la récupération des trains:', error);
        return of(createTrainOptions(['COMPLEXE']));
      })
    );
  }

  /**
   * Récupère les sièges de causes disponibles pour une unité et un mois
   * @param unite Code de l'unité
   * @param mois Mois au format ddMMyyyy
   * @returns Observable contenant la liste des sièges
   */
  getSiegesDisponibles(unite: string, mois: string): Observable<string[]> {
    const url = `${this.baseUrl}/${unite}/${mois}/sieges`;

    return this.http.get<string[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des sièges:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les classes de causes disponibles pour une unité et un mois
   * @param unite Code de l'unité
   * @param mois Mois au format ddMMyyyy
   * @returns Observable contenant la liste des classes
   */
  getClassesDisponibles(unite: string, mois: string): Observable<string[]> {
    const url = `${this.baseUrl}/${unite}/${mois}/classes`;

    return this.http.get<string[]>(url).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des classes:', error);
        return of([]);
      })
    );
  }

  /**
   * Calcule les statistiques globales à partir des données d'évolution
   * @param response Données d'évolution complètes
   * @returns Statistiques globales
   */
  calculateGlobalStatistics(response: EvolutionCausesResponse): GlobalCauseStatistics {
    const trainStats: TrainCauseStatistics[] = response.trains.map(train => {
      const totalTrain = train.totalAnnuel;

      return {
        codeTrain: train.codeTrain,
        nombreCauses: train.causes.length,
        totalQuantite: totalTrain,
        pourcentageTotal: response.totalGlobal > 0 ? (totalTrain / response.totalGlobal) * 100 : 0
      };
    });

    // Calcul du mois le plus actif
    const quantitesParMois: { [mois: number]: number } = {};

    response.trains.forEach(train => {
      train.causes.forEach(cause => {
        cause.quantitesMensuelles.forEach(monthly => {
          if (!quantitesParMois[monthly.mois]) {
            quantitesParMois[monthly.mois] = 0;
          }
          quantitesParMois[monthly.mois] += monthly.quantite;
        });
      });
    });

    const moisLePlusActif = Object.entries(quantitesParMois)
      .reduce((max, [mois, total]) =>
        total > max.total ? { mois: parseInt(mois), total } : max,
        { mois: 1, total: 0 }
      );

    return {
      nombreTrains: response.trains.length,
      nombreCausesTotal: response.trains.reduce((sum, train) => sum + train.causes.length, 0),
      totalGlobal: response.totalGlobal,
      repartitionParTrain: trainStats,
      moisLePlusActif: {
        mois: moisLePlusActif.mois,
        nomMois: MOIS_COMPLETS[moisLePlusActif.mois],
        total: moisLePlusActif.total
      },
      mode: response.mode
    };
  }

  /**
   * Transforme les données brutes en réponse structurée
   */
  private transformDataToResponse(data: EvolutionCausesTrain[], params: EvolutionCausesParams): EvolutionCausesResponse {
    const trainsMap = new Map<string, TrainCauseEvolutionData>();

    // Grouper les données par train
    data.forEach(item => {
      if (!trainsMap.has(item.codeTrain)) {
        trainsMap.set(item.codeTrain, {
          codeTrain: item.codeTrain,
          causes: [],
          totalAnnuel: 0
        });
      }

      const train = trainsMap.get(item.codeTrain)!;
      const moisDate = new Date(item.mois);
      const moisNum = moisDate.getMonth() + 1;

      // Déterminer le libellé de la cause selon le mode
      const libelleCause = params.mode === 'sieges' ? item.siegeCause : item.classeCause;

      if (!libelleCause) return; // Ignorer si pas de cause pour ce mode

      // Chercher si la cause existe déjà
      let cause = train.causes.find(c => c.libelleCause === libelleCause);

      if (!cause) {
        cause = {
          libelleCause,
          mode: params.mode,
          quantitesMensuelles: this.initializeMonthlyQuantities(),
          totalAnnuel: 0
        };
        train.causes.push(cause);
      }

      // Ajouter la quantité au mois correspondant
      const monthlyQuantity = cause.quantitesMensuelles.find(m => m.mois === moisNum);
      if (monthlyQuantity) {
        monthlyQuantity.quantite += item.quantiteGazTorchee;
        cause.totalAnnuel += item.quantiteGazTorchee;
        train.totalAnnuel += item.quantiteGazTorchee;
      }
    });

    const trains = Array.from(trainsMap.values());
    const totalGlobal = trains.reduce((sum, train) => sum + train.totalAnnuel, 0);

    return {
      trains,
      annee: params.annee,
      unite: params.unite,
      mode: params.mode,
      totalGlobal
    };
  }

  /**
   * Initialise les quantités mensuelles à zéro
   */
  private initializeMonthlyQuantities(): MonthlyQuantity[] {
    return Array.from({ length: 12 }, (_, index) => ({
      mois: index + 1,
      nomMois: MOIS_LABELS[index + 1],
      quantite: 0
    }));
  }

  /**
   * Retourne une réponse vide en cas d'erreur
   */
  private getEmptyResponse(params: EvolutionCausesParams): EvolutionCausesResponse {
    return {
      trains: [],
      annee: params.annee,
      unite: params.unite,
      mode: params.mode,
      totalGlobal: 0
    };
  }
}
