package dz.sonatrach.weblqs.mayaaback.controller;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import dz.sonatrach.weblqs.mayaaback.model.RepartitionArretsParSiege;
// import dz.sonatrach.weblqs.mayaaback.repo.RepartitionArretsParSiegeRepository;

/**
 * Contrôleur pour l'accès aux données de la vue REPARTITION_ARRETS_PAR_SIEGE.
 *
 * Endpoints principaux :
 *   GET /api/repartition-arrets-siege/{unite}/{mois}
 *   GET /api/repartition-arrets-siege/{unite}/{mois}/internes
 *   GET /api/repartition-arrets-siege/{unite}/{mois}/externes
 *   GET /api/repartition-arrets-siege/{unite}/{mois}/sieges
 *   GET /api/repartition-arrets-siege/{unite}/{mois}/pie-chart/{type}
 *
 * - unite : code de l'unité (String, ex: "5X3")
 * - mois : période au format ddMMyyyy (ex: 01012024)
 * - type : type de siège (INTERNE ou EXTERNE)
 *
 * Réponses :
 *   200 OK : données trouvées
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/repartition-arrets-siege")
public class RepartitionArretsParSiegeController {

    // Repository commenté temporairement - en attente de la création des vues/tables
    // @Autowired
    // private RepartitionArretsParSiegeRepository repartitionArretsParSiegeRepository;

    /**
     * Récupère la répartition des arrêts par siège pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des données de répartition par siège
     */
    @GetMapping("/{unite}/{mois}")
    public ResponseEntity<List<RepartitionArretsParSiege>> getByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));

            // Données simulées - remplacer par repository quand les vues seront créées
            // List<RepartitionArretsParSiege> result = repartitionArretsParSiegeRepository.findByUniteAndMois(unite, date);
            List<RepartitionArretsParSiege> result = createMockRepartitionSiege(unite, date);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la répartition des arrêts par sièges internes
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des données de répartition pour les sièges internes
     */
    @GetMapping("/{unite}/{mois}/internes")
    public ResponseEntity<List<RepartitionArretsParSiege>> getSiegesInternesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));

            // Données simulées - remplacer par repository quand les vues seront créées
            // List<RepartitionArretsParSiege> result = repartitionArretsParSiegeRepository.findSiegesInternesByUniteAndMois(unite, date);
            List<RepartitionArretsParSiege> result = createMockRepartitionSiege(unite, date)
                    .stream()
                    .filter(r -> "INTERNE".equals(r.getTypeSiege()))
                    .toList();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la répartition des arrêts par sièges externes
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des données de répartition pour les sièges externes
     */
    @GetMapping("/{unite}/{mois}/externes")
    public ResponseEntity<List<RepartitionArretsParSiege>> getSiegesExternesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));

            // Données simulées - remplacer par repository quand les vues seront créées
            // List<RepartitionArretsParSiege> result = repartitionArretsParSiegeRepository.findSiegesExternesByUniteAndMois(unite, date);
            List<RepartitionArretsParSiege> result = createMockRepartitionSiege(unite, date)
                    .stream()
                    .filter(r -> "EXTERNE".equals(r.getTypeSiege()))
                    .toList();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les sièges distincts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des sièges de causes distincts
     */
    @GetMapping("/{unite}/{mois}/sieges")
    public ResponseEntity<List<String>> getSiegesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            // Données simulées - remplacer par repository quand les vues seront créées
            // LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            // List<String> sieges = repartitionArretsParSiegeRepository.findDistinctSiegesByUniteAndMois(unite, date);
            List<String> sieges = Arrays.asList(
                "Compresseur principal",
                "Turbine à gaz",
                "Système électrique",
                "Réseau de distribution",
                "Équipement auxiliaire"
            );

            return ResponseEntity.ok(sieges);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les données pour les graphiques en secteurs (avec pourcentages)
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @param type Type de siège (INTERNE ou EXTERNE)
     * @return Liste des données avec pourcentages pour les graphiques
     */
    @GetMapping("/{unite}/{mois}/pie-chart/{type}")
    public ResponseEntity<List<RepartitionArretsParSiege>> getDataForPieChart(
            @PathVariable String unite,
            @PathVariable String mois,
            @PathVariable String type) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));

            // Valider le type
            if (!type.equals("INTERNE") && !type.equals("EXTERNE")) {
                return ResponseEntity.badRequest().build();
            }

            // Données simulées - remplacer par repository quand les vues seront créées
            // List<RepartitionArretsParSiege> result = repartitionArretsParSiegeRepository.findDataForPieChart(unite, date, type);
            List<RepartitionArretsParSiege> result = createMockRepartitionSiege(unite, date)
                    .stream()
                    .filter(r -> type.equals(r.getTypeSiege()))
                    .filter(r -> r.getPourcentage().compareTo(BigDecimal.ZERO) > 0)
                    .toList();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la répartition par type de siège spécifique
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @param type Type de siège (INTERNE ou EXTERNE)
     * @return Liste des données de répartition pour le type de siège
     */
    @GetMapping("/{unite}/{mois}/type/{type}")
    public ResponseEntity<List<RepartitionArretsParSiege>> getByUniteAndMoisAndType(
            @PathVariable String unite,
            @PathVariable String mois,
            @PathVariable String type) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));

            // Valider le type
            if (!type.equals("INTERNE") && !type.equals("EXTERNE")) {
                return ResponseEntity.badRequest().build();
            }

            // Données simulées - remplacer par repository quand les vues seront créées
            // List<RepartitionArretsParSiege> result = repartitionArretsParSiegeRepository.findByUniteAndMoisAndTypeSiege(unite, date, type);
            List<RepartitionArretsParSiege> result = createMockRepartitionSiege(unite, date)
                    .stream()
                    .filter(r -> type.equals(r.getTypeSiege()))
                    .toList();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Méthodes privées pour créer des données mockées
    private List<RepartitionArretsParSiege> createMockRepartitionSiege(String unite, LocalDate date) {
        List<RepartitionArretsParSiege> result = new ArrayList<>();

        // Sièges internes
        RepartitionArretsParSiege siege1 = new RepartitionArretsParSiege();
        siege1.setId(1L);
        siege1.setUnite(unite);
        siege1.setMois(date);
        siege1.setSiegeCause("Compresseur principal");
        siege1.setTypeSiege("INTERNE");
        siege1.setQuantiteArrets(new BigDecimal("15"));
        siege1.setPourcentage(new BigDecimal("35.2"));
        siege1.setNumeroSiege(1);
        result.add(siege1);

        RepartitionArretsParSiege siege2 = new RepartitionArretsParSiege();
        siege2.setId(2L);
        siege2.setUnite(unite);
        siege2.setMois(date);
        siege2.setSiegeCause("Turbine à gaz");
        siege2.setTypeSiege("INTERNE");
        siege2.setQuantiteArrets(new BigDecimal("12"));
        siege2.setPourcentage(new BigDecimal("28.1"));
        siege2.setNumeroSiege(2);
        result.add(siege2);

        RepartitionArretsParSiege siege3 = new RepartitionArretsParSiege();
        siege3.setId(3L);
        siege3.setUnite(unite);
        siege3.setMois(date);
        siege3.setSiegeCause("Système de contrôle");
        siege3.setTypeSiege("INTERNE");
        siege3.setQuantiteArrets(new BigDecimal("8"));
        siege3.setPourcentage(new BigDecimal("18.7"));
        siege3.setNumeroSiege(3);
        result.add(siege3);

        // Sièges externes
        RepartitionArretsParSiege siege4 = new RepartitionArretsParSiege();
        siege4.setId(4L);
        siege4.setUnite(unite);
        siege4.setMois(date);
        siege4.setSiegeCause("Réseau électrique");
        siege4.setTypeSiege("EXTERNE");
        siege4.setQuantiteArrets(new BigDecimal("10"));
        siege4.setPourcentage(new BigDecimal("23.5"));
        siege4.setNumeroSiege(4);
        result.add(siege4);

        RepartitionArretsParSiege siege5 = new RepartitionArretsParSiege();
        siege5.setId(5L);
        siege5.setUnite(unite);
        siege5.setMois(date);
        siege5.setSiegeCause("Fournisseur gaz");
        siege5.setTypeSiege("EXTERNE");
        siege5.setQuantiteArrets(new BigDecimal("7"));
        siege5.setPourcentage(new BigDecimal("16.4"));
        siege5.setNumeroSiege(5);
        result.add(siege5);

        RepartitionArretsParSiege siege6 = new RepartitionArretsParSiege();
        siege6.setId(6L);
        siege6.setUnite(unite);
        siege6.setMois(date);
        siege6.setSiegeCause("Conditions météorologiques");
        siege6.setTypeSiege("EXTERNE");
        siege6.setQuantiteArrets(new BigDecimal("5"));
        siege6.setPourcentage(new BigDecimal("11.7"));
        siege6.setNumeroSiege(6);
        result.add(siege6);

        return result;
    }
}
