import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MayaaConsRoutingModule } from './mayaa-cons-routing.module';

import { RealisationComponent } from './pages/realisation/realisation.component';
import { AutoconsommationComponent } from './pages/autoconsommation/autoconsommation.component';
import { ArretsConsolideComponent } from './pages/arrets-consolide/arrets-consolide.component';

// PrimeNG Modules
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { DividerModule } from 'primeng/divider';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { BadgeModule } from 'primeng/badge';
import { MessageModule } from 'primeng/message';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@NgModule({
  declarations: [
    RealisationComponent,
    AutoconsommationComponent,
    ArretsConsolideComponent
  ],
  imports: [
    CommonModule,
    MayaaConsRoutingModule,
    // PrimeNG Modules
    CardModule,
    ChartModule,
    TableModule,
    TagModule,
    ProgressBarModule,
    DividerModule,
    SkeletonModule,
    TooltipModule,
    BadgeModule,
    MessageModule,
    ProgressSpinnerModule
  ]
})
export class MayaaConsModule { }
