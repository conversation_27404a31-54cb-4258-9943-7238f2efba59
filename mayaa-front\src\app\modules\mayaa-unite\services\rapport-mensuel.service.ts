import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

/**
 * Interface pour le statut du rapport
 */
export interface RapportStatus {
  periode: string;
  disponible: boolean;
  message: string;
}

/**
 * Service pour la gestion du rapport mensuel
 */
@Injectable({
  providedIn: 'root'
})
export class RapportMensuelService {
  private baseUrl = environment.apiUrl + '/api/rapport-mensuel';

  constructor(private http: HttpClient) { }

  /**
   * Vérifie la disponibilité des données pour une période donnée
   * @param periode Période au format ddMMyyyy
   * @returns Observable<RapportStatus>
   */
  verifierDisponibilite(periode: string): Observable<RapportStatus> {
    const url = `${this.baseUrl}/${periode}/status`;
    return this.http.get<RapportStatus>(url);
  }

  /**
   * Génère et télécharge le rapport mensuel
   * @param periode Période au format ddMMyyyy
   * @returns Observable<Blob>
   */
  genererRapport(periode: string): Observable<Blob> {
    const url = `${this.baseUrl}/${periode}`;
    const headers = new HttpHeaders({
      'Accept': 'application/pdf'
    });

    return this.http.get(url, {
      headers: headers,
      responseType: 'blob'
    });
  }

  /**
   * Prévisualise le rapport mensuel
   * @param periode Période au format ddMMyyyy
   * @returns Observable<Blob>
   */
  previsualiserRapport(periode: string): Observable<Blob> {
    const url = `${this.baseUrl}/${periode}/preview`;
    const headers = new HttpHeaders({
      'Accept': 'application/pdf'
    });

    return this.http.get(url, {
      headers: headers,
      responseType: 'blob'
    });
  }

  /**
   * Formate une date pour l'API (ddMMyyyy)
   * @param date Date à formater
   * @returns string au format ddMMyyyy
   */
  formatDateForApi(date: Date): string {
    const day = '01'; // Premier jour du mois
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  /**
   * Formate une date pour l'affichage
   * @param dateStr Date au format ddMMyyyy
   * @returns string formaté pour l'affichage
   */
  formatDateForDisplay(dateStr: string): string {
    if (!dateStr || dateStr.length !== 8) {
      return 'Date invalide';
    }

    try {
      const year = parseInt(dateStr.substring(4, 8));
      const month = parseInt(dateStr.substring(2, 4)) - 1; // 0-indexé
      const date = new Date(year, month, 1);
      
      return date.toLocaleDateString('fr-FR', { 
        month: 'long', 
        year: 'numeric' 
      });
    } catch (error) {
      return 'Date invalide';
    }
  }

  /**
   * Valide le format de période
   * @param periode Période à valider
   * @returns boolean
   */
  validerFormatPeriode(periode: string): boolean {
    if (!periode || periode.length !== 8) {
      return false;
    }

    const regex = /^\d{8}$/;
    if (!regex.test(periode)) {
      return false;
    }

    const day = parseInt(periode.substring(0, 2));
    const month = parseInt(periode.substring(2, 4));
    const year = parseInt(periode.substring(4, 8));

    // Vérifications basiques
    if (day < 1 || day > 31) return false;
    if (month < 1 || month > 12) return false;
    if (year < 2020 || year > 2030) return false;

    return true;
  }

  /**
   * Obtient la période courante au format API
   * @returns string au format ddMMyyyy pour le mois courant
   */
  getPeriodeCourante(): string {
    return this.formatDateForApi(new Date());
  }

  /**
   * Obtient la période précédente
   * @param periode Période de référence au format ddMMyyyy
   * @returns string au format ddMMyyyy pour le mois précédent
   */
  getPeriodePrecedente(periode: string): string {
    if (!this.validerFormatPeriode(periode)) {
      return this.getPeriodeCourante();
    }

    const year = parseInt(periode.substring(4, 8));
    const month = parseInt(periode.substring(2, 4));

    let newMonth = month - 1;
    let newYear = year;

    if (newMonth === 0) {
      newMonth = 12;
      newYear = year - 1;
    }

    const day = '01';
    const monthStr = newMonth.toString().padStart(2, '0');
    const yearStr = newYear.toString();

    return `${day}${monthStr}${yearStr}`;
  }

  /**
   * Obtient la période suivante
   * @param periode Période de référence au format ddMMyyyy
   * @returns string au format ddMMyyyy pour le mois suivant
   */
  getPeriodeSuivante(periode: string): string {
    if (!this.validerFormatPeriode(periode)) {
      return this.getPeriodeCourante();
    }

    const year = parseInt(periode.substring(4, 8));
    const month = parseInt(periode.substring(2, 4));

    let newMonth = month + 1;
    let newYear = year;

    if (newMonth === 13) {
      newMonth = 1;
      newYear = year + 1;
    }

    const day = '01';
    const monthStr = newMonth.toString().padStart(2, '0');
    const yearStr = newYear.toString();

    return `${day}${monthStr}${yearStr}`;
  }

  /**
   * Génère une liste de périodes disponibles (12 derniers mois)
   * @returns Array<{value: string, label: string}>
   */
  getPeriodesDisponibles(): Array<{value: string, label: string}> {
    const periodes: Array<{value: string, label: string}> = [];
    const now = new Date();

    for (let i = 0; i < 12; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const value = this.formatDateForApi(date);
      const label = this.formatDateForDisplay(value);
      
      periodes.push({ value, label });
    }

    return periodes;
  }
}
