import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, effect } from '@angular/core';
import { Subscription } from 'rxjs';

import { AutoconsommationService } from '../../services/autoconsommation.service';
import { CalendarService } from '../../../../services/calendar.service';
import {
  DonneesAutoconsommation,
  ChartDataAutoconsommation,
  ChartOptionsAutoconsommation
} from '../../../../model/Autoconsommation';

@Component({
  selector: 'app-autoconsommation',
  templateUrl: './autoconsommation.component.html',
  styleUrl: './autoconsommation.component.scss'
})
export class AutoconsommationComponent implements OnInit, OnDestroy {
  donneesAutoconsommation: DonneesAutoconsommation | null = null;
  loading = true;
  error: string | null = null;

  // Données pour les graphiques d'autoconsommation
  repartitionAutoconsChartData: ChartDataAutoconsommation | null = null;
  repartitionAutoconsChartOptions: ChartOptionsAutoconsommation | null = null;
  gazTorcheChartData: ChartDataAutoconsommation | null = null;
  gazTorcheChartOptions: ChartOptionsAutoconsommation | null = null;

  private subscription = new Subscription();

  constructor(
    private autoconsommationService: AutoconsommationService,
    private calendarService: CalendarService
  ) {
    // Écouter les changements de calendrier
    effect(() => {
      const selectedDate = this.calendarService.selectedMonthYear();
      if (selectedDate) {
        this.loadAutoconsommationData();
      }
    });
  }

  ngOnInit() {
    this.loadAutoconsommationData();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  /**
   * Charge les données d'autoconsommation
   */
  private loadAutoconsommationData() {
    this.loading = true;
    this.error = null;

    const selectedDate = this.calendarService.selectedMonthYear();
    if (!selectedDate) {
      this.error = 'Aucune date sélectionnée';
      this.loading = false;
      return;
    }

    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth() + 1;
    const periode = `${year}${month.toString().padStart(2, '0')}`;

    this.subscription.add(
      this.autoconsommationService.getDonneesAutoconsommation(periode).subscribe({
        next: (data) => {
          this.donneesAutoconsommation = data;
          this.setupCharts();
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données:', error);
          this.error = 'Erreur lors du chargement des données';
          this.loading = false;
        }
      })
    );
  }

  /**
   * Configure les graphiques
   */
  private setupCharts() {
    if (!this.donneesAutoconsommation) return;

    this.setupRepartitionAutoconsChart();
    this.setupGazTorcheChart();
  }

  /**
   * Configure le graphique de répartition autoconsommation
   */
  private setupRepartitionAutoconsChart() {
    if (!this.donneesAutoconsommation?.unites) return;

    const repartition = this.donneesAutoconsommation.unites;
    const labels = repartition.map(r => r.unite);
    const data = repartition.map(r => r.autoconsommationNette);

    this.repartitionAutoconsChartData = {
      labels: labels,
      datasets: [
        {
          data: data,
          backgroundColor: [
            '#FF6384',
            '#36A2EB',
            '#FFCE56',
            '#4BC0C0',
            '#9966FF',
            '#FF9F40'
          ],
          borderColor: [
            '#FF6384',
            '#36A2EB',
            '#FFCE56',
            '#4BC0C0',
            '#9966FF',
            '#FF9F40'
          ],
          borderWidth: 1
        }
      ]
    };

    this.repartitionAutoconsChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    };
  }

  /**
   * Configure le graphique de gaz torché
   */
  private setupGazTorcheChart() {
    if (!this.donneesAutoconsommation?.gazTorcheCauses) return;

    const repartition = this.donneesAutoconsommation.gazTorcheCauses;
    const labels = repartition.map(r => r.cause);
    const data = repartition.map(r => r.quantite);

    this.gazTorcheChartData = {
      labels: labels,
      datasets: [
        {
          data: data,
          backgroundColor: [
            '#FF6B6B',
            '#4ECDC4',
            '#45B7D1',
            '#96CEB4',
            '#FFEAA7',
            '#DDA0DD'
          ],
          borderColor: [
            '#FF5252',
            '#26A69A',
            '#2196F3',
            '#66BB6A',
            '#FFD54F',
            '#BA68C8'
          ],
          borderWidth: 1
        }
      ]
    };

    this.gazTorcheChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    };
  }

  /**
   * Calcule le pourcentage d'autoconsommation
   */
  getPourcentageAutoconsommation(autoconsommation: number, gnRecu: number): number {
    if (gnRecu === 0) return 0;
    return Math.round((autoconsommation / gnRecu) * 100);
  }

  /**
   * Retourne la classe CSS pour le pourcentage
   */
  getPourcentageClass(pourcentage: number): string {
    if (pourcentage <= 8) return 'text-green-600';
    if (pourcentage <= 12) return 'text-orange-500';
    return 'text-red-600';
  }

  /**
   * Retourne la sévérité pour les tags PrimeNG
   */
  getTagSeverity(pourcentage: number): 'success' | 'warning' | 'danger' {
    if (pourcentage <= 8) return 'success';
    if (pourcentage <= 12) return 'warning';
    return 'danger';
  }

  /**
   * Retourne le label de statut
   */
  getStatusLabel(statut: string): string {
    switch (statut?.toLowerCase()) {
      case 'actif':
        return 'Actif';
      case 'arret':
        return 'Arrêt';
      case 'maintenance':
        return 'Maintenance';
      default:
        return statut || 'Inconnu';
    }
  }

  /**
   * Retourne la sévérité du statut
   */
  getStatusSeverity(statut: string): 'success' | 'danger' | 'warning' | 'info' {
    switch (statut?.toLowerCase()) {
      case 'actif':
        return 'success';
      case 'arret':
        return 'danger';
      case 'maintenance':
        return 'warning';
      default:
        return 'info';
    }
  }

  /**
   * Formate un nombre avec séparateurs de milliers
   */
  formatNumber(value: number, decimals: number = 0): string {
    if (value == null) return '0';
    return value.toLocaleString('fr-FR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  /**
   * Formate un pourcentage
   */
  formatPercentage(value: number): string {
    if (value == null) return '0%';
    return `${value.toFixed(1)}%`;
  }
}
