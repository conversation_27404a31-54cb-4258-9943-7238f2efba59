import { Directive, ElementRef, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[zTrim]',
})
export class TrimDirective {
  constructor(private el: ElementRef, private control: NgControl) {}

  @HostListener('blur') onFoncusOut() {
    const value = this.control.control!.value?.trim();
    this.control.control!.setValue(value);
  }

  @HostListener('input') onInput() {
    const value = this.control.control!.value.trimStart();
    this.control.control!.setValue(value);
  }
}
