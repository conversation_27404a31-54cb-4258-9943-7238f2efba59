package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.dto.RealisationResponse;
import dz.sonatrach.weblqs.mayaaback.dto.RealisationResponse.*;
import dz.sonatrach.weblqs.mayaaback.service.RealisationService;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Contrôleur pour les données de réalisation
 */
@RestController
@RequestMapping("api/realisation")

public class RealisationController {

    @Autowired
    private RealisationService realisationService;

    /**
     * Récupère toutes les données de réalisation consolidées
     * @param periode Période au format YYYYMM
     * @return Données consolidées de réalisation
     */
    @GetMapping("/consolide")
    @JsonView(View.basic.class)
    public ResponseEntity<RealisationResponse> getDonneesRealisation(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE REALISATION CONSOLIDE ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            RealisationResponse response = realisationService.getDonneesRealisation(date);
            
            if (response == null) {
                System.out.println("Aucune donnée de réalisation trouvée pour " + periode);
                response = new RealisationResponse(periode); // Retourne une réponse vide mais valide
            }
            
            System.out.println("Données de réalisation retournées avec succès");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("Erreur dans le contrôleur réalisation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les statistiques générales de réalisation
     * @param periode Période au format YYYYMM
     * @return Statistiques générales
     */
    @GetMapping("/statistiques")
    @JsonView(View.basic.class)
    public ResponseEntity<StatistiquesGeneralesDto> getStatistiquesGenerales(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE STATISTIQUES REALISATION ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            StatistiquesGeneralesDto statistiques = realisationService.getStatistiquesGenerales(date);
            
            if (statistiques == null) {
                System.out.println("Aucune statistique trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Statistiques de réalisation retournées avec succès");
            return ResponseEntity.ok(statistiques);
            
        } catch (Exception e) {
            System.err.println("Erreur dans les statistiques réalisation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les données de production par unité
     * @param periode Période au format YYYYMM
     * @return Liste des données de production par unité
     */
    @GetMapping("/production-unites")
    @JsonView(View.basic.class)
    public ResponseEntity<List<RealisationUniteDto>> getProductionParUnite(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE PRODUCTION PAR UNITE ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            List<RealisationUniteDto> unites = realisationService.getProductionParUnite(date);
            
            if (unites == null || unites.isEmpty()) {
                System.out.println("Aucune donnée de production par unité trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Données de production par unité retournées avec succès: " + unites.size() + " unités");
            return ResponseEntity.ok(unites);
            
        } catch (Exception e) {
            System.err.println("Erreur dans la production par unité pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère l'évolution mensuelle de la production
     * @param periode Période au format YYYYMM
     * @param nombreMois Nombre de mois d'historique (défaut: 6)
     * @return Liste de l'évolution mensuelle
     */
    @GetMapping("/evolution")
    @JsonView(View.basic.class)
    public ResponseEntity<List<EvolutionProductionDto>> getEvolutionProduction(
            @RequestParam String periode,
            @RequestParam(defaultValue = "6") Integer nombreMois) {
        try {
            System.out.println("=== REQUETE EVOLUTION PRODUCTION ===");
            System.out.println("Période demandée: " + periode + ", Nombre de mois: " + nombreMois);
            
            LocalDate date = parseDate(periode);
            List<EvolutionProductionDto> evolution = realisationService.getEvolutionProduction(date, nombreMois);
            
            if (evolution == null || evolution.isEmpty()) {
                System.out.println("Aucune donnée d'évolution trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Données d'évolution retournées avec succès: " + evolution.size() + " mois");
            return ResponseEntity.ok(evolution);
            
        } catch (Exception e) {
            System.err.println("Erreur dans l'évolution production pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les totaux de réalisation consolidés
     * @param periode Période au format YYYYMM
     * @return Totaux consolidés
     */
    @GetMapping("/totaux")
    @JsonView(View.basic.class)
    public ResponseEntity<TotauxRealisationDto> getTotauxRealisation(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE TOTAUX REALISATION ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            TotauxRealisationDto totaux = realisationService.getTotauxRealisation(date);
            
            if (totaux == null) {
                System.out.println("Aucun total trouvé pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Totaux de réalisation retournés avec succès");
            return ResponseEntity.ok(totaux);
            
        } catch (Exception e) {
            System.err.println("Erreur dans les totaux réalisation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Test simple pour vérifier que l'endpoint fonctionne
     */
    @GetMapping("/test")
    public ResponseEntity<String> testRealisation() {
        return ResponseEntity.ok("Realisation endpoint is working!");
    }

    /**
     * Parse la date depuis différents formats
     */
    private LocalDate parseDate(String periode) {
        System.out.println("=== PARSING DATE ===");
        System.out.println("Période reçue: '" + periode + "' (longueur: " + periode.length() + ")");

        if (periode.length() == 6) {
            // Format YYYYMM
            String year = periode.substring(0, 4);
            String month = periode.substring(4, 6);
            LocalDate result = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            System.out.println("Format YYYYMM détecté -> " + result);
            return result;
        } else if (periode.length() == 8) {
            // Format ddMMyyyy - extraire mois et année
            String day = periode.substring(0, 2);
            String month = periode.substring(2, 4);
            String year = periode.substring(4, 8);
            LocalDate result = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            System.out.println("Format ddMMyyyy détecté: jour=" + day + ", mois=" + month + ", année=" + year + " -> " + result);
            return result;
        } else {
            throw new IllegalArgumentException("Format de période invalide: '" + periode + "'. Utilisez YYYYMM ou ddMMyyyy");
        }
    }
}
