<div class="layout-container" [ngClass]="containerClass">
    <app-topbar [topBarConfig]="layoutService.config().topBarConfig"></app-topbar>
    @if (layoutService.config().topBarConfig.showRightMneu) {
      <app-rightmenu [component]="layoutService.config().topBarConfig.rightMenuComponent!"></app-rightmenu>
    }
    <app-sidebar></app-sidebar>
    <div class="layout-content-wrapper">

      <!-- <app-breadcrumb></app-breadcrumb> -->
        <div class="layout-content">
            <router-outlet></router-outlet>
        </div>
        <app-footer></app-footer>
    </div>
    <app-config></app-config>

    <div *ngIf="layoutService.state.staticMenuMobileActive" class="layout-mask"></div>
</div>
