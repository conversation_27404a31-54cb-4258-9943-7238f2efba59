package dz.sonatrach.weblqs.mayaaback.repo;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import dz.sonatrach.weblqs.mayaaback.model.MUtilisateur;

public interface UsersRepository extends JpaRepository<MUtilisateur, Long> {
	Optional<MUtilisateur> findByKiduser(String kiduser);
	MUtilisateur findByUsername(String username);
	List<MUtilisateur> findByNomContainingIgnoreCase(String nom);
    List<MUtilisateur> findByUsernameContainingIgnoreCaseAndIdUtilisateurNotIn(String username, List<Long> ids);
    List<MUtilisateur> findByNomContainingIgnoreCaseAndKiduserIsNotNullAndIdUtilisateurNot(String nom, Long idUser);

    @Query("SELECT u FROM MUtilisateur u WHERE " +
    	       "(LOWER(u.username) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
    	       "LOWER(u.nom) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
    	       "LOWER(u.prenom) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
    	       "LOWER(u.matricule) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
    	       "LOWER(CONCAT(u.nom, ' ', u.prenom)) LIKE LOWER(CONCAT('%', :name, '%'))) " +
    	       "AND u.kiduser IS NOT NULL AND u.idUtilisateur <> :idUser")
    List<MUtilisateur> searchUsers(@Param("name") String name, @Param("idUser") Long idUser);

}
