package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.TestDataComplexe;
import dz.sonatrach.weblqs.mayaaback.repo.AutoConsMensuelRepository;
import dz.sonatrach.weblqs.mayaaback.repo.TestDataComplexeRepository;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@RestController
@RequestMapping("api/")
public class LoadDataController {

    @Autowired
    private AutoConsMensuelRepository autoConsMensuelRepository;

    @Autowired
    private TestDataComplexeRepository testDataComplexeRepository;


   @GetMapping("/load/{mois}/{annee}/{unite}")
    @JsonView(View.basic.class)
    @Transactional
    public ResponseEntity<Void> loadAutoCons(@PathVariable String mois, @PathVariable String annee,
                                             @PathVariable String unite) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d/M/yyyy");
        LocalDate localDate = LocalDate.parse("1/" + mois + "/" + annee, formatter);
        java.sql.Date vMois = java.sql.Date.valueOf(localDate);

        autoConsMensuelRepository.loadAutoCons(unite, vMois);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Vérifie les données de la vue TEST_DATA_COMPLEXE pour une unité et une période données.
     *
     * @param unite Code de l'unité (ex: "5X2")
     * @param pmois Période au format ddMMyyyy (ex: "01122024")
     * @return TestDataComplexe contenant les données de comparaison
     */
    @GetMapping("/status/{unite}/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<TestDataComplexe> verifDataComplexe(@PathVariable String unite, @PathVariable String pmois) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        Optional<TestDataComplexe> testDataComplexe = testDataComplexeRepository.findByPmoisAndUnite(date, unite);

        if (testDataComplexe.isEmpty()) {
            return ResponseEntity.noContent().build();
        }

        return ResponseEntity.ok(testDataComplexe.get());
    }


}
