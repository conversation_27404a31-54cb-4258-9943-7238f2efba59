/**
 * Interface représentant les données de réalisation d'une unité
 * Basée sur la vue REALISATION_UNITE
 */
export interface RealisationUnite {
  /** Code de l'unité (ex: "5X2", "5X3") */
  uniteCode: string;
  
  /** Nom de l'unité (ex: "GL1Z", "GL2Z") */
  unite?: string;
  
  /** Période au format Date */
  pmois: string | Date;
  
  /** Objectif de production GNL */
  objectifProductionGnl: number;
  
  /** Prévision de production GNL */
  previsionProductionGnl: number;
  
  /** Production GNL réelle */
  reelProductionGnl: number;
  
  /** Statut de l'unité (actif, arrêt total, etc.) */
  statut?: 'ACTIF' | 'ARRET_TOTAL' | 'MAINTENANCE';
}

/**
 * Interface pour les données consolidées de répartition du GN reçu
 */
export interface RepartitionGnRecu {
  /** Code de l'unité */
  uniteCode: string;
  
  /** Nom de l'unité */
  unite: string;
  
  /** GN transformé (en 10³ m³) */
  gnTransforme: number;
  
  /** Gaz torché (en 10³ m³) */
  gazTorche: number;
  
  /** Autoconsommation nette (en 10³ m³) */
  autoconsommationNette: number;
  
  /** Total GN reçu (en 10³ m³) */
  totalGnRecu: number;
  
  /** Pourcentage GN transformé */
  pourcentageGnTransforme: number;
  
  /** Pourcentage gaz torché */
  pourcentageGazTorche: number;
  
  /** Pourcentage autoconsommation nette */
  pourcentageAutoconsommationNette: number;
  
  /** Statut de l'unité */
  statut: 'ACTIF' | 'ARRET_TOTAL' | 'MAINTENANCE';
}

/**
 * Interface pour les données consolidées de production GNL
 */
export interface ProductionGnlConsolidee {
  /** Code de l'unité */
  uniteCode: string;
  
  /** Nom de l'unité */
  unite: string;
  
  /** Production réelle */
  productionReelle: number;
  
  /** Objectif de production */
  objectifProduction: number;
  
  /** Prévision de production */
  previsionProduction: number;
  
  /** Écart par rapport à l'objectif (%) */
  ecartObjectif: number;
  
  /** Écart par rapport à la prévision (%) */
  ecartPrevision: number;
  
  /** Taux de réalisation par rapport à l'objectif (%) */
  tauxRealisationObjectif: number;
  
  /** Taux de réalisation par rapport à la prévision (%) */
  tauxRealisationPrevision: number;
  
  /** Statut de l'unité */
  statut: 'ACTIF' | 'ARRET_TOTAL' | 'MAINTENANCE';
}

/**
 * Interface pour les totaux consolidés
 */
export interface TotalConsolide {
  /** Total production réelle */
  totalProductionReelle: number;
  
  /** Total objectif */
  totalObjectif: number;
  
  /** Total prévision */
  totalPrevision: number;
  
  /** Total GN transformé */
  totalGnTransforme: number;
  
  /** Total gaz torché */
  totalGazTorche: number;
  
  /** Total autoconsommation nette */
  totalAutoconsommationNette: number;
  
  /** Total GN reçu */
  totalGnRecu: number;
  
  /** Taux global de réalisation objectif (%) */
  tauxGlobalRealisationObjectif: number;
  
  /** Taux global de réalisation prévision (%) */
  tauxGlobalRealisationPrevision: number;
  
  /** Pourcentage global GN transformé */
  pourcentageGlobalGnTransforme: number;
  
  /** Pourcentage global gaz torché */
  pourcentageGlobalGazTorche: number;
  
  /** Pourcentage global autoconsommation nette */
  pourcentageGlobalAutoconsommationNette: number;
}
