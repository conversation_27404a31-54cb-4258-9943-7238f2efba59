package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.GazTorchDetails;

@Repository
public interface GazTorchDetailsRepository extends JpaRepository<GazTorchDetails, Long> {
    List<GazTorchDetails> findByPmoisAndUnite(LocalDate pMois, String unite);
    
}