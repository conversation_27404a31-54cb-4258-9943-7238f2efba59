package dz.sonatrach.weblqs.mayaaback.dto;

import com.fasterxml.jackson.annotation.JsonView;
import dz.sonatrach.weblqs.mayaaback.views.View;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * DTO pour le rapport mensuel d'analyse de l'autoconsommation
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RapportMensuelDto {

    @JsonView(View.basic.class)
    private String periode; // Format "Novembre-2024"

    @JsonView(View.basic.class)
    private LocalDate mois; // Date du mois

    @JsonView(View.basic.class)
    private String numeroReference; // Ex: "N°2.24/GL1K TEC/2024"

    @JsonView(View.basic.class)
    private DonneesIntroduction introduction;

    @JsonView(View.basic.class)
    private BilanGlobalComplexe bilanGlobal;

    @JsonView(View.basic.class)
    private PerformancesComplexe performances;

    @JsonView(View.basic.class)
    private AnalyseExcesEnergie analyseExcesEnergie;

    @JsonView(View.basic.class)
    private AnalyseExcesGazTorche analyseExcesGazTorche;

    @JsonView(View.basic.class)
    private EvolutionSiegesCauses evolutionSieges;

    @JsonView(View.basic.class)
    private EvolutionClassesCauses evolutionClasses;

    @JsonView(View.basic.class)
    private SuiviActionsEntreprises suiviActions;

    @JsonView(View.basic.class)
    private ActionsFinalisees actionsFinalisees;

    @JsonView(View.basic.class)
    private AnnexesRapport annexes;

    @JsonView(View.basic.class)
    private List<EvenementMajeur> evenementsMajeurs;

    /**
     * Données d'introduction du rapport
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DonneesIntroduction {
        private BigDecimal autoconsommationTotale; // 70 919 632 m³
        private BigDecimal pourcentageEquivalentGnRecu; // 11,46%
        private BigDecimal diminutionParRapportDesign; // 2,12%
        private BigDecimal autoconsommationNette; // 64 430 523 m³
        private BigDecimal pourcentageAutoconsommationNette; // 10,42%
        private BigDecimal pourcentageCausesInternes; // 9,95%
        private BigDecimal volumeGazTorche; // 6 489 109 m³
        private BigDecimal pourcentageGazTorche; // 1,05%
        private BigDecimal pourcentageCausesExternesGazTorche; // 0,00%
    }

    /**
     * Bilan global du complexe
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BilanGlobalComplexe {
        private BigDecimal gnRecu; // GN reçu total
        private BigDecimal designPourcentage; // % Design
        private BigDecimal consommationNette; // Consommation nette
        private BigDecimal totalConsommationNetteCauseInterne;
        private BigDecimal totalConsommationNetteCauseExterne;
        private BigDecimal totalGazTorches;
        private BigDecimal totalGazTorchesCauseInterne;
        private BigDecimal totalGazTorchesCauseExterne;
        private BigDecimal autoconsommationComplexeGl1k;
        private BigDecimal autoconsommationComplexeCauseInterne;
        private BigDecimal autoconsommationComplexeCauseExterne;
    }

    /**
     * Performances du complexe
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformancesComplexe {
        private Integer nombreTrainsMarche;
        private BigDecimal production; // Production GNL
        private BigDecimal productionDesign; // Production design
        private BigDecimal tp; // Taux de production
        private BigDecimal tf; // Taux de fonctionnement
        private BigDecimal tc; // Taux de charge
        private BigDecimal tap; // Taux d'arrêt programmé
        private BigDecimal tai; // Taux d'arrêt imprévu
    }

    /**
     * Analyse excès énergie interne
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalyseExcesEnergie {
        private List<ExcesEnergieUnite> unites;
        private BigDecimal totalExces;
        private BigDecimal pourcentageTotal;
    }

    /**
     * Analyse excès gaz torché
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalyseExcesGazTorche {
        private List<ExcesGazTorcheUnite> unites;
        private BigDecimal totalExces;
        private BigDecimal pourcentageTotal;
    }

    /**
     * Évolution des sièges de causes
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EvolutionSiegesCauses {
        private List<EvolutionSiegeCause> sieges;
        private List<String> moisLabels; // Jan, Fév, Mar...
    }

    /**
     * Évolution des classes de causes
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EvolutionClassesCauses {
        private List<EvolutionClasseCause> classes;
        private List<String> moisLabels;
    }

    /**
     * Suivi des actions entreprises
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SuiviActionsEntreprises {
        private List<ActionEntreprise> actions;
        private Integer nombreActionsEnCours;
        private Integer nombreActionsTerminees;
    }

    /**
     * Actions finalisées
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionsFinalisees {
        private List<ActionFinalisee> actions;
        private Integer nombreActionsFinalisees;
    }

    /**
     * Annexes du rapport
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnnexesRapport {
        private BilanEnergiesAnnexe bilanEnergies; // Annexe 1
        private TableauSiegesAnnexe tableauSieges; // Annexe 2
    }

    // Classes internes pour les détails
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExcesEnergieUnite {
        private String unite;
        private BigDecimal exces;
        private BigDecimal pourcentage;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExcesGazTorcheUnite {
        private String unite;
        private BigDecimal exces;
        private BigDecimal pourcentage;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EvolutionSiegeCause {
        private String siege;
        private List<BigDecimal> valeursMensuelles; // 12 mois
        private BigDecimal total;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EvolutionClasseCause {
        private String classe;
        private List<BigDecimal> valeursMensuelles; // 12 mois
        private BigDecimal total;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionEntreprise {
        private String description;
        private String responsable;
        private LocalDate dateEcheance;
        private String statut;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionFinalisee {
        private String description;
        private String responsable;
        private LocalDate dateRealisation;
        private String resultat;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EvenementMajeur {
        private LocalDate date;
        private String description;
        private String impact;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BilanEnergiesAnnexe {
        private List<BilanEnergieItem> items;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableauSiegesAnnexe {
        private List<SiegeAutoconsommationItem> items;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BilanEnergieItem {
        private String type;
        private BigDecimal entree;
        private BigDecimal evolution;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SiegeAutoconsommationItem {
        private String siege;
        private BigDecimal quantite;
        private BigDecimal pourcentage;
    }
}
