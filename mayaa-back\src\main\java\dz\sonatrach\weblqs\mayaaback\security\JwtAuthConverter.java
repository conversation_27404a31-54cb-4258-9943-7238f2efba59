package dz.sonatrach.weblqs.mayaaback.security;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

@Component
public class JwtAuthConverter implements Converter<Jwt, AbstractAuthenticationToken> {
    private final JwtGrantedAuthoritiesConverter jwtGrantedAuthoritiesConverter=new JwtGrantedAuthoritiesConverter();
    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        Collection<GrantedAuthority> authorities = Stream.concat(
                jwtGrantedAuthoritiesConverter.convert(jwt).stream(),
                extractResourceRoles(jwt).stream()
        ).collect(Collectors.toSet());
        return new JwtAuthenticationToken(jwt, authorities,jwt.getClaim("preferred_username"));
    }
    private Collection<GrantedAuthority> extractResourceRoles(Jwt jwt) {
        Collection<String> permissions;
        Map<String, Object> resourceAccess = jwt.getClaim("resource_access");

        // Extract roles for "mayaa-app"
        Map<String, Object> rokhssaApp = (Map<String, Object>) resourceAccess.get("mayaa");

        List<String> allRolesAndPermissions = (List<String>) rokhssaApp.get("roles");
        
        permissions = allRolesAndPermissions.stream()
	            .filter(role -> role.startsWith("$"))
	            .collect(Collectors.toList());
        return permissions.stream().map(role->new SimpleGrantedAuthority(role)).collect(Collectors.toSet());
    }

}