{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-menu.mjs"], "sourcesContent": ["import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Pipe, Inject, EventEmitter, forwardRef, Component, ViewEncapsulation, Input, Output, computed, signal, booleanAttribute, numberAttribute, ChangeDetectionStrategy, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i6 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from '@angular/platform-browser';\nconst _c0 = [\"pMenuItemContent\", \"\"];\nconst _c1 = a0 => ({\n  \"p-disabled\": a0\n});\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nfunction MenuItemContent_ng_container_1_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MenuItemContent_ng_container_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const itemContent_r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"target\", ctx_r1.item.target)(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx_r1.item.disabled));\n    i0.ɵɵattribute(\"title\", ctx_r1.item.title)(\"href\", ctx_r1.item.url || null, i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r1.item.automationId)(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", itemContent_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(11, _c2, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_container_1_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MenuItemContent_ng_container_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 8);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_2_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const itemContent_r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.item.routerLink)(\"queryParams\", ctx_r1.item.queryParams)(\"routerLinkActiveOptions\", ctx_r1.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(17, _c3))(\"target\", ctx_r1.item.target)(\"ngClass\", i0.ɵɵpureFunction1(18, _c1, ctx_r1.item.disabled))(\"fragment\", ctx_r1.item.fragment)(\"queryParamsHandling\", ctx_r1.item.queryParamsHandling)(\"preserveFragment\", ctx_r1.item.preserveFragment)(\"skipLocationChange\", ctx_r1.item.skipLocationChange)(\"replaceUrl\", ctx_r1.item.replaceUrl)(\"state\", ctx_r1.item.state);\n    i0.ɵɵattribute(\"data-automationid\", ctx_r1.item.automationId)(\"tabindex\", -1)(\"data-pc-section\", \"action\")(\"title\", ctx_r1.item.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", itemContent_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(20, _c2, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_1_Template, 2, 13, \"a\", 4)(2, MenuItemContent_ng_container_1_a_2_Template, 2, 22, \"a\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.item == null ? null : ctx_r1.item.routerLink));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item == null ? null : ctx_r1.item.routerLink);\n  }\n}\nfunction MenuItemContent_ng_container_2_1_ng_template_0_Template(rf, ctx) {}\nfunction MenuItemContent_ng_container_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenuItemContent_ng_container_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenuItemContent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_2_1_Template, 1, 0, null, 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.item.iconClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.icon)(\"ngStyle\", ctx_r1.item.iconStyle);\n  }\n}\nfunction MenuItemContent_ng_template_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.item.label);\n  }\n}\nfunction MenuItemContent_ng_template_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, ctx_r1.item.label), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MenuItemContent_ng_template_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.item.badge);\n  }\n}\nfunction MenuItemContent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenuItemContent_ng_template_3_span_0_Template, 1, 4, \"span\", 9)(1, MenuItemContent_ng_template_3_span_1_Template, 2, 1, \"span\", 10)(2, MenuItemContent_ng_template_3_ng_template_2_Template, 2, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, MenuItemContent_ng_template_3_span_4_Template, 2, 2, \"span\", 11);\n  }\n  if (rf & 2) {\n    const htmlLabel_r4 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.escape !== false)(\"ngIfElse\", htmlLabel_r4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.badge);\n  }\n}\nconst _c4 = [\"list\"];\nconst _c5 = [\"container\"];\nconst _c6 = a0 => ({\n  \"p-menu p-component\": true,\n  \"p-menu-overlay\": a0\n});\nconst _c7 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c8 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c9 = a0 => ({\n  \"p-hidden\": a0\n});\nconst _c10 = (a0, a1) => ({\n  \"p-hidden\": a0,\n  flex: a1\n});\nconst _c11 = (a0, a1, a2) => ({\n  \"p-hidden\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nfunction Menu_div_0_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, Menu_div_0_div_2_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"start\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 14);\n  }\n  if (rf & 2) {\n    const submenu_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c9, submenu_r3.visible === false));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const submenu_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(submenu_r3.label);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n  if (rf & 2) {\n    const submenu_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, submenu_r3.label), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_li_1_ng_container_1_span_1_Template, 2, 1, \"span\", 17)(2, Menu_div_0_5_ng_template_0_li_1_ng_container_1_ng_template_2_Template, 2, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlSubmenuLabel_r4 = i0.ɵɵreference(3);\n    const submenu_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", submenu_r3.escape !== false)(\"ngIfElse\", htmlSubmenuLabel_r4);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 15);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_li_1_ng_container_1_Template, 4, 2, \"ng-container\", 7)(2, Menu_div_0_5_ng_template_0_li_1_ng_container_2_Template, 1, 0, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const submenu_r3 = ctx_r4.$implicit;\n    const i_r6 = ctx_r4.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c10, submenu_r3.visible === false, submenu_r3.visible))(\"tooltipOptions\", submenu_r3.tooltipOptions);\n    i0.ɵɵattribute(\"data-automationid\", submenu_r3.automationId)(\"id\", ctx_r1.menuitemId(submenu_r3, ctx_r1.id, i_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.submenuHeaderTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.submenuHeaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c2, submenu_r3));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 14);\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const submenu_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c9, item_r7.visible === false || submenu_r3.visible === false));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 20);\n    i0.ɵɵlistener(\"onMenuItemClick\", function Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template_li_onMenuItemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r8 = i0.ɵɵnextContext();\n      const item_r7 = ctx_r8.$implicit;\n      const j_r10 = ctx_r8.index;\n      const i_r6 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.itemClick($event, ctx_r1.menuitemId(item_r7, ctx_r1.id, i_r6, j_r10)));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const item_r7 = ctx_r8.$implicit;\n    const j_r10 = ctx_r8.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    const submenu_r3 = ctx_r4.$implicit;\n    const i_r6 = ctx_r4.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(item_r7.styleClass);\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r7)(\"itemTemplate\", ctx_r1.itemTemplate)(\"ngClass\", i0.ɵɵpureFunction3(13, _c11, item_r7.visible === false || submenu_r3.visible === false, ctx_r1.focusedOptionId() && ctx_r1.menuitemId(item_r7, ctx_r1.id, i_r6, j_r10) === ctx_r1.focusedOptionId(), ctx_r1.disabled(item_r7.disabled)))(\"ngStyle\", item_r7.style)(\"tooltipOptions\", item_r7.tooltipOptions);\n    i0.ɵɵattribute(\"data-pc-section\", \"menuitem\")(\"aria-label\", ctx_r1.label(item_r7.label))(\"data-p-focused\", ctx_r1.isItemFocused(ctx_r1.menuitemId(item_r7, ctx_r1.id, i_r6, j_r10)))(\"data-p-disabled\", ctx_r1.disabled(item_r7.disabled))(\"aria-disabled\", ctx_r1.disabled(item_r7.disabled))(\"id\", ctx_r1.menuitemId(item_r7, ctx_r1.id, i_r6, j_r10));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_ng_template_2_li_0_Template, 1, 3, \"li\", 12)(1, Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template, 1, 17, \"li\", 19);\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r7.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r7.separator);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_li_0_Template, 1, 3, \"li\", 12)(1, Menu_div_0_5_ng_template_0_li_1_Template, 3, 12, \"li\", 13)(2, Menu_div_0_5_ng_template_0_ng_template_2_Template, 2, 2, \"ng-template\", 11);\n  }\n  if (rf & 2) {\n    const submenu_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", submenu_r3.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !submenu_r3.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", submenu_r3.items);\n  }\n}\nfunction Menu_div_0_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_Template, 3, 3, \"ng-template\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.model);\n  }\n}\nfunction Menu_div_0_6_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 14);\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c9, item_r11.visible === false));\n  }\n}\nfunction Menu_div_0_6_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 20);\n    i0.ɵɵlistener(\"onMenuItemClick\", function Menu_div_0_6_ng_template_0_li_1_Template_li_onMenuItemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r12 = i0.ɵɵnextContext();\n      const item_r11 = ctx_r12.$implicit;\n      const i_r14 = ctx_r12.index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.itemClick($event, ctx_r1.menuitemId(item_r11, ctx_r1.id, i_r14)));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    const item_r11 = ctx_r12.$implicit;\n    const i_r14 = ctx_r12.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(item_r11.styleClass);\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r11)(\"itemTemplate\", ctx_r1.itemTemplate)(\"ngClass\", i0.ɵɵpureFunction3(13, _c11, item_r11.visible === false, ctx_r1.focusedOptionId() && ctx_r1.menuitemId(item_r11, ctx_r1.id, i_r14, ctx_r1.j) === ctx_r1.focusedOptionId(), ctx_r1.disabled(item_r11.disabled)))(\"ngStyle\", item_r11.style)(\"tooltipOptions\", item_r11.tooltipOptions);\n    i0.ɵɵattribute(\"data-pc-section\", \"menuitem\")(\"aria-label\", ctx_r1.label(item_r11.label))(\"data-p-focused\", ctx_r1.isItemFocused(ctx_r1.menuitemId(item_r11, ctx_r1.id, i_r14)))(\"data-p-disabled\", ctx_r1.disabled(item_r11.disabled))(\"aria-disabled\", ctx_r1.disabled(item_r11.disabled))(\"id\", ctx_r1.menuitemId(item_r11, ctx_r1.id, i_r14));\n  }\n}\nfunction Menu_div_0_6_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_6_ng_template_0_li_0_Template, 1, 3, \"li\", 12)(1, Menu_div_0_6_ng_template_0_li_1_Template, 1, 17, \"li\", 19);\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r11.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r11.separator);\n  }\n}\nfunction Menu_div_0_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_6_ng_template_0_Template, 2, 2, \"ng-template\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.model);\n  }\n}\nfunction Menu_div_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, Menu_div_0_div_7_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"end\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate);\n  }\n}\nfunction Menu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4, 0);\n    i0.ɵɵlistener(\"click\", function Menu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function Menu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Menu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Menu_div_0_div_2_Template, 2, 2, \"div\", 5);\n    i0.ɵɵelementStart(3, \"ul\", 6, 1);\n    i0.ɵɵlistener(\"focus\", function Menu_div_0_Template_ul_focus_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onListFocus($event));\n    })(\"blur\", function Menu_div_0_Template_ul_blur_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onListBlur($event));\n    })(\"keydown\", function Menu_div_0_Template_ul_keydown_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onListKeyDown($event));\n    });\n    i0.ɵɵtemplate(5, Menu_div_0_5_Template, 1, 1, null, 7)(6, Menu_div_0_6_Template, 1, 1, null, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, Menu_div_0_div_7_Template, 2, 2, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c6, ctx_r1.popup))(\"ngStyle\", ctx_r1.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(23, _c8, i0.ɵɵpureFunction2(20, _c7, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"@.disabled\", ctx_r1.popup !== true);\n    i0.ɵɵattribute(\"data-pc-name\", \"menu\")(\"id\", ctx_r1.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.startTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"_list\")(\"tabindex\", ctx_r1.getTabIndexValue())(\"data-pc-section\", \"menu\")(\"aria-activedescendant\", ctx_r1.activedescendant())(\"aria-label\", ctx_r1.ariaLabel)(\"aria-labelledBy\", ctx_r1.ariaLabelledBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubMenu());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubMenu());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.endTemplate);\n  }\n}\nclass SafeHtmlPipe {\n  platformId;\n  sanitizer;\n  constructor(platformId, sanitizer) {\n    this.platformId = platformId;\n    this.sanitizer = sanitizer;\n  }\n  transform(value) {\n    if (!value || !isPlatformBrowser(this.platformId)) {\n      return value;\n    }\n    return this.sanitizer.bypassSecurityTrustHtml(value);\n  }\n  static ɵfac = function SafeHtmlPipe_Factory(t) {\n    return new (t || SafeHtmlPipe)(i0.ɵɵdirectiveInject(PLATFORM_ID, 16), i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"safeHtml\",\n    type: SafeHtmlPipe,\n    pure: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SafeHtmlPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'safeHtml'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i1.DomSanitizer\n  }], null);\n})();\nclass MenuItemContent {\n  item;\n  itemTemplate;\n  onMenuItemClick = new EventEmitter();\n  menu;\n  constructor(menu) {\n    this.menu = menu;\n  }\n  onItemClick(event, item) {\n    this.onMenuItemClick.emit({\n      originalEvent: event,\n      item\n    });\n  }\n  static ɵfac = function MenuItemContent_Factory(t) {\n    return new (t || MenuItemContent)(i0.ɵɵdirectiveInject(forwardRef(() => Menu)));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MenuItemContent,\n    selectors: [[\"\", \"pMenuItemContent\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      item: [i0.ɵɵInputFlags.None, \"pMenuItemContent\", \"item\"],\n      itemTemplate: \"itemTemplate\"\n    },\n    outputs: {\n      onMenuItemClick: \"onMenuItemClick\"\n    },\n    attrs: _c0,\n    decls: 5,\n    vars: 3,\n    consts: [[\"itemContent\", \"\"], [\"htmlLabel\", \"\"], [1, \"p-menuitem-content\", 3, \"click\"], [4, \"ngIf\"], [\"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"routerLinkActive\", \"p-menuitem-link-active\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"target\", \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"routerLinkActive\", \"p-menuitem-link-active\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"]],\n    template: function MenuItemContent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 2);\n        i0.ɵɵlistener(\"click\", function MenuItemContent_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event, ctx.item));\n        });\n        i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_Template, 3, 2, \"ng-container\", 3)(2, MenuItemContent_ng_container_2_Template, 2, 4, \"ng-container\", 3)(3, MenuItemContent_ng_template_3_Template, 5, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-pc-section\", \"content\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.itemTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.itemTemplate);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.RouterLink, i3.RouterLinkActive, i4.Ripple, SafeHtmlPipe],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuItemContent, [{\n    type: Component,\n    args: [{\n      selector: '[pMenuItemContent]',\n      template: `\n        <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, item)\">\n            <ng-container *ngIf=\"!itemTemplate\">\n                <a\n                    *ngIf=\"!item?.routerLink\"\n                    [attr.title]=\"item.title\"\n                    [attr.href]=\"item.url || null\"\n                    [attr.data-automationid]=\"item.automationId\"\n                    [attr.tabindex]=\"-1\"\n                    [attr.data-pc-section]=\"'action'\"\n                    class=\"p-menuitem-link\"\n                    [target]=\"item.target\"\n                    [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                    pRipple\n                >\n                    <ng-container *ngTemplateOutlet=\"itemContent; context: { $implicit: item }\"></ng-container>\n                </a>\n                <a\n                    *ngIf=\"item?.routerLink\"\n                    [routerLink]=\"item.routerLink\"\n                    [attr.data-automationid]=\"item.automationId\"\n                    [attr.tabindex]=\"-1\"\n                    [attr.data-pc-section]=\"'action'\"\n                    [attr.title]=\"item.title\"\n                    [queryParams]=\"item.queryParams\"\n                    routerLinkActive=\"p-menuitem-link-active\"\n                    [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                    class=\"p-menuitem-link\"\n                    [target]=\"item.target\"\n                    [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                    [fragment]=\"item.fragment\"\n                    [queryParamsHandling]=\"item.queryParamsHandling\"\n                    [preserveFragment]=\"item.preserveFragment\"\n                    [skipLocationChange]=\"item.skipLocationChange\"\n                    [replaceUrl]=\"item.replaceUrl\"\n                    [state]=\"item.state\"\n                    pRipple\n                >\n                    <ng-container *ngTemplateOutlet=\"itemContent; context: { $implicit: item }\"></ng-container>\n                </a>\n            </ng-container>\n\n            <ng-container *ngIf=\"itemTemplate\">\n                <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-template>\n            </ng-container>\n\n            <ng-template #itemContent>\n                <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [class]=\"item.iconClass\" [ngStyle]=\"item.iconStyle\"></span>\n                <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label | safeHtml\"></span></ng-template>\n                <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n            </ng-template>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: Menu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => Menu)]\n    }]\n  }], {\n    item: [{\n      type: Input,\n      args: ['pMenuItemContent']\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    onMenuItemClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Menu is a navigation / command component that supports dynamic and static positioning.\n * @group Components\n */\nclass Menu {\n  document;\n  platformId;\n  el;\n  renderer;\n  cd;\n  config;\n  overlayService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  model;\n  /**\n   * Defines if menu would displayed as a popup.\n   * @group Props\n   */\n  popup;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '.1s linear';\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  /**\n   * Callback to invoke when overlay menu is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when overlay menu is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * Callback to invoke when the list loses focus.\n   * @param {Event} event - blur event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  /**\n   * Callback to invoke when the list receives focus.\n   * @param {Event} event - focus event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  listViewChild;\n  containerViewChild;\n  templates;\n  startTemplate;\n  endTemplate;\n  itemTemplate;\n  submenuHeaderTemplate;\n  container;\n  scrollHandler;\n  documentClickListener;\n  documentResizeListener;\n  preventDocumentDefault;\n  target;\n  visible;\n  focusedOptionId = computed(() => {\n    return this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : null;\n  });\n  focusedOptionIndex = signal(-1);\n  selectedOptionIndex = signal(-1);\n  focused = false;\n  overlayVisible = false;\n  relativeAlign;\n  constructor(document, platformId, el, renderer, cd, config, overlayService) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.id = this.id || UniqueComponentId();\n  }\n  /**\n   * Toggles the visibility of the popup menu.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  toggle(event) {\n    if (this.visible) this.hide();else this.show(event);\n    this.preventDocumentDefault = true;\n  }\n  /**\n   * Displays the popup menu.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  show(event) {\n    if (this.visible && this.target !== event.currentTarget) {\n      this.hide();\n    }\n    this.target = event.currentTarget;\n    this.relativeAlign = event.relativeAlign;\n    this.visible = true;\n    this.preventDocumentDefault = true;\n    this.overlayVisible = true;\n    this.cd.detectChanges();\n  }\n  ngOnInit() {\n    if (!this.popup) {\n      this.bindDocumentClickListener();\n    }\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this.startTemplate = item.template;\n          break;\n        case 'end':\n          this.endTemplate = item.template;\n          break;\n        case 'itemTemplate':\n          this.itemTemplate = item.template;\n          break;\n        case 'submenuheader':\n          this.submenuHeaderTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  getTabIndexValue() {\n    return this.tabindex !== undefined ? this.tabindex.toString() : null;\n  }\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (this.popup) {\n          this.container = event.element;\n          this.moveOnTop();\n          this.onShow.emit({});\n          this.appendOverlay();\n          this.alignOverlay();\n          this.bindDocumentClickListener();\n          this.bindDocumentResizeListener();\n          this.bindScrollListener();\n          DomHandler.focus(this.listViewChild.nativeElement);\n          this.preventDocumentDefault = true;\n        }\n        break;\n      case 'void':\n        this.onOverlayHide();\n        this.onHide.emit({});\n        break;\n    }\n  }\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(event.element);\n        }\n        break;\n    }\n  }\n  alignOverlay() {\n    if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);else DomHandler.absolutePosition(this.container, this.target);\n  }\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n  restoreOverlayAppend() {\n    if (this.container && this.appendTo) {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n  }\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n  /**\n   * Hides the popup menu.\n   * @group Method\n   */\n  hide() {\n    this.visible = false;\n    this.relativeAlign = false;\n    this.cd.detectChanges();\n  }\n  onWindowResize() {\n    if (this.visible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n  menuitemId(item, id, index, childIndex) {\n    return item?.id ?? `${id}_${index}${childIndex !== undefined ? '_' + childIndex : ''}`;\n  }\n  isItemFocused(id) {\n    return this.focusedOptionId() === id;\n  }\n  label(label) {\n    return typeof label === 'function' ? label() : label;\n  }\n  disabled(disabled) {\n    return typeof disabled === 'function' ? disabled() : typeof disabled === 'undefined' ? false : disabled;\n  }\n  activedescendant() {\n    return this.focused ? this.focusedOptionId() : undefined;\n  }\n  onListFocus(event) {\n    if (!this.focused) {\n      this.focused = true;\n      this.onFocus.emit(event);\n    }\n  }\n  onListBlur(event) {\n    if (this.focused) {\n      this.focused = false;\n      this.changeFocusedOptionIndex(-1);\n      this.selectedOptionIndex.set(-1);\n      this.focusedOptionIndex.set(-1);\n      this.onBlur.emit(event);\n    }\n  }\n  onListKeyDown(event) {\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'NumpadEnter':\n        this.onEnterKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Escape':\n      case 'Tab':\n        if (this.popup) {\n          DomHandler.focus(this.target);\n          this.hide();\n        }\n        this.overlayVisible && this.hide();\n        break;\n      default:\n        break;\n    }\n  }\n  onArrowDownKey(event) {\n    const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex());\n    this.changeFocusedOptionIndex(optionIndex);\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey && this.popup) {\n      DomHandler.focus(this.target);\n      this.hide();\n      event.preventDefault();\n    } else {\n      const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex());\n      this.changeFocusedOptionIndex(optionIndex);\n      event.preventDefault();\n    }\n  }\n  onHomeKey(event) {\n    this.changeFocusedOptionIndex(0);\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedOptionIndex(DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]').length - 1);\n    event.preventDefault();\n  }\n  onEnterKey(event) {\n    const element = DomHandler.findSingle(this.containerViewChild.nativeElement, `li[id=\"${`${this.focusedOptionIndex()}`}\"]`);\n    const anchorElement = element && DomHandler.findSingle(element, 'a');\n    this.popup && DomHandler.focus(this.target);\n    anchorElement ? anchorElement.click() : element && element.click();\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  findNextOptionIndex(index) {\n    const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n    const matchedOptionIndex = [...links].findIndex(link => link.id === index);\n    return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n  }\n  findPrevOptionIndex(index) {\n    const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n    const matchedOptionIndex = [...links].findIndex(link => link.id === index);\n    return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n  }\n  changeFocusedOptionIndex(index) {\n    const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n    if (links.length > 0) {\n      let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n      order > -1 && this.focusedOptionIndex.set(links[order].getAttribute('id'));\n    }\n  }\n  itemClick(event, id) {\n    const {\n      originalEvent,\n      item\n    } = event;\n    if (!this.focused) {\n      this.focused = true;\n      this.onFocus.emit();\n    }\n    if (item.disabled) {\n      originalEvent.preventDefault();\n      return;\n    }\n    if (!item.url && !item.routerLink) {\n      originalEvent.preventDefault();\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: originalEvent,\n        item: item\n      });\n    }\n    if (this.popup) {\n      this.hide();\n    }\n    if (!this.popup && this.focusedOptionIndex() !== id) {\n      this.focusedOptionIndex.set(id);\n    }\n  }\n  onOverlayClick(event) {\n    if (this.popup) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n    this.preventDocumentDefault = true;\n  }\n  bindDocumentClickListener() {\n    if (!this.documentClickListener && isPlatformBrowser(this.platformId)) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n        const eventTarget = event.composed ? event.composedPath()[0] : event.target;\n        const isOutsideContainer = this.containerViewChild?.nativeElement && !this.containerViewChild?.nativeElement.contains(eventTarget);\n        const isOutsideTarget = !(this.target && (this.target === eventTarget || this.target.contains(eventTarget)));\n        if (!this.popup && isOutsideContainer && isOutsideTarget) {\n          this.onListBlur(event);\n        }\n        if (this.preventDocumentDefault && this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n          this.hide();\n          this.preventDocumentDefault = false;\n        }\n      });\n    }\n  }\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n  bindDocumentResizeListener() {\n    if (!this.documentResizeListener && isPlatformBrowser(this.platformId)) {\n      const window = this.document.defaultView;\n      this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n    }\n  }\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler && isPlatformBrowser(this.platformId)) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n        if (this.visible) {\n          this.hide();\n        }\n      });\n    }\n    this.scrollHandler?.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  onOverlayHide() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.preventDocumentDefault = false;\n  }\n  ngOnDestroy() {\n    if (this.popup) {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n    if (!this.popup) {\n      this.unbindDocumentClickListener();\n    }\n  }\n  hasSubMenu() {\n    if (this.model) {\n      for (var item of this.model) {\n        if (item.items) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  isItemHidden(item) {\n    if (item.separator) {\n      return item.visible === false || item.items && item.items.some(subitem => subitem.visible !== false);\n    }\n    return item.visible === false;\n  }\n  static ɵfac = function Menu_Factory(t) {\n    return new (t || Menu)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i5.OverlayService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Menu,\n    selectors: [[\"p-menu\"]],\n    contentQueries: function Menu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Menu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      popup: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"popup\", \"popup\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      appendTo: \"appendTo\",\n      autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      id: \"id\",\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\",\n      onBlur: \"onBlur\",\n      onFocus: \"onFocus\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"container\", \"\"], [\"list\", \"\"], [\"htmlSubmenuLabel\", \"\"], [3, \"ngClass\", \"class\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [\"class\", \"p-menu-start\", 4, \"ngIf\"], [\"role\", \"menu\", 1, \"p-menu-list\", \"p-reset\", 3, \"focus\", \"blur\", \"keydown\"], [4, \"ngIf\"], [\"class\", \"p-menu-end\", 4, \"ngIf\"], [1, \"p-menu-start\"], [4, \"ngTemplateOutlet\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menuitem-separator\", \"role\", \"separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-submenu-header\", \"pTooltip\", \"\", \"role\", \"none\", 3, \"ngClass\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menuitem-separator\", 3, \"ngClass\"], [\"pTooltip\", \"\", \"role\", \"none\", 1, \"p-submenu-header\", 3, \"ngClass\", \"tooltipOptions\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"innerHTML\"], [\"class\", \"p-menuitem\", \"pTooltip\", \"\", \"role\", \"menuitem\", 3, \"pMenuItemContent\", \"itemTemplate\", \"ngClass\", \"ngStyle\", \"class\", \"tooltipOptions\", \"onMenuItemClick\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"menuitem\", 1, \"p-menuitem\", 3, \"onMenuItemClick\", \"pMenuItemContent\", \"itemTemplate\", \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [1, \"p-menu-end\"]],\n    template: function Menu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Menu_div_0_Template, 8, 25, \"div\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i6.Tooltip, MenuItemContent, SafeHtmlPipe],\n    styles: [\"@layer primeng{.p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menu, [{\n    type: Component,\n    args: [{\n      selector: 'p-menu',\n      template: `\n        <div\n            #container\n            [ngClass]=\"{ 'p-menu p-component': true, 'p-menu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            *ngIf=\"!popup || visible\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            [attr.data-pc-name]=\"'menu'\"\n            [attr.id]=\"id\"\n        >\n            <div *ngIf=\"startTemplate\" class=\"p-menu-start\" [attr.data-pc-section]=\"'start'\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <ul\n                #list\n                class=\"p-menu-list p-reset\"\n                role=\"menu\"\n                [attr.id]=\"id + '_list'\"\n                [attr.tabindex]=\"getTabIndexValue()\"\n                [attr.data-pc-section]=\"'menu'\"\n                [attr.aria-activedescendant]=\"activedescendant()\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledBy]=\"ariaLabelledBy\"\n                (focus)=\"onListFocus($event)\"\n                (blur)=\"onListBlur($event)\"\n                (keydown)=\"onListKeyDown($event)\"\n            >\n                <ng-template ngFor let-submenu let-i=\"index\" [ngForOf]=\"model\" *ngIf=\"hasSubMenu()\">\n                    <li class=\"p-menuitem-separator\" *ngIf=\"submenu.separator\" [ngClass]=\"{ 'p-hidden': submenu.visible === false }\" role=\"separator\"></li>\n                    <li\n                        class=\"p-submenu-header\"\n                        [attr.data-automationid]=\"submenu.automationId\"\n                        *ngIf=\"!submenu.separator\"\n                        [ngClass]=\"{ 'p-hidden': submenu.visible === false, flex: submenu.visible }\"\n                        pTooltip\n                        [tooltipOptions]=\"submenu.tooltipOptions\"\n                        role=\"none\"\n                        [attr.id]=\"menuitemId(submenu, id, i)\"\n                    >\n                        <ng-container *ngIf=\"!submenuHeaderTemplate\">\n                            <span *ngIf=\"submenu.escape !== false; else htmlSubmenuLabel\">{{ submenu.label }}</span>\n                            <ng-template #htmlSubmenuLabel><span [innerHTML]=\"submenu.label | safeHtml\"></span></ng-template>\n                        </ng-container>\n                        <ng-container *ngTemplateOutlet=\"submenuHeaderTemplate; context: { $implicit: submenu }\"></ng-container>\n                    </li>\n                    <ng-template ngFor let-item let-j=\"index\" [ngForOf]=\"submenu.items\">\n                        <li class=\"p-menuitem-separator\" *ngIf=\"item.separator\" [ngClass]=\"{ 'p-hidden': item.visible === false || submenu.visible === false }\" role=\"separator\"></li>\n                        <li\n                            class=\"p-menuitem\"\n                            *ngIf=\"!item.separator\"\n                            [pMenuItemContent]=\"item\"\n                            [itemTemplate]=\"itemTemplate\"\n                            [ngClass]=\"{ 'p-hidden': item.visible === false || submenu.visible === false, 'p-focus': focusedOptionId() && menuitemId(item, id, i, j) === focusedOptionId(), 'p-disabled': disabled(item.disabled) }\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            (onMenuItemClick)=\"itemClick($event, menuitemId(item, id, i, j))\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                            role=\"menuitem\"\n                            [attr.data-pc-section]=\"'menuitem'\"\n                            [attr.aria-label]=\"label(item.label)\"\n                            [attr.data-p-focused]=\"isItemFocused(menuitemId(item, id, i, j))\"\n                            [attr.data-p-disabled]=\"disabled(item.disabled)\"\n                            [attr.aria-disabled]=\"disabled(item.disabled)\"\n                            [attr.id]=\"menuitemId(item, id, i, j)\"\n                        ></li>\n                    </ng-template>\n                </ng-template>\n                <ng-template ngFor let-item let-i=\"index\" [ngForOf]=\"model\" *ngIf=\"!hasSubMenu()\">\n                    <li class=\"p-menuitem-separator\" *ngIf=\"item.separator\" [ngClass]=\"{ 'p-hidden': item.visible === false }\" role=\"separator\"></li>\n                    <li\n                        class=\"p-menuitem\"\n                        *ngIf=\"!item.separator\"\n                        [pMenuItemContent]=\"item\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [ngClass]=\"{ 'p-hidden': item.visible === false, 'p-focus': focusedOptionId() && menuitemId(item, id, i, j) === focusedOptionId(), 'p-disabled': disabled(item.disabled) }\"\n                        [ngStyle]=\"item.style\"\n                        [class]=\"item.styleClass\"\n                        (onMenuItemClick)=\"itemClick($event, menuitemId(item, id, i))\"\n                        pTooltip\n                        [tooltipOptions]=\"item.tooltipOptions\"\n                        role=\"menuitem\"\n                        [attr.data-pc-section]=\"'menuitem'\"\n                        [attr.aria-label]=\"label(item.label)\"\n                        [attr.data-p-focused]=\"isItemFocused(menuitemId(item, id, i))\"\n                        [attr.data-p-disabled]=\"disabled(item.disabled)\"\n                        [attr.aria-disabled]=\"disabled(item.disabled)\"\n                        [attr.id]=\"menuitemId(item, id, i)\"\n                    ></li>\n                </ng-template>\n            </ul>\n            <div *ngIf=\"endTemplate\" class=\"p-menu-end\" [attr.data-pc-section]=\"'end'\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i5.PrimeNGConfig\n  }, {\n    type: i5.OverlayService\n  }], {\n    model: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    listViewChild: [{\n      type: ViewChild,\n      args: ['list']\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass MenuModule {\n  static ɵfac = function MenuModule_Factory(t) {\n    return new (t || MenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MenuModule,\n    declarations: [Menu, MenuItemContent, SafeHtmlPipe],\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule],\n    exports: [Menu, RouterModule, TooltipModule, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule],\n      exports: [Menu, RouterModule, TooltipModule, SharedModule],\n      declarations: [Menu, MenuItemContent, SafeHtmlPipe]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menu, MenuItemContent, MenuModule, SafeHtmlPipe };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,IAAM,MAAM,SAAO;AAAA,EACjB,cAAc;AAChB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,UAAU,OAAO,KAAK,MAAM,EAAE,WAAc,gBAAgB,GAAG,KAAK,OAAO,KAAK,QAAQ,CAAC;AACvG,IAAG,YAAY,SAAS,OAAO,KAAK,KAAK,EAAE,QAAQ,OAAO,KAAK,OAAO,MAAS,aAAa,EAAE,qBAAqB,OAAO,KAAK,YAAY,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ;AACxL,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,EACvH;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,cAAc,OAAO,KAAK,UAAU,EAAE,eAAe,OAAO,KAAK,WAAW,EAAE,2BAA2B,OAAO,KAAK,2BAA8B,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,KAAK,MAAM,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,EAAE,YAAY,OAAO,KAAK,QAAQ,EAAE,uBAAuB,OAAO,KAAK,mBAAmB,EAAE,oBAAoB,OAAO,KAAK,gBAAgB,EAAE,sBAAsB,OAAO,KAAK,kBAAkB,EAAE,cAAc,OAAO,KAAK,UAAU,EAAE,SAAS,OAAO,KAAK,KAAK;AACzhB,IAAG,YAAY,qBAAqB,OAAO,KAAK,YAAY,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ,EAAE,SAAS,OAAO,KAAK,KAAK;AACrI,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,EACvH;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6CAA6C,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,6CAA6C,GAAG,IAAI,KAAK,CAAC;AAC1I,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,EAAE,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,WAAW;AAC5E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,UAAU;AAAA,EAC3E;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,aAAa;AAAA,EAC/F;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,MAAM,CAAC;AACzE,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,IAAI,CAAC;AAAA,EAC3H;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,KAAK,SAAS;AACnC,IAAG,WAAW,WAAW,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,SAAS;AAAA,EAC7E;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,KAAK;AAAA,EACxC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,OAAO,GAAG,UAAU;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAG,OAAO,KAAK,KAAK,GAAM,cAAc;AAAA,EACvF;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK,eAAe;AACpD,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,KAAK;AAAA,EACxC;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,sDAAsD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,+CAA+C,GAAG,GAAG,QAAQ,EAAE;AAAA,EAC3U;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,KAAK,IAAI;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,WAAW,KAAK,EAAE,YAAY,YAAY;AAC5E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,KAAK,KAAK;AAAA,EACzC;AACF;AACA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,SAAO;AAAA,EACjB,sBAAsB;AAAA,EACtB,kBAAkB;AACpB;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,sBAAsB;AAAA,EACtB,sBAAsB;AACxB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,YAAY;AACd;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,YAAY;AAAA,EACZ,MAAM;AACR;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC5B,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAChB;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0BAA0B,IAAI,KAAK;AAC1C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,EAAE;AACnF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,WAAW,YAAY,KAAK,CAAC;AAAA,EACnF;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,IAAG,UAAU;AACb,IAAG,kBAAkB,WAAW,KAAK;AAAA,EACvC;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,OAAO,GAAG,UAAU;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAG,WAAW,KAAK,GAAM,cAAc;AAAA,EACtF;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,WAAW,WAAW,KAAK,EAAE,YAAY,mBAAmB;AAAA,EACpF;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE;AACvL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,aAAa,OAAO;AAC1B,UAAM,OAAO,OAAO;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,WAAW,YAAY,OAAO,WAAW,OAAO,CAAC,EAAE,kBAAkB,WAAW,cAAc;AACnJ,IAAG,YAAY,qBAAqB,WAAW,YAAY,EAAE,MAAM,OAAO,WAAW,YAAY,OAAO,IAAI,IAAI,CAAC;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,qBAAqB;AACnD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,qBAAqB,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,UAAU,CAAC;AAAA,EACpI;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,QAAQ,YAAY,SAAS,WAAW,YAAY,KAAK,CAAC;AAAA,EAChH;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,mBAAmB,SAAS,qFAAqF,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,YAAM,UAAU,OAAO;AACvB,YAAM,QAAQ,OAAO;AACrB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,QAAQ,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC,CAAC;AAAA,IACpG,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,UAAU,OAAO;AACvB,UAAM,QAAQ,OAAO;AACrB,UAAM,SAAY,cAAc;AAChC,UAAM,aAAa,OAAO;AAC1B,UAAM,OAAO,OAAO;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,UAAU;AAChC,IAAG,WAAW,oBAAoB,OAAO,EAAE,gBAAgB,OAAO,YAAY,EAAE,WAAc,gBAAgB,IAAI,MAAM,QAAQ,YAAY,SAAS,WAAW,YAAY,OAAO,OAAO,gBAAgB,KAAK,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,MAAM,OAAO,gBAAgB,GAAG,OAAO,SAAS,QAAQ,QAAQ,CAAC,CAAC,EAAE,WAAW,QAAQ,KAAK,EAAE,kBAAkB,QAAQ,cAAc;AACtY,IAAG,YAAY,mBAAmB,UAAU,EAAE,cAAc,OAAO,MAAM,QAAQ,KAAK,CAAC,EAAE,kBAAkB,OAAO,cAAc,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,mBAAmB,OAAO,SAAS,QAAQ,QAAQ,CAAC,EAAE,iBAAiB,OAAO,SAAS,QAAQ,QAAQ,CAAC,EAAE,MAAM,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC;AAAA,EACzV;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,wDAAwD,GAAG,IAAI,MAAM,EAAE;AAAA,EACrK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,QAAQ,QAAQ,SAAS;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,QAAQ,SAAS;AAAA,EAC1C;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,0CAA0C,GAAG,IAAI,MAAM,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,EAAE;AAAA,EACxN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,IAAG,WAAW,QAAQ,WAAW,SAAS;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,WAAW,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,WAAW,KAAK;AAAA,EAC3C;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,EAAE;AAAA,EAC/E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,SAAS,YAAY,KAAK,CAAC;AAAA,EACjF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,mBAAmB,SAAS,uEAAuE,QAAQ;AACvH,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc;AACjC,YAAM,WAAW,QAAQ;AACzB,YAAM,QAAQ,QAAQ;AACtB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,QAAQ,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC,CAAC;AAAA,IAC/F,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc;AACjC,UAAM,WAAW,QAAQ;AACzB,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,UAAU;AACjC,IAAG,WAAW,oBAAoB,QAAQ,EAAE,gBAAgB,OAAO,YAAY,EAAE,WAAc,gBAAgB,IAAI,MAAM,SAAS,YAAY,OAAO,OAAO,gBAAgB,KAAK,OAAO,WAAW,UAAU,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,OAAO,gBAAgB,GAAG,OAAO,SAAS,SAAS,QAAQ,CAAC,CAAC,EAAE,WAAW,SAAS,KAAK,EAAE,kBAAkB,SAAS,cAAc;AAChX,IAAG,YAAY,mBAAmB,UAAU,EAAE,cAAc,OAAO,MAAM,SAAS,KAAK,CAAC,EAAE,kBAAkB,OAAO,cAAc,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,mBAAmB,OAAO,SAAS,SAAS,QAAQ,CAAC,EAAE,iBAAiB,OAAO,SAAS,SAAS,QAAQ,CAAC,EAAE,MAAM,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC;AAAA,EAClV;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,0CAA0C,GAAG,IAAI,MAAM,EAAE;AAAA,EACzI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,QAAQ,SAAS,SAAS;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,SAAS,SAAS;AAAA,EAC3C;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,EAAE;AAAA,EAC/E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0BAA0B,IAAI,KAAK;AAC1C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,EAAE;AACnF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,KAAK;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,WAAW;AAAA,EACtD;AACF;AACA,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,yCAAyC,QAAQ;AAC/E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,2BAA2B,SAAS,oEAAoE,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,wBAAwB,MAAM,CAAC;AAAA,IAC9D,CAAC,EAAE,0BAA0B,SAAS,mEAAmE,QAAQ;AAC/G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,sBAAsB,MAAM,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,WAAW,GAAG,2BAA2B,GAAG,GAAG,OAAO,CAAC;AAC1D,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,WAAW,SAAS,SAAS,wCAAwC,QAAQ;AAC9E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,QAAQ,SAAS,uCAAuC,QAAQ;AACjE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,uBAAuB,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,uBAAuB,GAAG,GAAG,MAAM,CAAC;AAC9F,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2BAA2B,GAAG,GAAG,OAAO,CAAC;AAC1D,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,EAAE,qBAAwB,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,OAAO,uBAAuB,OAAO,qBAAqB,CAAC,CAAC,EAAE,cAAc,OAAO,UAAU,IAAI;AAC3Q,IAAG,YAAY,gBAAgB,MAAM,EAAE,MAAM,OAAO,EAAE;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,aAAa;AAC1C,IAAG,UAAU;AACb,IAAG,YAAY,MAAM,OAAO,KAAK,OAAO,EAAE,YAAY,OAAO,iBAAiB,CAAC,EAAE,mBAAmB,MAAM,EAAE,yBAAyB,OAAO,iBAAiB,CAAC,EAAE,cAAc,OAAO,SAAS,EAAE,mBAAmB,OAAO,cAAc;AACxO,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,WAAW,CAAC;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW,CAAC;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,WAAW;AAAA,EAC1C;AACF;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,YAAY,YAAY,WAAW;AACjC,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACjD,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU,wBAAwB,KAAK;AAAA,EACrD;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,GAAG;AAC7C,WAAO,KAAK,KAAK,eAAiB,kBAAkB,aAAa,EAAE,GAAM,kBAAqB,cAAc,EAAE,CAAC;AAAA,EACjH;AAAA,EACA,OAAO,QAA0B,aAAa;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,kBAAkB,IAAI,aAAa;AAAA,EACnC;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,gBAAgB,KAAK;AAAA,MACxB,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAoB,kBAAkB,WAAW,MAAM,IAAI,CAAC,CAAC;AAAA,EAChF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,MAAM,CAAI,WAAa,MAAM,oBAAoB,MAAM;AAAA,MACvD,cAAc;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,sBAAsB,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,WAAW,IAAI,GAAG,UAAU,WAAW,GAAG,MAAM,GAAG,CAAC,oBAAoB,0BAA0B,SAAS,mBAAmB,WAAW,IAAI,GAAG,cAAc,eAAe,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,mBAAmB,GAAG,UAAU,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,oBAAoB,0BAA0B,WAAW,IAAI,GAAG,mBAAmB,GAAG,cAAc,eAAe,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,SAAS,mBAAmB,GAAG,WAAW,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,oBAAoB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,CAAC;AAAA,IACzpC,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,SAAS,SAAS,8CAA8C,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,QAAQ,IAAI,IAAI,CAAC;AAAA,QACzD,CAAC;AACD,QAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC1P,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mBAAmB,SAAS;AAC3C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,YAAY;AACvC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,YAAY;AAAA,MACxC;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,MAAS,kBAAqB,SAAY,YAAe,kBAAqB,QAAQ,YAAY;AAAA,IAChI,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsDV,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,IAAI,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,OAAN,MAAM,MAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,UAAU,IAAI,aAAa;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,SAAS,MAAM;AAC/B,WAAO,KAAK,mBAAmB,MAAM,KAAK,KAAK,mBAAmB,IAAI;AAAA,EACxE,CAAC;AAAA,EACD,qBAAqB,OAAO,EAAE;AAAA,EAC9B,sBAAsB,OAAO,EAAE;AAAA,EAC/B,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB;AAAA,EACA,YAAY,UAAU,YAAY,IAAI,UAAU,IAAI,QAAQ,gBAAgB;AAC1E,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,KAAK,KAAK,MAAM,kBAAkB;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACZ,QAAI,KAAK;AAAS,WAAK,KAAK;AAAA;AAAO,WAAK,KAAK,KAAK;AAClD,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,OAAO;AACV,QAAI,KAAK,WAAW,KAAK,WAAW,MAAM,eAAe;AACvD,WAAK,KAAK;AAAA,IACZ;AACA,SAAK,SAAS,MAAM;AACpB,SAAK,gBAAgB,MAAM;AAC3B,SAAK,UAAU;AACf,SAAK,yBAAyB;AAC9B,SAAK,iBAAiB;AACtB,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,cAAc,KAAK;AACxB;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,wBAAwB,KAAK;AAClC;AAAA,QACF;AACE,eAAK,eAAe,KAAK;AACzB;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,aAAa,SAAY,KAAK,SAAS,SAAS,IAAI;AAAA,EAClE;AAAA,EACA,wBAAwB,OAAO;AAC7B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,YAAI,KAAK,OAAO;AACd,eAAK,YAAY,MAAM;AACvB,eAAK,UAAU;AACf,eAAK,OAAO,KAAK,CAAC,CAAC;AACnB,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,eAAK,0BAA0B;AAC/B,eAAK,2BAA2B;AAChC,eAAK,mBAAmB;AACxB,qBAAW,MAAM,KAAK,cAAc,aAAa;AACjD,eAAK,yBAAyB;AAAA,QAChC;AACA;AAAA,MACF,KAAK;AACH,aAAK,cAAc;AACnB,aAAK,OAAO,KAAK,CAAC,CAAC;AACnB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,sBAAsB,OAAO;AAC3B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,YAAI,KAAK,YAAY;AACnB,sBAAY,MAAM,MAAM,OAAO;AAAA,QACjC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK;AAAe,iBAAW,iBAAiB,KAAK,WAAW,KAAK,MAAM;AAAA;AAAO,iBAAW,iBAAiB,KAAK,WAAW,KAAK,MAAM;AAAA,EAC/I;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,aAAa;AAAQ,aAAK,SAAS,YAAY,KAAK,SAAS,MAAM,KAAK,SAAS;AAAA;AAAO,mBAAW,YAAY,KAAK,WAAW,KAAK,QAAQ;AAAA,IACvJ;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,aAAa,KAAK,UAAU;AACnC,WAAK,SAAS,YAAY,KAAK,GAAG,eAAe,KAAK,SAAS;AAAA,IACjE;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,YAAY;AACnB,kBAAY,IAAI,QAAQ,KAAK,WAAW,KAAK,aAAa,KAAK,OAAO,OAAO,IAAI;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,WAAW,CAAC,WAAW,cAAc,GAAG;AAC/C,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,WAAW,MAAM,IAAI,OAAO,YAAY;AACtC,WAAO,MAAM,MAAM,GAAG,EAAE,IAAI,KAAK,GAAG,eAAe,SAAY,MAAM,aAAa,EAAE;AAAA,EACtF;AAAA,EACA,cAAc,IAAI;AAChB,WAAO,KAAK,gBAAgB,MAAM;AAAA,EACpC;AAAA,EACA,MAAM,OAAO;AACX,WAAO,OAAO,UAAU,aAAa,MAAM,IAAI;AAAA,EACjD;AAAA,EACA,SAAS,UAAU;AACjB,WAAO,OAAO,aAAa,aAAa,SAAS,IAAI,OAAO,aAAa,cAAc,QAAQ;AAAA,EACjG;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,UAAU,KAAK,gBAAgB,IAAI;AAAA,EACjD;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,QAAQ,KAAK,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,yBAAyB,EAAE;AAChC,WAAK,oBAAoB,IAAI,EAAE;AAC/B,WAAK,mBAAmB,IAAI,EAAE;AAC9B,WAAK,OAAO,KAAK,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,KAAK,OAAO;AACd,qBAAW,MAAM,KAAK,MAAM;AAC5B,eAAK,KAAK;AAAA,QACZ;AACA,aAAK,kBAAkB,KAAK,KAAK;AACjC;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,cAAc,KAAK,oBAAoB,KAAK,mBAAmB,CAAC;AACtE,SAAK,yBAAyB,WAAW;AACzC,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,UAAU,KAAK,OAAO;AAC9B,iBAAW,MAAM,KAAK,MAAM;AAC5B,WAAK,KAAK;AACV,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,YAAM,cAAc,KAAK,oBAAoB,KAAK,mBAAmB,CAAC;AACtE,WAAK,yBAAyB,WAAW;AACzC,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,SAAK,yBAAyB,CAAC;AAC/B,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,yBAAyB,WAAW,KAAK,KAAK,mBAAmB,eAAe,yDAAyD,EAAE,SAAS,CAAC;AAC1J,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,UAAU,WAAW,WAAW,KAAK,mBAAmB,eAAe,UAAU,GAAG,KAAK,mBAAmB,CAAC,EAAE,IAAI;AACzH,UAAM,gBAAgB,WAAW,WAAW,WAAW,SAAS,GAAG;AACnE,SAAK,SAAS,WAAW,MAAM,KAAK,MAAM;AAC1C,oBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AACjE,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM,QAAQ,WAAW,KAAK,KAAK,mBAAmB,eAAe,yDAAyD;AAC9H,UAAM,qBAAqB,CAAC,GAAG,KAAK,EAAE,UAAU,UAAQ,KAAK,OAAO,KAAK;AACzE,WAAO,qBAAqB,KAAK,qBAAqB,IAAI;AAAA,EAC5D;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM,QAAQ,WAAW,KAAK,KAAK,mBAAmB,eAAe,yDAAyD;AAC9H,UAAM,qBAAqB,CAAC,GAAG,KAAK,EAAE,UAAU,UAAQ,KAAK,OAAO,KAAK;AACzE,WAAO,qBAAqB,KAAK,qBAAqB,IAAI;AAAA,EAC5D;AAAA,EACA,yBAAyB,OAAO;AAC9B,UAAM,QAAQ,WAAW,KAAK,KAAK,mBAAmB,eAAe,yDAAyD;AAC9H,QAAI,MAAM,SAAS,GAAG;AACpB,UAAI,QAAQ,SAAS,MAAM,SAAS,MAAM,SAAS,IAAI,QAAQ,IAAI,IAAI;AACvE,cAAQ,MAAM,KAAK,mBAAmB,IAAI,MAAM,KAAK,EAAE,aAAa,IAAI,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,UAAU,OAAO,IAAI;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,UAAU;AACjB,oBAAc,eAAe;AAC7B;AAAA,IACF;AACA,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,oBAAc,eAAe;AAAA,IAC/B;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ;AAAA,QACX;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,OAAO;AACd,WAAK,KAAK;AAAA,IACZ;AACA,QAAI,CAAC,KAAK,SAAS,KAAK,mBAAmB,MAAM,IAAI;AACnD,WAAK,mBAAmB,IAAI,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,OAAO;AACd,WAAK,eAAe,IAAI;AAAA,QACtB,eAAe;AAAA,QACf,QAAQ,KAAK,GAAG;AAAA,MAClB,CAAC;AAAA,IACH;AACA,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,4BAA4B;AAC1B,QAAI,CAAC,KAAK,yBAAyB,kBAAkB,KAAK,UAAU,GAAG;AACrE,YAAM,iBAAiB,KAAK,KAAK,KAAK,GAAG,cAAc,gBAAgB;AACvE,WAAK,wBAAwB,KAAK,SAAS,OAAO,gBAAgB,SAAS,WAAS;AAClF,cAAM,cAAc,MAAM,WAAW,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AACrE,cAAM,qBAAqB,KAAK,oBAAoB,iBAAiB,CAAC,KAAK,oBAAoB,cAAc,SAAS,WAAW;AACjI,cAAM,kBAAkB,EAAE,KAAK,WAAW,KAAK,WAAW,eAAe,KAAK,OAAO,SAAS,WAAW;AACzG,YAAI,CAAC,KAAK,SAAS,sBAAsB,iBAAiB;AACxD,eAAK,WAAW,KAAK;AAAA,QACvB;AACA,YAAI,KAAK,0BAA0B,KAAK,kBAAkB,sBAAsB,iBAAiB;AAC/F,eAAK,KAAK;AACV,eAAK,yBAAyB;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,8BAA8B;AAC5B,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,CAAC,KAAK,0BAA0B,kBAAkB,KAAK,UAAU,GAAG;AACtE,YAAM,SAAS,KAAK,SAAS;AAC7B,WAAK,yBAAyB,KAAK,SAAS,OAAO,QAAQ,UAAU,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,IACrG;AAAA,EACF;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAC5B,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,iBAAiB,kBAAkB,KAAK,UAAU,GAAG;AAC7D,WAAK,gBAAgB,IAAI,8BAA8B,KAAK,QAAQ,MAAM;AACxE,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,eAAe,mBAAmB;AAAA,EACzC;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,qBAAqB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B;AACjC,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,QAAQ;AAC3B,aAAK,gBAAgB;AAAA,MACvB;AACA,UAAI,KAAK,aAAa,KAAK,YAAY;AACrC,oBAAY,MAAM,KAAK,SAAS;AAAA,MAClC;AACA,WAAK,qBAAqB;AAC1B,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,OAAO;AACd,eAAS,QAAQ,KAAK,OAAO;AAC3B,YAAI,KAAK,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,YAAY,SAAS,KAAK,SAAS,KAAK,MAAM,KAAK,aAAW,QAAQ,YAAY,KAAK;AAAA,IACrG;AACA,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,OAAO,OAAO,SAAS,aAAa,GAAG;AACrC,WAAO,KAAK,KAAK,OAAS,kBAAkB,QAAQ,GAAM,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,aAAa,GAAM,kBAAqB,cAAc,CAAC;AAAA,EAChS;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,IACtB,gBAAgB,SAAS,oBAAoB,IAAI,KAAK,UAAU;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,WAAW,IAAI,KAAK;AACtC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO,CAAI,WAAa,4BAA4B,SAAS,SAAS,gBAAgB;AAAA,MACtF,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,gBAAgB;AAAA,MACrG,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,eAAe;AAAA,MACpG,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,IAAI;AAAA,MACJ,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,IAChG;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,WAAW,SAAS,GAAG,CAAC,SAAS,gBAAgB,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,GAAG,eAAe,WAAW,GAAG,SAAS,QAAQ,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,SAAS,wBAAwB,QAAQ,aAAa,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,oBAAoB,YAAY,IAAI,QAAQ,QAAQ,GAAG,WAAW,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,wBAAwB,GAAG,SAAS,GAAG,CAAC,YAAY,IAAI,QAAQ,QAAQ,GAAG,oBAAoB,GAAG,WAAW,gBAAgB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,SAAS,cAAc,YAAY,IAAI,QAAQ,YAAY,GAAG,oBAAoB,gBAAgB,WAAW,WAAW,SAAS,kBAAkB,mBAAmB,GAAG,MAAM,GAAG,CAAC,YAAY,IAAI,QAAQ,YAAY,GAAG,cAAc,GAAG,mBAAmB,oBAAoB,gBAAgB,WAAW,WAAW,gBAAgB,GAAG,CAAC,GAAG,YAAY,CAAC;AAAA,IAChqC,UAAU,SAAS,cAAc,IAAI,KAAK;AACxC,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,qBAAqB,GAAG,IAAI,OAAO,CAAC;AAAA,MACvD;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,CAAC,IAAI,SAAS,IAAI,OAAO;AAAA,MACjD;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAY,SAAS,iBAAiB,YAAY;AAAA,IAC1H,QAAQ,CAAC,oUAAoU;AAAA,IAC7U,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACnE,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,4BAA4B,MAAM;AAAA,QACzG,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqGV,YAAY,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,QACpE,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,4BAA4B,MAAM;AAAA,QACzG,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACP,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,oUAAoU;AAAA,IAC/U,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,GAAG;AAC3C,WAAO,KAAK,KAAK,aAAY;AAAA,EAC/B;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,MAAM,iBAAiB,YAAY;AAAA,IAClD,SAAS,CAAC,cAAc,cAAc,cAAc,eAAe,YAAY;AAAA,IAC/E,SAAS,CAAC,MAAM,cAAc,eAAe,YAAY;AAAA,EAC3D,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,cAAc,eAAe,cAAc,cAAc,eAAe,YAAY;AAAA,EAC5H,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,cAAc,eAAe,YAAY;AAAA,MAC/E,SAAS,CAAC,MAAM,cAAc,eAAe,YAAY;AAAA,MACzD,cAAc,CAAC,MAAM,iBAAiB,YAAY;AAAA,IACpD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}