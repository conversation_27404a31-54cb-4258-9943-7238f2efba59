.rapport-mensuel-container {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;

    .card-header {
      padding: 1.5rem;
      border-bottom: none;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

      h2 {
        margin: 0;
        color: white;
      }

      p {
        margin: 0;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .card-body {
      padding: 1.5rem;
    }
  }

  .action-card {
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    &.bg-blue-50 {
      &:hover {
        background-color: #dbeafe !important;
      }
    }

    &.bg-green-50 {
      &:hover {
        background-color: #dcfce7 !important;
      }
    }

    h4 {
      margin: 0 0 0.25rem 0;
      font-size: 1.1rem;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }

    .p-button {
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
      }
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;

    &.status-available {
      background-color: #f0f9ff;
      border: 1px solid #0ea5e9;
      color: #0369a1;
    }

    &.status-unavailable {
      background-color: #fef3c7;
      border: 1px solid #f59e0b;
      color: #92400e;
    }

    &.status-loading {
      background-color: #f8fafc;
      border: 1px solid #64748b;
      color: #475569;
    }

    i {
      font-size: 1.25rem;
      margin-right: 0.75rem;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;

    .info-item {
      padding: 1rem;
      border-radius: 8px;
      border-left: 4px solid;

      &.info-period {
        background-color: #eff6ff;
        border-left-color: #3b82f6;
      }

      &.info-unit {
        background-color: #f9fafb;
        border-left-color: #6b7280;
      }

      h4 {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
      }

      p {
        margin: 0;
        font-size: 1rem;
        font-weight: 500;
      }
    }
  }

  .content-sections {
    .section-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f5f9;

        &:last-child {
          border-bottom: none;
        }

        i {
          color: #10b981;
          margin-right: 0.75rem;
          font-size: 0.875rem;
        }

        span {
          color: #374151;
          font-size: 0.875rem;
        }
      }
    }
  }

  .warning-notice {
    background-color: #fffbeb;
    border: 1px solid #fbbf24;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;

    .warning-content {
      display: flex;
      align-items: flex-start;

      i {
        color: #f59e0b;
        margin-right: 0.75rem;
        margin-top: 0.125rem;
        font-size: 1rem;
      }

      p {
        margin: 0;
        color: #92400e;
        font-size: 0.875rem;
        line-height: 1.5;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 0.5rem;

    .card .card-header {
      padding: 1rem;

      h2 {
        font-size: 1.25rem;
      }
    }

    .card .card-body {
      padding: 1rem;
    }

    .action-card {
      margin-bottom: 1rem;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }
  }

  // Animation pour les boutons de chargement
  .p-button {
    .pi-spin {
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  // Styles pour les messages de statut
  .text-green-500 {
    color: #10b981 !important;
  }

  .text-blue-500 {
    color: #3b82f6 !important;
  }

  .text-orange-500 {
    color: #f59e0b !important;
  }

  .text-primary {
    color: #667eea !important;
  }

  // Amélioration des bordures colorées
  .border-left-3 {
    border-left-width: 3px !important;
    border-left-style: solid !important;
  }

  .border-blue-500 {
    border-color: #3b82f6 !important;
  }

  .border-gray-500 {
    border-color: #6b7280 !important;
  }

  .border-yellow-500 {
    border-color: #eab308 !important;
  }

  // Backgrounds colorés
  .bg-blue-50 {
    background-color: #eff6ff !important;
  }

  .bg-gray-50 {
    background-color: #f9fafb !important;
  }

  .bg-yellow-50 {
    background-color: #fefce8 !important;
  }

  .bg-green-50 {
    background-color: #f0fdf4 !important;
  }
}
