export interface BilanGnGnlData {
  gnRecu: number;           // GN reçu en CM³
  gnTransforme: number;     // GN transformé en CM³
  utilisationInterne: number; // Utilisation interne totale en CM³
  gazTorches: number;       // Gaz torchés en CM³
  autoconsommationNette: number; // Autoconsommation nette en CM³
  
  // Pourcentages calculés
  gnTransformePercent: number;
  utilisationInternePercent: number;
  gazTorchesPercent: number;
  autoconsommationNettePercent: number;
}

export interface ChartDataItem {
  label: string;
  value: number;
  percent: number;
  color: string;
  unit: string;
}

export interface BilanChartData {
  mainChart: ChartDataItem[];
  subChart: ChartDataItem[];
}
