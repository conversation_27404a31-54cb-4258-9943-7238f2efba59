package dz.sonatrach.weblqs.mayaaback.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.model.MUtilisateur;
import dz.sonatrach.weblqs.mayaaback.service.UtilisateurService;
import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.validation.Valid;

@RestController
@RequestMapping("api/shared/users")
@Validated
public class UserCtrl {

	@Autowired
	private UtilisateurService userInfosService;
	


	
	@GetMapping("/k/{kidUser}")	
	@PreAuthorize("hasAnyAuthority('$cfg.users.user.consulter', '$profile.user.consulter')")
	@JsonView(View.DetailUser.class)
	public ResponseEntity<MUtilisateur> getUserBykId(@Valid @PathVariable String kidUser) {
		MUtilisateur Utilisateur = userInfosService.getUserByKId(kidUser);
		return new ResponseEntity<>(Utilisateur, HttpStatus.OK);
	}

	
	
	@PostMapping("/add")
	@PreAuthorize("hasAuthority('$profile.user.ajouter')")
	@JsonView(View.basic.class)
    public ResponseEntity<MUtilisateur> addKuserToDB() {
		MUtilisateur Utilisateur = userInfosService.addKuserToDB();
        return new ResponseEntity<>(Utilisateur,HttpStatus.CREATED);
    }
}
