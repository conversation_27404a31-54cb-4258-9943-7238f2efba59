import { KeycloakService } from 'keycloak-angular';
import {environment} from './environments/environment';

export function initKeycloak(keycloak: KeycloakService) {
  return () =>
    keycloak.init({
      config: {
        url: environment.keycloakUrl,
        realm: environment.keycloakRealmId,
        clientId: environment.keycloakClientId,
      },
      initOptions: {
        onLoad: 'login-required',
        checkLoginIframe: true,
      },
    });
}
