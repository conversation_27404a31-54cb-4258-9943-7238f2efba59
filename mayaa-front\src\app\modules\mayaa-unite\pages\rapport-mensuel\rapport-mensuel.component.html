<div class="rapport-mensuel-container">
  <!-- En-tête -->
  <div class="card mb-4">
    <div class="card-header bg-primary text-white">
      <div class="flex justify-content-between align-items-center">
        <div class="flex align-items-center">
          <i class="pi pi-file-pdf text-2xl mr-3"></i>
          <div>
            <h2 class="text-xl font-bold mb-1">Rapport Mensuel d'Autoconsommation</h2>
            <p class="text-sm opacity-90">Génération du rapport PDF pour {{ formatDateForDisplay(currentMois) }}</p>
          </div>
        </div>
        <p-button 
          icon="pi pi-refresh" 
          [loading]="loading"
          (onClick)="actualiser()"
          styleClass="p-button-outlined p-button-secondary"
          pTooltip="Actualiser les données">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Statut des données -->
  <div class="card mb-4">
    <div class="card-body">
      <h3 class="text-lg font-semibold mb-3 flex align-items-center">
        <i class="pi pi-info-circle mr-2"></i>
        Statut des Données
      </h3>
      
      <div class="flex align-items-center mb-3">
        <i [class]="getStatusIconClass() + ' text-xl mr-3 ' + getStatusColorClass()"></i>
        <div>
          <p class="font-medium mb-1" [class]="getStatusColorClass()">
            {{ getStatusText() }}
          </p>
          <p class="text-sm text-gray-600">{{ messageStatus }}</p>
        </div>
      </div>

      <div class="grid">
        <div class="col-12 md:col-6">
          <div class="bg-blue-50 border-left-3 border-blue-500 p-3 mb-3">
            <h4 class="text-sm font-semibold text-blue-700 mb-2">Période sélectionnée</h4>
            <p class="text-blue-600">{{ formatDateForDisplay(currentMois) }}</p>
          </div>
        </div>
        <div class="col-12 md:col-6">
          <div class="bg-gray-50 border-left-3 border-gray-500 p-3 mb-3">
            <h4 class="text-sm font-semibold text-gray-700 mb-2">Unité</h4>
            <p class="text-gray-600">Complexe GL1K</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions principales -->
  <div class="card mb-4">
    <div class="card-body">
      <h3 class="text-lg font-semibold mb-4 flex align-items-center">
        <i class="pi pi-cog mr-2"></i>
        Actions
      </h3>

      <div class="grid">
        <!-- Prévisualisation -->
        <div class="col-12 md:col-6">
          <div class="action-card bg-blue-50 border-2 border-blue-200 p-4 border-round">
            <div class="flex align-items-center mb-3">
              <i class="pi pi-eye text-blue-500 text-2xl mr-3"></i>
              <div>
                <h4 class="font-semibold text-blue-700">Prévisualiser</h4>
                <p class="text-sm text-blue-600">Voir le rapport dans le navigateur</p>
              </div>
            </div>
            <p-button 
              label="Prévisualiser"
              icon="pi pi-eye"
              [loading]="previewEnCours"
              [disabled]="!rapportDisponible || loading"
              (onClick)="previsualiserRapport()"
              styleClass="p-button-outlined p-button-info w-full">
            </p-button>
          </div>
        </div>

        <!-- Téléchargement -->
        <div class="col-12 md:col-6">
          <div class="action-card bg-green-50 border-2 border-green-200 p-4 border-round">
            <div class="flex align-items-center mb-3">
              <i class="pi pi-download text-green-500 text-2xl mr-3"></i>
              <div>
                <h4 class="font-semibold text-green-700">Télécharger</h4>
                <p class="text-sm text-green-600">Générer et télécharger le PDF</p>
              </div>
            </div>
            <p-button 
              label="Télécharger PDF"
              icon="pi pi-download"
              [loading]="generationEnCours"
              [disabled]="!rapportDisponible || loading"
              (onClick)="genererRapport()"
              styleClass="p-button-success w-full">
            </p-button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Informations sur le rapport -->
  <div class="card">
    <div class="card-body">
      <h3 class="text-lg font-semibold mb-4 flex align-items-center">
        <i class="pi pi-info mr-2"></i>
        Contenu du Rapport
      </h3>

      <div class="grid">
        <div class="col-12 md:col-6">
          <h4 class="font-semibold mb-3 text-primary">Sections Principales</h4>
          <ul class="list-none p-0">
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">1. Introduction</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">2. Bilan global du complexe</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">3. Performances du complexe</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">4. Analyse excès autoconsommation (Énergie)</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">5. Analyse excès autoconsommation (Gaz torchés)</span>
            </li>
          </ul>
        </div>
        <div class="col-12 md:col-6">
          <h4 class="font-semibold mb-3 text-primary">Sections Complémentaires</h4>
          <ul class="list-none p-0">
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">6. Évolution des sièges de causes</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">7. Évolution des classes de causes</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">8. Suivi des actions entreprises</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">9. Actions finalisées</span>
            </li>
            <li class="flex align-items-center mb-2">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-sm">10. Annexes</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="mt-4 p-3 bg-yellow-50 border-left-3 border-yellow-500">
        <div class="flex align-items-center">
          <i class="pi pi-info-circle text-yellow-600 mr-2"></i>
          <p class="text-sm text-yellow-700 mb-0">
            Le rapport est généré automatiquement à partir des données disponibles dans le système.
            Assurez-vous que toutes les données du mois sont saisies avant la génération.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Messages toast -->
<p-toast position="top-right"></p-toast>
