import { Component, OnInit, OnDestroy, effect } from '@angular/core';
import { RapportMensuelService } from '../../services/rapport-mensuel.service';
import { CalendarService } from '../../../../services/calendar.service';
import { MessageService } from 'primeng/api';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-rapport-mensuel',
  templateUrl: './rapport-mensuel.component.html',
  styleUrls: ['./rapport-mensuel.component.scss']
})
export class RapportMensuelComponent implements OnInit, OnDestroy {

  // État du composant
  loading: boolean = false;
  generationEnCours: boolean = false;
  previewEnCours: boolean = false;

  // Données
  currentUnite: string = '5X3';
  currentMois: string = '';
  rapportDisponible: boolean = false;
  messageStatus: string = '';

  // Subscriptions
  private subscriptions: Subscription = new Subscription();

  constructor(
    private rapportService: RapportMensuelService,
    private calendarService: CalendarService,
    private messageService: MessageService
  ) {
    // Écouter les changements de calendrier
    effect(() => {
      const selectedDate = this.calendarService.selectedMonthYear();
      if (selectedDate) {
        this.currentMois = this.formatDateForApi(selectedDate);
        this.verifierDisponibilite();
      }
    });
  }

  ngOnInit(): void {
    // Initialisation avec la date courante si pas de sélection
    if (!this.currentMois) {
      const now = new Date();
      this.currentMois = this.formatDateForApi(now);
      this.verifierDisponibilite();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Formate une date pour l'API (ddMMyyyy)
   */
  private formatDateForApi(date: Date): string {
    const day = '01'; // Premier jour du mois
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  /**
   * Formate une date pour l'affichage (Mois Année)
   */
  public formatDateForDisplay(dateStr: string): string {
    const date = new Date(
      parseInt(dateStr.substring(4, 8)), // année
      parseInt(dateStr.substring(2, 4)) - 1, // mois (0-indexé)
      1
    );
    return date.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' });
  }

  /**
   * Vérifie la disponibilité des données pour le rapport
   */
  verifierDisponibilite(): void {
    if (!this.currentMois) return;

    this.loading = true;

    const subscription = this.rapportService.verifierDisponibilite(this.currentMois)
      .subscribe({
        next: (status) => {
          this.rapportDisponible = status.disponible;
          this.messageStatus = status.message;
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors de la vérification:', error);
          this.rapportDisponible = false;
          this.messageStatus = 'Erreur lors de la vérification des données';
          this.loading = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Erreur',
            detail: 'Impossible de vérifier la disponibilité des données'
          });
        }
      });

    this.subscriptions.add(subscription);
  }

  /**
   * Génère et télécharge le rapport PDF
   */
  genererRapport(): void {
    if (!this.rapportDisponible || this.generationEnCours) return;

    this.generationEnCours = true;

    const subscription = this.rapportService.genererRapport(this.currentMois)
      .subscribe({
        next: (blob) => {
          // Créer un lien de téléchargement
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          const nomFichier = `Rapport_Mensuel_Autoconsommation_${this.formatDateForDisplay(this.currentMois).replace(' ', '_')}.pdf`;
          link.download = nomFichier;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          window.URL.revokeObjectURL(url);

          this.generationEnCours = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Succès',
            detail: 'Rapport généré et téléchargé avec succès'
          });
        },
        error: (error) => {
          console.error('Erreur lors de la génération:', error);
          this.generationEnCours = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Erreur',
            detail: 'Erreur lors de la génération du rapport'
          });
        }
      });

    this.subscriptions.add(subscription);
  }

  /**
   * Prévisualise le rapport dans un nouvel onglet
   */
  previsualiserRapport(): void {
    if (!this.rapportDisponible || this.previewEnCours) return;

    this.previewEnCours = true;

    const subscription = this.rapportService.previsualiserRapport(this.currentMois)
      .subscribe({
        next: (blob) => {
          // Ouvrir le PDF dans un nouvel onglet
          const url = window.URL.createObjectURL(blob);
          window.open(url, '_blank');

          // Nettoyer l'URL après un délai
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
          }, 1000);

          this.previewEnCours = false;
          this.messageService.add({
            severity: 'info',
            summary: 'Prévisualisation',
            detail: 'Rapport ouvert dans un nouvel onglet'
          });
        },
        error: (error) => {
          console.error('Erreur lors de la prévisualisation:', error);
          this.previewEnCours = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Erreur',
            detail: 'Erreur lors de la prévisualisation du rapport'
          });
        }
      });

    this.subscriptions.add(subscription);
  }

  /**
   * Actualise les données
   */
  actualiser(): void {
    this.verifierDisponibilite();
    this.messageService.add({
      severity: 'info',
      summary: 'Actualisation',
      detail: 'Vérification des données en cours...'
    });
  }

  /**
   * Retourne la classe CSS pour l'icône de statut
   */
  getStatusIconClass(): string {
    if (this.loading) return 'pi pi-spin pi-spinner';
    return this.rapportDisponible ? 'pi pi-check-circle' : 'pi pi-exclamation-triangle';
  }

  /**
   * Retourne la classe CSS pour la couleur du statut
   */
  getStatusColorClass(): string {
    if (this.loading) return 'text-blue-500';
    return this.rapportDisponible ? 'text-green-500' : 'text-orange-500';
  }

  /**
   * Retourne le texte du statut
   */
  getStatusText(): string {
    if (this.loading) return 'Vérification en cours...';
    return this.rapportDisponible ? 'Données disponibles' : 'Données incomplètes';
  }
}
