package dz.sonatrach.weblqs.mayaaback.model;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@Entity
@Table(name = "NOMBRE_ARRET_TRAINS_PAR_CLASSE_CAUSE")
public class NombreArretTrainsParClasseCause implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "UNITE", length = 3)
    private String unite;

    @Column(name = "TRAIN", length = 4)
    private String train;

    @Column(name = "PDATE")
    private LocalDate pdate;

    @Column(name = "CODE_CLASSE_CAUSE", length = 6)
    private String codeClasseCause;

    @Column(name = "LIBELLE_CLASSE_CAUSE", length = 90)
    private String libelleClasseCause;

    @Column(name = "NBRE_ARRET")
    private Integer nbreArret;

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUnite() { return unite; }
    public void setUnite(String unite) { this.unite = unite; }

    public String getTrain() { return train; }
    public void setTrain(String train) { this.train = train; }

    public LocalDate getPdate() { return pdate; }
    public void setPdate(LocalDate pdate) { this.pdate = pdate; }

    public String getCodeClasseCause() { return codeClasseCause; }
    public void setCodeClasseCause(String codeClasseCause) { this.codeClasseCause = codeClasseCause; }

    public String getLibelleClasseCause() { return libelleClasseCause; }
    public void setLibelleClasseCause(String libelleClasseCause) { this.libelleClasseCause = libelleClasseCause; }

    public Integer getNbreArret() { return nbreArret; }
    public void setNbreArret(Integer nbreArret) { this.nbreArret = nbreArret; }
}
