/**
 * Interface pour les données d'analyse d'excès des utilités
 */
export interface AnalyseExcesUtilite {
  codeAc: string;
  problemeSpecifique: string;
  intitule: string;
  ac: number; // 10³ CM³ GN
  classeCauses: string;
  causes: string;
  actions: string;
  classes: string;
  etat: string;
  numero: string;
}

/**
 * Interface pour les données d'analyse d'excès des trains
 */
export interface AnalyseExcesTrain {
  codeAc: string;
  problemeSpecifique: string;
  intitule: string;
  ac: number; // 10³ CM³ GN
  classeCauses: string;
  causes: string;
  actions: string;
  classes: string;
  etat: string;
  numero: string;
  trainName: string;
}

/**
 * Interface pour la réponse complète de l'API d'analyse d'excès
 */
export interface AnalyseExcesResponse {
  utilites: AnalyseExcesUtilite[];
  trains: AnalyseExcesTrain[];
}

/**
 * Interface pour les paramètres de requête
 */
export interface AnalyseExcesParams {
  unite: string;
  mois: string; // Format YYYY-MM
}

/**
 * Énumération pour les états possibles
 */
export enum EtatAnalyseExces {
  EN_COURS = 'En cours',
  TERMINE = 'Terminé',
  PLANIFIE = 'Planifié',
  ANNULE = 'Annulé'
}

/**
 * Énumération pour les classes de criticité
 */
export enum ClasseCriticite {
  CRITIQUE = 'Critique',
  MAJEUR = 'Majeur',
  MINEUR = 'Mineur'
}

/**
 * Interface pour les colonnes de tableau
 */
export interface TableColumn {
  field: string;
  header: string;
  sortable?: boolean;
  width?: string;
}
