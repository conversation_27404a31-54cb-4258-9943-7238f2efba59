package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.dto.AutoconsommationResponse;
import dz.sonatrach.weblqs.mayaaback.dto.AutoconsommationResponse.*;
import dz.sonatrach.weblqs.mayaaback.service.AutoconsommationService;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Contrôleur pour les données d'autoconsommation
 */
@RestController
@RequestMapping("api/autoconsommation")

public class AutoconsommationController {

    @Autowired
    private AutoconsommationService autoconsommationService;

    /**
     * Récupère toutes les données d'autoconsommation consolidées
     * @param periode Période au format YYYYMM
     * @return Données consolidées d'autoconsommation
     */
    @GetMapping("/consolide")
    @JsonView(View.basic.class)
    public ResponseEntity<AutoconsommationResponse> getDonneesAutoconsommation(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE AUTOCONSOMMATION CONSOLIDE ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            AutoconsommationResponse response = autoconsommationService.getDonneesAutoconsommation(date);
            
            if (response == null) {
                System.out.println("Aucune donnée d'autoconsommation trouvée pour " + periode);
                response = new AutoconsommationResponse(periode); // Retourne une réponse vide mais valide
            }
            
            System.out.println("Données d'autoconsommation retournées avec succès");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("Erreur dans le contrôleur autoconsommation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les statistiques d'autoconsommation
     * @param periode Période au format YYYYMM
     * @return Statistiques d'autoconsommation
     */
    @GetMapping("/statistiques")
    @JsonView(View.basic.class)
    public ResponseEntity<StatistiquesAutoconsommationDto> getStatistiquesAutoconsommation(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE STATISTIQUES AUTOCONSOMMATION ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            StatistiquesAutoconsommationDto statistiques = autoconsommationService.getStatistiquesAutoconsommation(date);
            
            if (statistiques == null) {
                System.out.println("Aucune statistique d'autoconsommation trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Statistiques d'autoconsommation retournées avec succès");
            return ResponseEntity.ok(statistiques);
            
        } catch (Exception e) {
            System.err.println("Erreur dans les statistiques autoconsommation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les données d'autoconsommation par unité
     * @param periode Période au format YYYYMM
     * @return Liste des données d'autoconsommation par unité
     */
    @GetMapping("/unites")
    @JsonView(View.basic.class)
    public ResponseEntity<List<AutoconsommationUniteDto>> getAutoconsommationParUnite(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE AUTOCONSOMMATION PAR UNITE ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            List<AutoconsommationUniteDto> unites = autoconsommationService.getAutoconsommationParUnite(date);
            
            if (unites == null || unites.isEmpty()) {
                System.out.println("Aucune donnée d'autoconsommation par unité trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Données d'autoconsommation par unité retournées avec succès: " + unites.size() + " unités");
            return ResponseEntity.ok(unites);
            
        } catch (Exception e) {
            System.err.println("Erreur dans l'autoconsommation par unité pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les données de gaz torché par cause
     * @param periode Période au format YYYYMM
     * @return Liste des données de gaz torché par cause
     */
    @GetMapping("/gaz-torche-causes")
    @JsonView(View.basic.class)
    public ResponseEntity<List<GazTorcheCauseDto>> getGazTorcheParCause(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE GAZ TORCHE PAR CAUSE ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            List<GazTorcheCauseDto> causes = autoconsommationService.getGazTorcheParCause(date);
            
            if (causes == null || causes.isEmpty()) {
                System.out.println("Aucune donnée de gaz torché par cause trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Données de gaz torché par cause retournées avec succès: " + causes.size() + " causes");
            return ResponseEntity.ok(causes);
            
        } catch (Exception e) {
            System.err.println("Erreur dans le gaz torché par cause pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère l'évolution mensuelle de l'autoconsommation
     * @param periode Période au format YYYYMM
     * @param nombreMois Nombre de mois d'historique (défaut: 6)
     * @return Liste de l'évolution mensuelle
     */
    @GetMapping("/evolution")
    @JsonView(View.basic.class)
    public ResponseEntity<List<EvolutionAutoconsommationDto>> getEvolutionAutoconsommation(
            @RequestParam String periode,
            @RequestParam(defaultValue = "6") Integer nombreMois) {
        try {
            System.out.println("=== REQUETE EVOLUTION AUTOCONSOMMATION ===");
            System.out.println("Période demandée: " + periode + ", Nombre de mois: " + nombreMois);
            
            LocalDate date = parseDate(periode);
            List<EvolutionAutoconsommationDto> evolution = autoconsommationService.getEvolutionAutoconsommation(date, nombreMois);
            
            if (evolution == null || evolution.isEmpty()) {
                System.out.println("Aucune donnée d'évolution d'autoconsommation trouvée pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Données d'évolution d'autoconsommation retournées avec succès: " + evolution.size() + " mois");
            return ResponseEntity.ok(evolution);
            
        } catch (Exception e) {
            System.err.println("Erreur dans l'évolution autoconsommation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère les totaux d'autoconsommation consolidés
     * @param periode Période au format YYYYMM
     * @return Totaux consolidés
     */
    @GetMapping("/totaux")
    @JsonView(View.basic.class)
    public ResponseEntity<TotauxAutoconsommationDto> getTotauxAutoconsommation(@RequestParam String periode) {
        try {
            System.out.println("=== REQUETE TOTAUX AUTOCONSOMMATION ===");
            System.out.println("Période demandée: " + periode);
            
            LocalDate date = parseDate(periode);
            TotauxAutoconsommationDto totaux = autoconsommationService.getTotauxAutoconsommation(date);
            
            if (totaux == null) {
                System.out.println("Aucun total d'autoconsommation trouvé pour " + periode);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Totaux d'autoconsommation retournés avec succès");
            return ResponseEntity.ok(totaux);
            
        } catch (Exception e) {
            System.err.println("Erreur dans les totaux autoconsommation pour " + periode + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Test simple pour vérifier que l'endpoint fonctionne
     */
    @GetMapping("/test")
    public ResponseEntity<String> testAutoconsommation() {
        return ResponseEntity.ok("Autoconsommation endpoint is working!");
    }

    /**
     * Parse la date depuis différents formats
     */
    private LocalDate parseDate(String periode) {
        System.out.println("=== PARSING DATE AUTOCONSOMMATION ===");
        System.out.println("Période reçue: '" + periode + "' (longueur: " + periode.length() + ")");

        if (periode.length() == 6) {
            // Format YYYYMM
            String year = periode.substring(0, 4);
            String month = periode.substring(4, 6);
            LocalDate result = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            System.out.println("Format YYYYMM détecté -> " + result);
            return result;
        } else if (periode.length() == 8) {
            // Format ddMMyyyy - extraire mois et année
            String day = periode.substring(0, 2);
            String month = periode.substring(2, 4);
            String year = periode.substring(4, 8);
            LocalDate result = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            System.out.println("Format ddMMyyyy détecté: jour=" + day + ", mois=" + month + ", année=" + year + " -> " + result);
            return result;
        } else {
            throw new IllegalArgumentException("Format de période invalide: '" + periode + "'. Utilisez YYYYMM ou ddMMyyyy");
        }
    }
}
