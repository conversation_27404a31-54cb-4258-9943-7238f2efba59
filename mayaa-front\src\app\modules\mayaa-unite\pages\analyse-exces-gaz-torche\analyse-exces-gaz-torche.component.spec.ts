import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AnalyseExcesGazTorcheComponent } from './analyse-exces-gaz-torche.component';
import { AnalyseExcesGazTorcheService } from '../../services/analyse-exces-gaz-torche.service';
import { AnalyseExcesGazTorche } from '../../../../model/analyse-exces-gaz-torche.model';

// Modules PrimeNG nécessaires pour les tests
import { TableModule } from 'primeng/table';
import { MessageModule } from 'primeng/message';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

describe('AnalyseExcesGazTorcheComponent', () => {
  let component: AnalyseExcesGazTorcheComponent;
  let fixture: ComponentFixture<AnalyseExcesGazTorcheComponent>;
  let mockAnalyseExcesGazTorcheService: jasmine.SpyObj<AnalyseExcesGazTorcheService>;

  // Données mock pour les tests
  const mockGazTorcheData: AnalyseExcesGazTorche[] = [
    {
      codeAc: 'GT001',
      problemeSpecifique: 'Test problème gaz torché',
      intitule: 'Test gaz torché',
      ac: 425.8,
      classeCauses: 'Technique',
      causes: 'Test cause',
      actions: 'Test action',
      classes: 'Critique',
      etat: 'En cours',
      numero: '001',
      trainName: 'Train 100'
    },
    {
      codeAc: 'GT002',
      problemeSpecifique: 'Test problème gaz torché 2',
      intitule: 'Test gaz torché 2',
      ac: 178.3,
      classeCauses: 'Opérationnelle',
      causes: 'Test cause 2',
      actions: 'Test action 2',
      classes: 'Majeur',
      etat: 'Terminé',
      numero: '002',
      trainName: 'Train 200'
    }
  ];

  beforeEach(async () => {
    // Création du spy pour le service
    const spy = jasmine.createSpyObj('AnalyseExcesGazTorcheService', [
      'getGazTorcheData',
      'getGazTorcheDataWithStats',
      'getGlobalStatistics'
    ]);

    await TestBed.configureTestingModule({
      declarations: [AnalyseExcesGazTorcheComponent],
      imports: [
        TableModule,
        MessageModule,
        ProgressSpinnerModule
      ],
      providers: [
        { provide: AnalyseExcesGazTorcheService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AnalyseExcesGazTorcheComponent);
    component = fixture.componentInstance;
    mockAnalyseExcesGazTorcheService = TestBed.inject(AnalyseExcesGazTorcheService) as jasmine.SpyObj<AnalyseExcesGazTorcheService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.gazTorcheData).toEqual([]);
    expect(component.loading).toBeFalse();
    expect(component.currentUnite).toBe('GL1K');
    expect(component.currentMois).toBe('2024-01');
  });

  it('should load data on init', () => {
    // Configuration du mock
    mockAnalyseExcesGazTorcheService.getGazTorcheData.and.returnValue(of(mockGazTorcheData));

    // Appel de ngOnInit
    component.ngOnInit();

    // Vérifications
    expect(mockAnalyseExcesGazTorcheService.getGazTorcheData).toHaveBeenCalledWith('GL1K', '2024-01');
    expect(component.gazTorcheData).toEqual(mockGazTorcheData);
    expect(component.loading).toBeFalse();
  });

  it('should handle loading state correctly', () => {
    // Configuration du mock
    mockAnalyseExcesGazTorcheService.getGazTorcheData.and.returnValue(of(mockGazTorcheData));

    // Vérification de l'état initial
    expect(component.loading).toBeFalse();

    // Déclenchement du chargement
    component.loadData();

    // Vérification que loading est activé pendant le chargement
    expect(component.loading).toBeTrue();
  });

  it('should get unique trains correctly', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], trainName: 'Train 100' },
      { ...mockGazTorcheData[1], trainName: 'Train 200' },
      { ...mockGazTorcheData[0], trainName: 'Train 100' } // Doublon
    ];

    const uniqueTrains = component.getUniqueTrains();

    expect(uniqueTrains).toEqual(['Train 100', 'Train 200']);
    expect(uniqueTrains.length).toBe(2);
  });

  it('should filter train data by train name', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], trainName: 'Train 100' },
      { ...mockGazTorcheData[1], trainName: 'Train 200' },
      { ...mockGazTorcheData[0], trainName: 'Train 100' }
    ];

    const train100Data = component.getTrainData('Train 100');
    const train200Data = component.getTrainData('Train 200');

    expect(train100Data.length).toBe(2);
    expect(train200Data.length).toBe(1);
    expect(train100Data.every(item => item.trainName === 'Train 100')).toBeTrue();
    expect(train200Data.every(item => item.trainName === 'Train 200')).toBeTrue();
  });

  it('should calculate train total AC correctly', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], trainName: 'Train 100', ac: 100 },
      { ...mockGazTorcheData[1], trainName: 'Train 100', ac: 200 },
      { ...mockGazTorcheData[0], trainName: 'Train 200', ac: 150 }
    ];

    const train100Total = component.getTrainTotalAC('Train 100');
    const train200Total = component.getTrainTotalAC('Train 200');

    expect(train100Total).toBe(300);
    expect(train200Total).toBe(150);
  });

  it('should calculate train item count correctly', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], trainName: 'Train 100' },
      { ...mockGazTorcheData[1], trainName: 'Train 100' },
      { ...mockGazTorcheData[0], trainName: 'Train 200' }
    ];

    const train100Count = component.getTrainItemCount('Train 100');
    const train200Count = component.getTrainItemCount('Train 200');

    expect(train100Count).toBe(2);
    expect(train200Count).toBe(1);
  });

  it('should calculate total AC correctly', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], ac: 100 },
      { ...mockGazTorcheData[1], ac: 200 },
      { ...mockGazTorcheData[0], ac: 150 }
    ];

    const totalAC = component.getTotalAC();
    expect(totalAC).toBe(450);
  });

  it('should calculate total items correctly', () => {
    component.gazTorcheData = mockGazTorcheData;

    const totalItems = component.getTotalItems();
    expect(totalItems).toBe(2);
  });

  it('should calculate status distribution correctly', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], etat: 'En cours' },
      { ...mockGazTorcheData[1], etat: 'En cours' },
      { ...mockGazTorcheData[0], etat: 'Terminé' }
    ];

    const distribution = component.getStatusDistribution();
    expect(distribution['En cours']).toBe(2);
    expect(distribution['Terminé']).toBe(1);
  });

  it('should calculate criticality distribution correctly', () => {
    component.gazTorcheData = [
      { ...mockGazTorcheData[0], classes: 'Critique' },
      { ...mockGazTorcheData[1], classes: 'Majeur' },
      { ...mockGazTorcheData[0], classes: 'Critique' }
    ];

    const distribution = component.getCriticalityDistribution();
    expect(distribution['Critique']).toBe(2);
    expect(distribution['Majeur']).toBe(1);
  });

  it('should refresh data when parameters change', () => {
    mockAnalyseExcesGazTorcheService.getGazTorcheData.and.returnValue(of(mockGazTorcheData));

    component.refreshData('GL2K', '2024-02');

    expect(component.currentUnite).toBe('GL2K');
    expect(component.currentMois).toBe('2024-02');
    expect(mockAnalyseExcesGazTorcheService.getGazTorcheData).toHaveBeenCalledWith('GL2K', '2024-02');
  });

  it('should have correct column configurations', () => {
    expect(component.gazTorcheColumns.length).toBe(10);

    // Vérification de quelques colonnes importantes
    const codeAcColumn = component.gazTorcheColumns.find(col => col.field === 'codeAc');
    expect(codeAcColumn).toBeDefined();
    expect(codeAcColumn?.header).toBe('CODE AC');
    expect(codeAcColumn?.sortable).toBeTrue();

    const acColumn = component.gazTorcheColumns.find(col => col.field === 'ac');
    expect(acColumn).toBeDefined();
    expect(acColumn?.header).toBe('AC (10³ CM³ GN)');
    expect(acColumn?.sortable).toBeTrue();
  });
});
