package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.MUnite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository pour l'accès aux données de la table M_UNITE
 */
@Repository
public interface MUniteRepository extends JpaRepository<MUnite, Long> {

    /**
     * Récupère toutes les unités avec leurs groupes Keycloak
     * @return Liste de toutes les unités
     */
    List<MUnite> findAll();

    /**
     * Récupère les unités accessibles pour les groupes Keycloak donnés
     * @param groupIds Liste des IDs de groupes Keycloak
     * @return Liste des unités accessibles
     */
    @Query("SELECT m FROM MUnite m WHERE m.keycloakGroupId IN :groupIds")
    List<MUnite> findByKeycloakGroupIdIn(@Param("groupIds") List<String> groupIds);

    /**
     * Récupère une unité par son code
     * @param codeUnite Code de l'unité
     * @return L'unité correspondante
     */
    MUnite findByCodeUnite(String codeUnite);
}
