package dz.sonatrach.weblqs.mayaaback.controller;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import dz.sonatrach.weblqs.mayaaback.model.RepartitionArretsParCause;

@RestController
@RequestMapping("api/repartition-arrets-cause")
public class RepartitionArretsParCauseController {

    @GetMapping("/{unite}/{mois}")
    public ResponseEntity<List<RepartitionArretsParCause>> getByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<RepartitionArretsParCause> result = createMockRepartitionCause(unite, date);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{unite}/{mois}/internes")
    public ResponseEntity<List<RepartitionArretsParCause>> getCausesInternesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<RepartitionArretsParCause> result = createMockRepartitionCause(unite, date)
                    .stream()
                    .filter(r -> "INTERNE".equals(r.getTypeCause()))
                    .toList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{unite}/{mois}/externes")
    public ResponseEntity<List<RepartitionArretsParCause>> getCausesExternesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<RepartitionArretsParCause> result = createMockRepartitionCause(unite, date)
                    .stream()
                    .filter(r -> "EXTERNE".equals(r.getTypeCause()))
                    .toList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{unite}/{mois}/classes")
    public ResponseEntity<List<RepartitionArretsParCause>> getClassesCausesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<RepartitionArretsParCause> result = createMockRepartitionCause(unite, date)
                    .stream()
                    .filter(r -> "CLASSE".equals(r.getTypeCause()))
                    .toList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{unite}/{mois}/causes")
    public ResponseEntity<List<String>> getCausesByUniteAndMois(
            @PathVariable String unite,
            @PathVariable String mois) {
        try {
            List<String> causes = Arrays.asList(
                "Maintenance préventive",
                "Défaillance équipement",
                "Panne réseau",
                "Arrêt programmé",
                "Problème fournisseur"
            );
            return ResponseEntity.ok(causes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{unite}/{mois}/pie-chart/{type}")
    public ResponseEntity<List<RepartitionArretsParCause>> getDataForPieChart(
            @PathVariable String unite,
            @PathVariable String mois,
            @PathVariable String type) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));

            if (!type.equals("INTERNE") && !type.equals("EXTERNE") && !type.equals("CLASSE")) {
                return ResponseEntity.badRequest().build();
            }

            List<RepartitionArretsParCause> result = createMockRepartitionCause(unite, date)
                    .stream()
                    .filter(r -> type.equals(r.getTypeCause()))
                    .filter(r -> r.getPourcentage().compareTo(BigDecimal.ZERO) > 0)
                    .toList();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    private List<RepartitionArretsParCause> createMockRepartitionCause(String unite, LocalDate date) {
        List<RepartitionArretsParCause> result = new ArrayList<>();

        // Causes internes
        RepartitionArretsParCause cause1 = new RepartitionArretsParCause();
        cause1.setId(1L);
        cause1.setUnite(unite);
        cause1.setMois(date);
        cause1.setCause("Maintenance préventive");
        cause1.setTypeCause("INTERNE");
        cause1.setQuantiteArrets(new BigDecimal("22"));
        cause1.setPourcentage(new BigDecimal("31.0"));
        cause1.setNumeroCause(1);
        result.add(cause1);

        RepartitionArretsParCause cause2 = new RepartitionArretsParCause();
        cause2.setId(2L);
        cause2.setUnite(unite);
        cause2.setMois(date);
        cause2.setCause("Défaillance équipement");
        cause2.setTypeCause("INTERNE");
        cause2.setQuantiteArrets(new BigDecimal("18"));
        cause2.setPourcentage(new BigDecimal("25.4"));
        cause2.setNumeroCause(2);
        result.add(cause2);

        // Causes externes
        RepartitionArretsParCause cause3 = new RepartitionArretsParCause();
        cause3.setId(3L);
        cause3.setUnite(unite);
        cause3.setMois(date);
        cause3.setCause("Panne réseau");
        cause3.setTypeCause("EXTERNE");
        cause3.setQuantiteArrets(new BigDecimal("16"));
        cause3.setPourcentage(new BigDecimal("22.5"));
        cause3.setNumeroCause(3);
        result.add(cause3);

        RepartitionArretsParCause cause4 = new RepartitionArretsParCause();
        cause4.setId(4L);
        cause4.setUnite(unite);
        cause4.setMois(date);
        cause4.setCause("Arrêt fournisseur");
        cause4.setTypeCause("EXTERNE");
        cause4.setQuantiteArrets(new BigDecimal("15"));
        cause4.setPourcentage(new BigDecimal("21.1"));
        cause4.setNumeroCause(4);
        result.add(cause4);

        // Classes
        RepartitionArretsParCause classe1 = new RepartitionArretsParCause();
        classe1.setId(5L);
        classe1.setUnite(unite);
        classe1.setMois(date);
        classe1.setCause("Classe A - Critique");
        classe1.setTypeCause("CLASSE");
        classe1.setQuantiteArrets(new BigDecimal("35"));
        classe1.setPourcentage(new BigDecimal("49.3"));
        classe1.setNumeroCause(1);
        classe1.setClasseCause("Classe A");
        result.add(classe1);

        RepartitionArretsParCause classe2 = new RepartitionArretsParCause();
        classe2.setId(6L);
        classe2.setUnite(unite);
        classe2.setMois(date);
        classe2.setCause("Classe B - Majeur");
        classe2.setTypeCause("CLASSE");
        classe2.setQuantiteArrets(new BigDecimal("25"));
        classe2.setPourcentage(new BigDecimal("35.2"));
        classe2.setNumeroCause(2);
        classe2.setClasseCause("Classe B");
        result.add(classe2);

        return result;
    }
}
