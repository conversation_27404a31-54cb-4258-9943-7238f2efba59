package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.NombreArretTrainsParType;
import dz.sonatrach.weblqs.mayaaback.repo.NombreArretTrainsParTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("api/nombre-arret-trains-par-type")
public class NombreArretTrainsParTypeController {
    @Autowired
    private NombreArretTrainsParTypeRepository nombreArretTrainsParTypeRepository;

    @GetMapping("/{unite}/{mois}")
    public ResponseEntity<List<NombreArretTrainsParType>> getByUniteAndMois(@PathVariable String unite, @PathVariable String mois) {
        LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<NombreArretTrainsParType> result = nombreArretTrainsParTypeRepository.findByUniteAndPdate(unite, date);
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }
}
