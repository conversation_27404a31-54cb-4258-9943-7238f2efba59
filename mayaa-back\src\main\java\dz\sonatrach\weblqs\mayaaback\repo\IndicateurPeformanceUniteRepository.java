package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.IndicateurPeformanceUnite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface IndicateurPeformanceUniteRepository extends JpaRepository<IndicateurPeformanceUnite, Long> {

    /**
     * Trouve les indicateurs de performance pour une unité et un mois donnés.
     * @param mois Le mois de référence
     * @param unite Le code de l'unité
     * @return Liste des indicateurs de performance
     */
    List<IndicateurPeformanceUnite> findByMoisAndUnite(LocalDate mois, String unite);

    /**
     * Trouve tous les indicateurs de performance pour une unité donnée.
     * @param unite Le code de l'unité
     * @return Liste des indicateurs de performance
     */
    List<IndicateurPeformanceUnite> findByUniteOrderByMoisDesc(String unite);

    /**
     * Trouve le dernier indicateur de performance pour une unité donnée.
     * @param unite Le code de l'unité
     * @return Le dernier indicateur de performance
     */
    Optional<IndicateurPeformanceUnite> findFirstByUniteOrderByMoisDesc(String unite);

    /**
     * Trouve tous les indicateurs de performance pour un mois donné.
     * @param mois Le mois de référence
     * @return Liste des indicateurs de performance
     */
    List<IndicateurPeformanceUnite> findByMoisOrderByUnite(LocalDate mois);

    /**
     * Trouve les indicateurs de performance pour un mois et plusieurs unités données (optimisé).
     * @param mois Le mois de référence
     * @param unites Liste des codes d'unités
     * @return Liste des indicateurs de performance
     */
    List<IndicateurPeformanceUnite> findByMoisAndUniteIn(LocalDate mois, List<String> unites);
}