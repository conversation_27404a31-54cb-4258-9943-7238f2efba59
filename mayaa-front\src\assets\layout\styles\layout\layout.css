/* roboto-regular - latin-ext_latin */
@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src: local("Roboto"), local("Roboto-Regular"), url("../../fonts/roboto-v20-latin-ext_latin-regular.woff2") format("woff2"), url("../../fonts/roboto-v20-latin-ext_latin-regular.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* roboto-500 - latin-ext_latin */
@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src: local("Roboto Medium"), local("Roboto-Medium"), url("../../fonts/roboto-v20-latin-ext_latin-500.woff2") format("woff2"), url("../../fonts/roboto-v20-latin-ext_latin-500.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* roboto-700 - latin-ext_latin */
@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src: local("Roboto Bold"), local("Roboto-Bold"), url("../../fonts/roboto-v20-latin-ext_latin-700.woff2") format("woff2"), url("../../fonts/roboto-v20-latin-ext_latin-700.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
.card {
  background-color: var(--surface-card);
  padding: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12);
  border-radius: var(--border-radius);
}
.card:last-child {
  margin-bottom: 0;
}

.ng-hidden {
  display: none !important;
}

h1, h2, h3, h4, h5, h6 {
  margin: 1.5rem 0 1rem 0;
  font-family: inherit;
  font-weight: 600;
  line-height: 1.2;
  color: var(--surface-900);
}
h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
  margin-top: 0;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

mark {
  background: #FFF8E1;
  padding: 0.25rem 0.4rem;
  border-radius: var(--border-radius);
  font-family: monospace;
}

blockquote {
  margin: 1rem 0;
  padding: 0 2rem;
  border-left: 4px solid #90A4AE;
}

hr {
  border-top: solid var(--surface-border);
  border-width: 1px 0 0 0;
  margin: 1rem 0;
}

p {
  margin: 0 0 1rem 0;
  line-height: 1.5;
}
p:last-child {
  margin-bottom: 0;
}

@keyframes px-mask-in {
  from {
    background-color: transparent;
  }
  to {
    background-color: var(--maskbg);
  }
}
@keyframes px-scalein {
  0% {
    opacity: 0;
    transform: scaleY(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
}
@keyframes px-fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes px-fadeout {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.px-scalein {
  animation: px-scalein 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.px-fadein {
  animation: px-fadein 0.15s linear;
}

.px-fadeout {
  animation: px-fadeout 0.15s linear;
}

.layout-topbar-blue {
  --topbar-bg: #1565C0;
  --topbar-start-bg: #0D47A1;
  --topbar-menu-button-bg: #FBC02D;
  --topbar-menu-button-hover-bg: #dda928;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-lightblue {
  --topbar-bg: #0288D1;
  --topbar-start-bg: #0277BD;
  --topbar-menu-button-bg: #FDD835;
  --topbar-menu-button-hover-bg: #dfbe2f;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-white {
  --topbar-bg: #ffffff;
  --topbar-start-bg: #ffffff;
  --topbar-menu-button-bg: var(--primary-color);
  --topbar-menu-button-hover-bg: var(--primary-600);
  --topbar-menu-button-text-color: var(--primary-color-text);
  --topbar-item-text-color: #616161;
  --topbar-item-hover-bg: rgba(0,0,0,.12);
}

.layout-topbar-dark {
  --topbar-bg: #1e1e1e;
  --topbar-start-bg: #1e1e1e;
  --topbar-menu-button-bg: #E91E63;
  --topbar-menu-button-hover-bg: #c41953;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-deeppurple {
  --topbar-bg: #4527A0;
  --topbar-start-bg: #311B92;
  --topbar-menu-button-bg: #F9A825;
  --topbar-menu-button-hover-bg: #d18d1f;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-purple {
  --topbar-bg: #6A1B9A;
  --topbar-start-bg: #4A148C;
  --topbar-menu-button-bg: #F9A825;
  --topbar-menu-button-hover-bg: #d18d1f;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-pink {
  --topbar-bg: #AD1457;
  --topbar-start-bg: #880E4F;
  --topbar-menu-button-bg: #F9A825;
  --topbar-menu-button-hover-bg: #d18d1f;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-cyan {
  --topbar-bg: #0097A7;
  --topbar-start-bg: #006064;
  --topbar-menu-button-bg: #E64A19;
  --topbar-menu-button-hover-bg: #ca4116;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(103, 66, 66, 0.12);
}

.layout-topbar-teal {
  --topbar-bg: #00796B;
  --topbar-start-bg: #004D40;
  --topbar-menu-button-bg: #D32F2F;
  --topbar-menu-button-hover-bg: #d84848;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-green {
  --topbar-bg: #43A047;
  --topbar-start-bg: #2E7D32;
  --topbar-menu-button-bg: #F4511E;
  --topbar-menu-button-hover-bg: #f56639;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-lightgreen {
  --topbar-bg: #689F38;
  --topbar-start-bg: #558B2F;
  --topbar-menu-button-bg: #F57C00;
  --topbar-menu-button-hover-bg: #d86d00;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-lime {
  --topbar-bg: #AFB42B;
  --topbar-start-bg: #9E9D24;
  --topbar-menu-button-bg: #F57C00;
  --topbar-menu-button-hover-bg: #f68c1f;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #212121;
  --topbar-item-hover-bg: rgba(0,0,0,.12);
}

.layout-topbar-yellow {
  --topbar-bg: #FBC02D;
  --topbar-start-bg: #F9A825;
  --topbar-menu-button-bg:#212121;
  --topbar-menu-button-hover-bg:#454545;
  --topbar-menu-button-text-color:#ffffff;
  --topbar-item-text-color: #212121;
  --topbar-item-hover-bg: rgba(0,0,0,.12);
}

.layout-topbar-amber {
  --topbar-bg: #FFA000;
  --topbar-start-bg: #FF8F00;
  --topbar-menu-button-bg: #212121;
  --topbar-menu-button-hover-bg: #454545;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #212121;
  --topbar-item-hover-bg: rgba(0,0,0,.12);
}

.layout-topbar-orange {
  --topbar-bg: #FB8C00;
  --topbar-start-bg: #EF6C00;
  --topbar-menu-button-bg: #212121;
  --topbar-menu-button-hover-bg: #454545;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #212121;
  --topbar-item-hover-bg: rgba(0,0,0,.12);
}

.layout-topbar-deeporange {
  --topbar-bg: #D84315;
  --topbar-start-bg: #BF360C;
  --topbar-menu-button-bg: #00BCD4;
  --topbar-menu-button-hover-bg: #009eb2;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-brown {
  --topbar-bg: #5D4037;
  --topbar-start-bg: #4E342E;
  --topbar-menu-button-bg: #F9A825;
  --topbar-menu-button-hover-bg: #d18d1f;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-grey {
  --topbar-bg: #616161;
  --topbar-start-bg: #424242;
  --topbar-menu-button-bg: #0097A7;
  --topbar-menu-button-hover-bg: #008593;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-bluegrey {
  --topbar-bg: #546E7A;
  --topbar-start-bg: #37474F;
  --topbar-menu-button-bg: #0097A7;
  --topbar-menu-button-hover-bg: #008593;
  --topbar-menu-button-text-color: #ffffff;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar-indigo {
  --topbar-bg: #3F51B5;
  --topbar-start-bg: #283593;
  --topbar-menu-button-bg: #FBC02D;
  --topbar-menu-button-hover-bg: #d3a126;
  --topbar-menu-button-text-color: #212121;
  --topbar-item-text-color: #ffffff;
  --topbar-item-hover-bg: rgba(255,255,255,.12);
}

.layout-topbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 4rem;
  transition: width var(--transition-duration);
  display: flex;
  color: var(--topbar-item-text-color);
  background-color: var(--topbar-bg);
  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.12), 0px 4px 5px rgba(0, 0, 0, 0.14), 0px 2px 4px -1px rgba(0, 0, 0, 0.2);
}
.layout-topbar ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.layout-topbar .layout-topbar-start {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 17rem;
  background-color: var(--topbar-start-bg);
}

.layout-topbar-logo > img{

}

img[style*="color:#fffff"] {
  background-color: green;
}

.layout-topbar .layout-topbar-start .layout-topbar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}
.layout-topbar .layout-topbar-start .layout-topbar-logo .layout-topbar-logo-slim {
  display: none;
}
.layout-topbar .layout-topbar-start .layout-menu-button {
  position: absolute;
  top: 50%;
  margin-top: -1.25rem;
  right: -1.25rem;
  width: 2.5rem;
  height: 2.5rem;
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--topbar-menu-button-text-color);
  background-color: var(--topbar-menu-button-bg);
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.06), 0px 7px 9px rgba(0, 0, 0, 0.12), 0px 20px 25px -8px rgba(0, 0, 0, 0.18);
  transition: background-color var(--transition-duration);
  cursor: pointer;
}
.layout-topbar .layout-topbar-start .layout-menu-button:hover {
  background-color: var(--topbar-menu-button-hover-bg);
}
.layout-topbar .layout-topbar-start .layout-menu-button i {
  transition: transform var(--transition-duration);
}
.layout-topbar .layout-topbar-start .layout-topbar-mobile-button {
  display: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  cursor: pointer;
}
.layout-topbar .layout-topbar-end {
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
}
.layout-topbar .layout-topbar-end .layout-megamenu {
  background: none;
}
.layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text,
.layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
  color: var(--topbar-item-text-color);
}
.layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link:not(.p-disabled):hover {
  background: var(--topbar-item-hover-bg);
  transition: background-color var(--transition-duration);
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items {
  display: flex;
  flex-direction: row;
  gap: 1rem;
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li {
  position: relative;
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li > a {
  width: 2.5rem;
  height: 2.5rem;
  user-select: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--topbar-item-text-color);
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li > a i {
  font-size: 1.25rem;
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li > a:hover {
  background-color: var(--topbar-item-hover-bg);
  transition: background-color var(--transition-duration);
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li > div {
  position: absolute;
  top: 3.25rem;
  right: 0;
  min-width: 20rem;
  padding: 1rem 0;
  transform-origin: top;
  background: var(--surface-overlay);
  color: var(--text-color);
  border-radius: var(--border-radius);
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li.layout-topbar-search {
  position: static;
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li.layout-topbar-search .layout-search-panel {
  background: var(--surface-overlay);
  height: 4rem;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  transform-origin: top;
  display: flex;
  align-items: center;
  padding: 0 2rem;
  border-radius: 0;
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li.layout-topbar-search .layout-search-panel > i {
  color: var(--text-color);
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li.layout-topbar-search .layout-search-panel input {
  border-radius: 0;
  border: 0 none;
  flex-grow: 1;
  background-color: transparent;
  margin: 0 1rem;
}
.layout-topbar .layout-topbar-end .layout-topbar-actions-end .layout-topbar-items > li.layout-topbar-search .layout-search-panel input:focus {
  outline: 0 none;
}

.layout-menu-light {
  --menu-bg: #FDFEFF;
  --root-menuitem-text-color: #657380;
  --menuitem-text-color: #515C66;
  --menuitem-hover-bg: rgba(0,0,0,.04);
  --active-menuitem-text-color: var(--primary-500);
  --active-menuitem-bg: var(--primary-50);
  --inline-menu-border-color: #e4e4e4;
}

.layout-menu-dark {
  --menu-bg: #1e1e1e;
  --root-menuitem-text-color: rgba(255,255,255,.60);
  --menuitem-text-color: rgba(255,255,255,.87);
  --menuitem-hover-bg: hsla(0,0%,100%,.04);
  --active-menuitem-text-color: rgba(255,255,255,.87);
  --active-menuitem-bg: hsla(0,0%,100%,.04);
  --inline-menu-border-color: hsla(0,0%,100%,.12);
}

.layout-menu-indigo {
  --menu-bg: #1A237E;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-bluegrey {
  --menu-bg: #37474F;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-brown {
  --menu-bg: #4E342E;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-cyan {
  --menu-bg: #006064;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-green {
  --menu-bg: #2E7D32;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-deeppurple {
  --menu-bg: #4527A0;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-deeporange {
  --menu-bg: #BF360C;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-pink {
  --menu-bg: #880E4F;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-purple {
  --menu-bg: #6A1B9A;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-menu-teal {
  --menu-bg: #00695C;
  --root-menuitem-text-color: #ffffff;
  --menuitem-text-color: rgba(255,255,255,.6);
  --menuitem-hover-bg: rgba(255,255,255,.12);
  --active-menuitem-text-color: #ffffff;
  --active-menuitem-bg: rgba(255,255,255,.24);
  --inline-menu-border-color: rgba(255,255,255,.24);
}

.layout-sidebar {
  position: fixed;
  height: calc(100% - 4rem);
  top: 4rem;
  left: 0;
  width: 17rem;
  background: var(--menu-bg);
  box-shadow: 2px 0 4px -1px rgba(0, 0, 0, 0.2), 4px 0 5px 0 rgba(0, 0, 0, 0.14), 1px 0 10px 0 rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
}
.layout-sidebar .layout-sidebar-top {
  display: none;
}
.layout-sidebar .layout-menu-container {
  flex: 1;
  padding-bottom: 2rem;
  overflow: auto;
}
.layout-sidebar .layout-menu {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.layout-sidebar .layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
  display: flex;
  align-items: center;
  font-size: 0.857rem;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--root-menuitem-text-color);
  padding: 1rem 1.5rem 1rem 1rem;
}
.layout-sidebar .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > .layout-menuitem-root-icon {
  display: none;
}
.layout-sidebar .layout-menu .layout-root-menuitem > a {
  display: none;
}
.layout-sidebar .layout-menu a {
  user-select: none;
}
.layout-sidebar .layout-menu li.active-menuitem > a {
  color: var(--active-menuitem-text-color);
  background-color: var(--active-menuitem-bg);
}
.layout-sidebar .layout-menu li.active-menuitem > a .layout-menuitem-icon {
  margin-right: 0.5rem;
  color: var(--active-menuitem-text-color);
}
.layout-sidebar .layout-menu li.active-menuitem > a .layout-submenu-toggler {
  transform: rotate(-180deg);
}
.layout-sidebar .layout-menu ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.layout-sidebar .layout-menu ul a {
  display: flex;
  align-items: center;
  position: relative;
  outline: 0 none;
  color: var(--menuitem-text-color);
  cursor: pointer;
  padding: 0.75rem 1.5rem;
  transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}
.layout-sidebar .layout-menu ul a .layout-menuitem-icon {
  margin-right: 0.5rem;
  color: var(--menuitem-text-color);
}
.layout-sidebar .layout-menu ul a .layout-submenu-toggler {
  font-size: 75%;
  margin-left: auto;
  transition: transform var(--transition-duration);
}
.layout-sidebar .layout-menu ul a.active-route {
  font-weight: 700;
}
.layout-sidebar .layout-menu ul a:hover {
  background-color: var(--menuitem-hover-bg);
}
.layout-sidebar .layout-menu ul ul {
  overflow: hidden;
  border-radius: var(--border-radius);
}
.layout-sidebar .layout-menu ul ul li a {
  padding-left: 2.5rem;
}
.layout-sidebar .layout-menu ul ul li li a {
  padding-left: 3rem;
}
.layout-sidebar .layout-menu ul ul li li li a {
  padding-left: 3.5rem;
}
.layout-sidebar .layout-menu ul ul li li li li a {
  padding-left: 4rem;
}
.layout-sidebar .layout-menu ul ul li li li li li a {
  padding-left: 5.5rem;
}
.layout-sidebar .layout-menu ul ul li li li li li li a {
  padding-left: 5rem;
}

@media screen and (min-width: 992px) {
  .layout-drawer .layout-sidebar {
    height: 100%;
    top: 0;
    transition: width 0.3s cubic-bezier(0, 0, 0.2, 1);
    width: 4.25rem;
    z-index: 999;
  }
  .layout-drawer .layout-sidebar .layout-sidebar-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.75rem;
    height: 4rem;
    background-color: var(--topbar-start-bg);
    color: var(--topbar-item-text-color);
  }
  .layout-drawer .layout-sidebar .layout-sidebar-top .layout-sidebar-logo {
    display: none;
    width: 0;
    transition: width 0.2s cubic-bezier(0, 0, 0.2, 1);
    transition-delay: 300ms;
  }
  .layout-drawer .layout-sidebar .layout-sidebar-top .layout-sidebar-logo-slim {
    display: inline;
    order: 1;
  }
  .layout-drawer .layout-sidebar .layout-sidebar-top .layout-sidebar-anchor {
    display: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    border: 2px solid var(--topbar-menu-button-bg);
    background-color: transparent;
    transition: background-color var(--transition-duration), transform 0.3s;
  }
  .layout-drawer .layout-sidebar .layout-menu-container {
    overflow: hidden;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu {
    transition: all 0.4s;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
    opacity: 1;
    white-space: nowrap;
    display: flex;
    justify-content: flex-end;
    height: 2.92rem;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > span {
    margin-right: auto;
    opacity: 0;
    transition: all 0.1s;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text .layout-menuitem-icon {
    font-size: 1.25rem;
    width: 1.25rem;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > .layout-menuitem-root-icon {
    display: block;
    margin-left: 0.125rem;
    font-size: 1.25rem;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu ul a span {
    opacity: 0;
    white-space: nowrap;
    transition: all 0.1s;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu ul a .layout-menuitem-icon {
    font-size: 1.25rem;
    width: auto;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu ul a .layout-submenu-toggler {
    display: none;
  }
  .layout-drawer .layout-sidebar .layout-menu-container .layout-menu ul ul li a {
    padding: 0.75rem 1.5rem;
  }
  .layout-drawer .layout-sidebar .layout-menu-profile > button {
    padding: 0.75rem 1rem;
  }
  .layout-drawer .layout-sidebar .layout-menu-profile > button span {
    opacity: 0;
    white-space: nowrap;
    transition: all 0.1s;
  }
  .layout-drawer .layout-sidebar .layout-menu-profile > ul > li > button > i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
    width: auto;
  }
  .layout-drawer .layout-topbar {
    padding-left: 4.25rem;
  }
  .layout-drawer .layout-topbar .layout-topbar-start {
    display: none;
  }
  .layout-drawer.layout-drawer .layout-content-wrapper {
    margin-left: 4.25rem;
    transition: margin-left 0.3s cubic-bezier(0, 0, 0.2, 1);
    overflow-x: hidden;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar {
    width: 17rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-sidebar-top {
    padding: 0 1.5rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-sidebar-top .layout-sidebar-logo {
    display: inline;
    width: 100%;
    transition: width 0.2s cubic-bezier(0, 0, 0.2, 1);
    transition-delay: 300ms;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-sidebar-top .layout-sidebar-logo-slim {
    display: none;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-sidebar-top .layout-sidebar-anchor {
    display: block;
    animation: px-fadein 0.15s linear;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container {
    overflow: auto;
    overflow-x: hidden;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
    white-space: nowrap;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > .layout-menuitem-root-icon {
    display: none;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > span {
    margin-right: auto;
    opacity: 1;
    transition: all 0.1s;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul a {
    width: auto;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul a .layout-submenu-toggler {
    display: block;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul a span {
    opacity: 1;
    white-space: nowrap;
    transition: all 0.3s;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul {
    overflow: hidden;
    border-radius: var(--border-radius);
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul li a {
    padding-left: 2.5rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul li li a {
    padding-left: 3rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul li li li a {
    padding-left: 3.5rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul li li li li a {
    padding-left: 4rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul li li li li li a {
    padding-left: 5.5rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul ul li li li li li li a {
    padding-left: 5rem;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-profile > button span {
    opacity: 1;
    white-space: nowrap;
    transition: all 0.1s;
  }
  .layout-drawer.layout-sidebar-active .layout-sidebar .layout-menu-profile > ul > li > button > i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
    width: auto;
  }
  .layout-drawer.layout-sidebar-anchored .layout-topbar {
    padding-left: 17rem;
  }
  .layout-drawer.layout-sidebar-anchored .layout-sidebar-top .layout-sidebar-anchor {
    background-color: var(--topbar-menu-button-bg);
  }
  .layout-drawer.layout-sidebar-anchored .layout-content-wrapper {
    margin-left: 17rem;
  }
}
@media screen and (min-width: 992px) {
  .layout-container.layout-slim .layout-sidebar {
    width: 5rem;
    overflow: visible;
    z-index: 998;
  }
  .layout-container.layout-slim .layout-sidebar .layout-menu-container {
    overflow: auto;
  }
  .layout-container.layout-slim .layout-sidebar .layout-menu-container::-webkit-scrollbar {
    display: none;
  }
  .layout-container.layout-slim .layout-topbar .layout-topbar-start {
    width: 5rem;
  }
  .layout-container.layout-slim .layout-topbar .layout-topbar-start .layout-topbar-logo .layout-topbar-logo-full {
    display: none;
  }
  .layout-container.layout-slim .layout-topbar .layout-topbar-start .layout-topbar-logo .layout-topbar-logo-slim {
    display: block;
  }
  .layout-container.layout-slim .layout-topbar .layout-topbar-start .layout-menu-button {
    display: none;
  }
  .layout-container.layout-slim .layout-content-wrapper {
    margin-left: 5rem;
  }
  .layout-container.layout-slim .layout-menu {
    padding: 1rem 0;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem {
    margin: 0.75rem 0;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem:first-child {
    margin-top: 0;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem:last-child {
    margin-top: 0;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
    display: none;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > a {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    outline: none;
    transition: background-color var(--transition-duration);
    width: 3rem;
    height: 3rem;
    margin: 0 auto;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > a .layout-submenu-toggler {
    display: none;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > a .layout-menuitem-icon {
    font-size: 1.5rem;
    color: var(--menuitem-text-color);
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > a .layout-menuitem-text {
    display: none;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > a:hover {
    background-color: var(--menuitem-hover-bg);
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul {
    position: absolute;
    left: 5rem;
    top: 0;
    min-width: 15rem;
    background-color: var(--menu-bg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
    max-height: 20rem;
    overflow: auto;
    z-index: 999;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul::-webkit-scrollbar {
    display: none;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul a {
    padding-right: 0.5rem;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul li a {
    padding-left: 0.5rem;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul li li a {
    padding-left: 1rem;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul li li li a {
    padding-left: 1.5rem;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul li li li li a {
    padding-left: 2rem;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul li li li li li a {
    padding-left: 2.5rem;
  }
  .layout-container.layout-slim .layout-menu .layout-root-menuitem > ul li li li li li li a {
    padding-left: 3rem;
  }
  .layout-container.layout-slim .layout-menu-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .layout-container.layout-slim .layout-menu-profile > button {
    width: 3rem;
    height: 3rem;
    padding: 0;
    border-radius: 50%;
    justify-content: center;
    margin: 0.75rem 0;
  }
  .layout-container.layout-slim .layout-menu-profile > button span, .layout-container.layout-slim .layout-menu-profile > button i {
    display: none;
  }
  .layout-container.layout-slim .layout-menu-profile > ul > li {
    margin: 0.75rem 0;
  }
  .layout-container.layout-slim .layout-menu-profile > ul > li:first-child {
    margin-top: 0;
  }
  .layout-container.layout-slim .layout-menu-profile > ul > li > button {
    width: 3rem;
    height: 3rem;
    padding: 0;
    justify-content: center;
    border-radius: 50%;
  }
  .layout-container.layout-slim .layout-menu-profile > ul > li > button > i {
    font-size: 1.5rem;
    margin-right: 0;
  }
  .layout-container.layout-slim .layout-menu-profile > ul > li > button > span {
    display: none;
  }
  .layout-container.layout-slim .layout-menu-profile > ul > li:last-child {
    margin-bottom: 1rem;
  }
}
@media screen and (min-width: 992px) {
  .layout-container.layout-slim-plus .layout-sidebar {
    width: 7rem;
    overflow: visible;
    z-index: 998;
  }
  .layout-container.layout-slim-plus .layout-sidebar .layout-menu-container {
    overflow: auto;
  }
  .layout-container.layout-slim-plus .layout-sidebar .layout-menu-container::-webkit-scrollbar {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-topbar .layout-topbar-start {
    width: 7rem;
  }
  .layout-container.layout-slim-plus .layout-topbar .layout-topbar-start .layout-topbar-logo .layout-topbar-logo-full {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-topbar .layout-topbar-start .layout-topbar-logo .layout-topbar-logo-slim {
    display: block;
  }
  .layout-container.layout-slim-plus .layout-topbar .layout-topbar-start .layout-menu-button {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-content-wrapper {
    margin-left: 7rem;
  }
  .layout-container.layout-slim-plus .layout-menu {
    padding: 1rem 0;
  }
  .layout-container.layout-slim-plus .layout-menu ul {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-menu li.active-menuitem > ul {
    display: block;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem {
    margin: 0.75rem 0;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem:first-child {
    margin-top: 0;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem:last-child {
    margin-top: 0;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    outline: none;
    transition: background-color var(--transition-duration);
    width: auto;
    height: auto;
    margin: 0 auto 1rem auto;
    padding: 0.75rem 0;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > a .layout-submenu-toggler {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > a .layout-menuitem-icon {
    font-size: 1.5rem;
    color: var(--menuitem-text-color);
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > a .layout-menuitem-text {
    font-size: 0.875rem;
    display: block;
    margin-top: 0.25rem;
    color: var(--menuitem-text-color);
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > a:hover {
    background-color: var(--menuitem-hover-bg);
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul {
    position: absolute;
    left: 7rem;
    top: 0;
    min-width: 15rem;
    background-color: var(--menu-bg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
    max-height: 20rem;
    overflow: auto;
    z-index: 999;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul::-webkit-scrollbar {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul a {
    padding-right: 0.5rem;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul li a {
    padding-left: 0.5rem;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul li li a {
    padding-left: 1rem;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul li li li a {
    padding-left: 1.5rem;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul li li li li a {
    padding-left: 2rem;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul li li li li li a {
    padding-left: 2.5rem;
  }
  .layout-container.layout-slim-plus .layout-menu .layout-root-menuitem > ul li li li li li li a {
    padding-left: 3rem;
  }
  .layout-container.layout-slim-plus .layout-menu-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > button {
    width: 3rem;
    height: 3rem;
    padding: 0;
    border-radius: 50%;
    justify-content: center;
    margin: 0.75rem 0;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > button span, .layout-container.layout-slim-plus .layout-menu-profile > button i {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > ul > li {
    margin: 0.75rem 0;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > ul > li:first-child {
    margin-top: 0;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > ul > li > button {
    width: 3rem;
    height: 3rem;
    padding: 0;
    justify-content: center;
    border-radius: 50%;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > ul > li > button > i {
    font-size: 1.5rem;
    margin-right: 0;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > ul > li > button > span {
    display: none;
  }
  .layout-container.layout-slim-plus .layout-menu-profile > ul > li:last-child {
    margin-bottom: 1rem;
  }
}
@media screen and (min-width: 992px) {
  .layout-container.layout-horizontal .layout-sidebar {
    width: 100%;
    height: 3rem;
    top: 4rem;
    position: fixed;
    z-index: 998;
    overflow: visible;
    flex-direction: row;
    padding: 0 2rem;
  }
  .layout-container.layout-horizontal .layout-sidebar .layout-menu-container {
    overflow: auto;
    padding-bottom: 0;
    width: calc(100% - 300px);
  }
  .layout-container.layout-horizontal .layout-sidebar .layout-menu-container::-webkit-scrollbar {
    display: none;
  }
  .layout-container.layout-horizontal .layout-topbar .layout-topbar-start .layout-menu-button {
    display: none;
  }
  .layout-container.layout-horizontal .layout-topbar .layout-topbar-end {
    width: 300px;
  }
  .layout-container.layout-horizontal .layout-content-wrapper {
    padding-top: 7rem;
  }
  .layout-container.layout-horizontal .layout-menu {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    align-items: center;
    height: 100%;
  }
  .layout-container.layout-horizontal .layout-menu ul {
    display: none;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem {
    border-radius: var(--border-radius);
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > .layout-menuitem-root-text {
    display: none;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > a {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0;
    cursor: pointer;
    outline: none;
    color: var(--menuitem-text-color);
    transition: background-color var(--transition-duration);
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > a .layout-submenu-toggler {
    display: block;
    margin-left: auto;
    transition: transform 0.2s;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > a .layout-menuitem-icon {
    font-size: 1.25rem;
    color: var(--menuitem-text-color);
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > a .layout-menuitem-text {
    font-size: 0.875rem;
    display: block;
    margin-left: 0.75rem;
    margin-right: 0.75rem;
    white-space: nowrap;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > a:hover {
    background-color: var(--menuitem-hover-bg);
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul {
    position: absolute;
    left: auto;
    top: 3rem;
    min-width: 15rem;
    background-color: var(--menu-bg);
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
    padding: 0.5rem 0;
    border-radius: var(--border-radius);
    max-height: 20rem;
    overflow: auto;
    z-index: 999;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul::-webkit-scrollbar {
    display: none;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul a {
    padding-right: 0.5rem;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul li a {
    padding-left: 0.5rem;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul li li a {
    padding-left: 1rem;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul li li li a {
    padding-left: 1.5rem;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul li li li li a {
    padding-left: 2rem;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul li li li li li a {
    padding-left: 2.5rem;
  }
  .layout-container.layout-horizontal .layout-menu .layout-root-menuitem > ul li li li li li li a {
    padding-left: 3rem;
  }
  .layout-container.layout-horizontal.layout-menu-profile-start .layout-menu-profile {
    border-bottom: 0 none;
  }
  .layout-container.layout-horizontal.layout-menu-profile-start .layout-menu-profile > ul {
    left: 0;
  }
  .layout-container.layout-horizontal.layout-menu-profile-end .layout-menu-profile {
    border-top: 0 none;
  }
  .layout-container.layout-horizontal.layout-menu-profile-end .layout-menu-profile > ul {
    right: 0;
  }
  .layout-container.layout-horizontal .layout-menu-profile {
    position: relative;
    overflow: visible;
    width: auto;
    height: 100%;
  }
  .layout-container.layout-horizontal .layout-menu-profile > button {
    padding: 0 0.75rem;
    width: auto;
    height: 100%;
  }
  .layout-container.layout-horizontal .layout-menu-profile > button .layout-menu-profile-toggler {
    margin-left: 0.5rem;
  }
  .layout-container.layout-horizontal .layout-menu-profile > ul {
    position: absolute;
    top: 3rem;
    min-width: 15rem;
    background-color: var(--menu-bg);
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
    padding: 1rem;
    border-radius: var(--border-radius);
    max-height: 20rem;
    overflow: auto;
    z-index: 999;
    transform-origin: center top;
  }
  .layout-container.layout-horizontal .layout-menu-profile > ul > li > button {
    padding: 0.75rem 0.5rem;
  }
}
@media screen and (min-width: 992px) {
  .layout-reveal .layout-sidebar {
    height: 100%;
    top: 0;
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    transform: translateX(-12.75rem);
    z-index: 999;
  }
  .layout-reveal .layout-sidebar .layout-sidebar-top {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 0.75rem;
    height: 4rem;
    background-color: var(--topbar-start-bg);
    color: var(--topbar-item-text-color);
  }
  .layout-reveal .layout-sidebar .layout-sidebar-top .layout-sidebar-logo {
    display: none;
  }
  .layout-reveal .layout-sidebar .layout-sidebar-top .layout-sidebar-logo-slim {
    display: inline;
  }
  .layout-reveal .layout-sidebar .layout-sidebar-top .layout-sidebar-anchor {
    display: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    border: 2px solid var(--topbar-menu-button-bg);
    background-color: transparent;
    transition: background-color var(--transition-duration), transform 0.3s;
  }
  .layout-reveal .layout-sidebar .layout-menu-container {
    overflow: hidden;
  }
  .layout-reveal .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > span {
    margin-right: auto;
  }
  .layout-reveal .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > .layout-menuitem-root-icon {
    display: block;
    margin-right: 0.125rem;
  }
  .layout-reveal .layout-sidebar .layout-menu-container .layout-menu ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
  }
  .layout-reveal .layout-sidebar .layout-menu-container .layout-menu ul a .layout-menuitem-text {
    order: 1;
    margin-right: auto;
  }
  .layout-reveal .layout-sidebar .layout-menu-container .layout-menu ul a .layout-submenu-toggler {
    order: 2;
    display: none;
    margin-right: 0.5rem;
  }
  .layout-reveal .layout-sidebar .layout-menu-container .layout-menu ul a .layout-menuitem-icon {
    order: 3;
    margin-right: 0;
    font-size: 1.25rem;
    width: auto;
  }
  .layout-reveal .layout-sidebar .layout-menu-profile > ul > li > button {
    flex-direction: row-reverse;
  }
  .layout-reveal .layout-sidebar .layout-menu-profile > ul > li > button > i {
    margin-right: 0;
    margin-left: auto;
    font-size: 1.25rem;
    width: auto;
  }
  .layout-reveal .layout-topbar {
    padding-left: 4.25rem;
  }
  .layout-reveal .layout-topbar .layout-topbar-start {
    display: none;
  }
  .layout-reveal.layout-reveal .layout-content-wrapper {
    margin-left: 4.25rem;
    transition: margin-left 0.3s cubic-bezier(0, 0, 0.2, 1);
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar {
    transform: translateX(0);
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-sidebar-top {
    padding: 0 1.5rem;
    justify-content: space-between;
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-sidebar-top .layout-sidebar-logo {
    display: inline;
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-sidebar-top .layout-sidebar-logo-slim {
    display: none;
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-sidebar-top .layout-sidebar-anchor {
    display: block;
    animation: px-fadein 0.15s linear;
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-menu-container {
    overflow: auto;
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu .layout-root-menuitem > .layout-menuitem-root-text > .layout-menuitem-root-icon {
    display: none;
  }
  .layout-reveal.layout-sidebar-active .layout-sidebar .layout-menu-container .layout-menu ul a .layout-submenu-toggler {
    display: block;
  }
  .layout-reveal.layout-sidebar-anchored .layout-topbar {
    padding-left: 17rem;
  }
  .layout-reveal.layout-sidebar-anchored .layout-sidebar-top .layout-sidebar-anchor {
    background-color: var(--topbar-menu-button-bg);
  }
  .layout-reveal.layout-sidebar-anchored .layout-content-wrapper {
    margin-left: 17rem;
  }
}
.layout-container.layout-menu-profile-active .layout-menu-profile-toggler {
  transform: rotate(-180deg);
}
.layout-container.layout-menu-profile-end .layout-menu-profile {
  border-top: 1px solid var(--inline-menu-border-color);
}
.layout-container.layout-menu-profile-start .layout-menu-profile {
  border-bottom: 1px solid var(--inline-menu-border-color);
}

.layout-menu-profile {
  width: 100%;
  background: var(--menu-bg);
  overflow: hidden;
}
.layout-menu-profile > button {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--menuitem-text-color);
  transition: background-color var(--transition-duration);
}
.layout-menu-profile > button > span {
  display: flex;
  flex-direction: column;
  margin-left: 0.5rem;
}
.layout-menu-profile > button > i {
  margin-left: auto;
  transition: transform var(--transition-duration);
}
.layout-menu-profile > button:hover {
  background-color: var(--menuitem-hover-bg);
}
.layout-menu-profile > ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.layout-menu-profile > ul > li > button {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
  color: var(--menuitem-text-color);
}
.layout-menu-profile > ul > li > button > i {
  margin-right: 0.5rem;
  color: var(--menuitem-text-color);
}
.layout-menu-profile > ul > li > button:hover {
  background-color: var(--menuitem-hover-bg);
}

.layout-megamenu.p-megamenu {
  border: 0 none;
  padding: 0;
}

.parallax__layer__0 {
  transform: translateZ(-150px) scale(2.5);
}

.parallax__layer__1 {
  transform: translateZ(-100px) scale(2);
}

.parallax__layer__2 {
  transform: translateZ(-50px) scale(1.5);
}

.parallax__layer__3 {
  transform: translateZ(0px) scale(1);
}

html {
  height: 100%;
  font-size: 14px;
}

body {
  font-weight: 400;
  padding: 0;
  margin: 0;
  min-height: 100%;
  background: var(--surface-ground);
  color: var(--text-color);
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  text-decoration: none;
}

.p-toast.p-toast-top-right, .p-toast.p-toast-top-left, .p-toast.p-toast-top-center {
  top: 85px;
}

.layout-content-wrapper {
  padding-top: 4rem;
}
.layout-content-wrapper .layout-content {
  min-height: calc(100vh - 13.5rem);
  padding: 2rem;
}

.layout-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background-color: var(--surface-card);
}

.layout-config-button.p-button {
  z-index: 999;
  position: fixed;
  top: 50%;
  right: 0;
  width: auto;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.layout-config-button.p-button .p-button-icon {
  font-size: 2rem;
}

.layout-breadcrumb-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.05), 0px 1px 4px rgba(0, 0, 0, 0.08);
  padding: 0.5rem 2rem;
  background-color: var(--surface-card);
}
.layout-breadcrumb-container .layout-breadcrumb ol {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style: none;
  gap: 0.5rem;
  flex-wrap: wrap;
  color: var(--text-color-secondary);
}
.layout-breadcrumb-container .layout-breadcrumb ol li {
  display: flex;
  align-items: center;
}
.layout-breadcrumb-container .layout-breadcrumb-buttons {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.layout-breadcrumb-container .layout-breadcrumb-buttons button {
  flex-shrink: 0;
}

@media screen and (min-width: 992px) {
  .layout-container.layout-static .layout-sidebar {
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1);
  }
  .layout-container.layout-static .layout-content-wrapper {
    margin-left: 17rem;
    transition: margin-left 0.3s cubic-bezier(0, 0, 0.2, 1);
  }
  .layout-container.layout-static .layout-topbar .layout-menu-button i {
    transform: rotate(180deg);
  }
  .layout-container.layout-static-inactive .layout-sidebar {
    transform: translateX(-100%);
  }
  .layout-container.layout-static-inactive .layout-content-wrapper {
    margin-left: 0;
  }
  .layout-container.layout-static-inactive .layout-topbar .layout-menu-button i {
    transform: rotate(0deg);
  }
  .layout-container.layout-overlay .layout-content-wrapper {
    margin-left: 0;
  }
  .layout-container.layout-overlay .layout-sidebar {
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1);
  }
  .layout-container.layout-overlay.layout-overlay-active .layout-sidebar {
    transform: translateX(0);
  }
  .layout-container.layout-overlay.layout-overlay-active .layout-topbar .layout-menu-button i {
    transform: rotate(180deg);
  }
}
@media screen and (max-width: 991px) {
  .blocked-scroll {
    overflow: hidden;
  }
  .layout-container .layout-content-wrapper {
    margin-left: 0;
  }
  .layout-container .layout-sidebar {
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1);
  }
  .layout-container.layout-mobile-active .layout-sidebar {
    transform: translateX(0);
  }
  .layout-container.layout-mobile-active .layout-mask {
    display: block;
    animation: px-mask-in 0.3s cubic-bezier(0, 0, 0.2, 1);
  }
  .layout-container.layout-mobile-active .layout-topbar .layout-menu-button i {
    transform: rotate(180deg);
  }
  .layout-container .layout-mask {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 998;
    width: 100%;
    height: 100%;
    background-color: var(--maskbg);
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end {
    display: flex;
    flex-direction: column-reverse;
    align-items: stretch;
    position: fixed;
    top: 4rem;
    width: 100%;
    background-color: var(--topbar-bg);
    padding-left: 0;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-megamenu {
    height: 3rem;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu .p-megamenu-root-list > .p-menuitem {
    position: static;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu .p-megamenu-root-list > .p-menuitem .p-megamenu-panel {
    position: absolute;
    left: 1rem;
    right: 1rem;
    max-height: 20rem;
    overflow: auto;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-megamenu.p-megamenu .p-megamenu-root-list > .p-menuitem .p-megamenu-grid {
    flex-direction: column;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-topbar-items {
    justify-content: space-between;
    align-items: center;
    height: 3rem;
    padding: 0 1rem;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-topbar-items > li {
    position: static;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-end .layout-topbar-items > li > div {
    left: 1rem;
    right: 1rem;
  }
  .layout-container.layout-topbar-menu-active .layout-topbar .layout-topbar-actions-start {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 3rem;
  }
  .layout-topbar {
    display: flex;
    flex-direction: column;
  }
  .layout-topbar .layout-topbar-start {
    padding: 0 1rem;
    width: 100%;
    height: 4rem;
    justify-content: start;
  }
  .layout-topbar .layout-topbar-start .layout-menu-button {
    position: relative;
    margin-top: 0;
    margin-left: 1rem;
    top: auto;
  }
  .layout-topbar .layout-topbar-start .layout-topbar-mobile-button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    transition: background-color var(--transition-duration);
  }
  .layout-topbar .layout-topbar-start .layout-topbar-mobile-button:hover {
    background-color: var(--topbar-bg);
  }
  .layout-topbar .layout-topbar-end {
    display: none;
  }
}
@media screen and (min-width: 1729px) {
  .layout-content {
    width: 1504px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
