{"name": "mayaa-front", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/common": "^17.3.12", "@angular/compiler": "^17.3.12", "@angular/core": "^17.3.12", "@angular/forms": "^17.3.12", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/router": "^17.3.12", "chart.js": "^3.9.1", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "keycloak-angular": "^15.3.0", "keycloak-js": "^24.0.5", "primeng": "17.18.13", "quill": "^1.3.7", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.10", "@angular/cli": "^17.3.11", "@angular/compiler-cli": "^17.3.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}