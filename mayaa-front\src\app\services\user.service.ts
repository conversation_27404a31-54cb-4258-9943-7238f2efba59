import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'projects/shared-lib/src/environments/environment';
import { Observable } from 'rxjs';
import { RUser } from '../model/RUser';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private baseUrl: string = environment.apiUrl + '/api/shared/';
  constructor(private http: HttpClient) {}

  // Get the list of all RUsers
  public getListRUsers(): Observable<RUser[]> {
    return this.http.get<RUser[]>(`${this.baseUrl}users`);
  }

  // Get a user by its ID
  public getUserById(idUser: number): Observable<RUser> {
    return this.http.get<RUser>(`${this.baseUrl}users/${idUser}`);
  }

  // Get a user by its kidUser
  public getUserByKId(kidUser: string): Observable<RUser> {
    return this.http.get<RUser>(`${this.baseUrl}users/k/${kidUser}`);
  }

  // Add the connected user to the database
  public addKuserToDB(): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}users/add`, {});
  }

  public searchUsersByName(name: string,responsable: boolean): Observable<RUser[]> {
    if (!responsable)
      return this.http.get<RUser[]>(`${this.baseUrl}users/search/${name}`);
    else
      return this.http.get<RUser[]>(`${this.baseUrl}users/searchresponsable/${name}`);
  }


  public searchUsersByNameNoInterimForConnectedUser(searchKey: string, userId: number): Observable<RUser[]> {
    return this.http.get<RUser[]>(`${this.baseUrl}users/search/${searchKey}/${userId}`);
  }
}
