package dz.sonatrach.weblqs.mayaaback.model;

import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Table(name = "GAZ_TORCHEE_PAR_CAUSE_TORCHAGE")
public class GazTorcheeParCauseTorchage implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE", length = 3)
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "PMOIS")
    @JsonView(View.basic.class)
    private LocalDate pmois;

    @Column(name = "CODE_CAUSE_TORCHAGE", length = 60)
    @JsonView(View.basic.class)
    private String codeCauseTorchage;

    @Column(name = "LIB_CAUSE_TORCHAGE", length = 600)
    @JsonView(View.basic.class)
    private String libCauseTorchage;

    @Column(name = "QUANTITE_GAZ_TORCHEE")
    @JsonView(View.basic.class)
    private BigDecimal quantiteGazTorchee;

    @Column(name = "TAUX_GAZ_TORCHEE_PAR_CAUSE")
    @JsonView(View.basic.class)
    private BigDecimal tauxGazTorcheeParCause;

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUnite() { return unite; }
    public void setUnite(String unite) { this.unite = unite; }

    public LocalDate getPmois() { return pmois; }
    public void setPmois(LocalDate pmois) { this.pmois = pmois; }

    public String getCodeCauseTorchage() { return codeCauseTorchage; }
    public void setCodeCauseTorchage(String codeCauseTorchage) { this.codeCauseTorchage = codeCauseTorchage; }

    public String getLibCauseTorchage() { return libCauseTorchage; }
    public void setLibCauseTorchage(String libCauseTorchage) { this.libCauseTorchage = libCauseTorchage; }

    public BigDecimal getQuantiteGazTorchee() { return quantiteGazTorchee; }
    public void setQuantiteGazTorchee(BigDecimal quantiteGazTorchee) { this.quantiteGazTorchee = quantiteGazTorchee; }

    public BigDecimal getTauxGazTorcheeParCause() { return tauxGazTorcheeParCause; }
    public void setTauxGazTorcheeParCause(BigDecimal tauxGazTorcheeParCause) { this.tauxGazTorcheeParCause = tauxGazTorcheeParCause; }
}
