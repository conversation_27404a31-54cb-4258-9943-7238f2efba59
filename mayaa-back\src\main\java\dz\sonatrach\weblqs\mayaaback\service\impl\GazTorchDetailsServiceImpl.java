package dz.sonatrach.weblqs.mayaaback.service.impl;

import java.time.LocalDate;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import dz.sonatrach.weblqs.mayaaback.exception.NotFoundException;
import dz.sonatrach.weblqs.mayaaback.model.GazTorchDetails;
import dz.sonatrach.weblqs.mayaaback.repo.GazTorchDetailsRepository;
import dz.sonatrach.weblqs.mayaaback.service.GazTorchDetailsService;

@Service
public class GazTorchDetailsServiceImpl implements GazTorchDetailsService {

    @Autowired
    private GazTorchDetailsRepository gazTorchDetailsRepository;

    @Override
    public List<GazTorchDetails> getListCauseGazTorche(LocalDate pmois, String unite) {
        List<GazTorchDetails> details = gazTorchDetailsRepository.findByPmoisAndUnite(pmois, unite);
        if (details.isEmpty()) {
            throw new NotFoundException("No GazTorchDetails found for the given month and unit.");
        }
        return details;
    }
}
