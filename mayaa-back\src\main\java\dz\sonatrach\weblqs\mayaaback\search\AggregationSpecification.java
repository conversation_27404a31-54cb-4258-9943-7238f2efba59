package dz.sonatrach.weblqs.mayaaback.search;

import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.*;
import java.util.HashMap;
import java.util.Map;

public class AggregationSpecification<T> implements Specification<T> {
    private final Map<String, String> aggregationFields;

    public AggregationSpecification(Map<String, String> aggregationFields) {
        this.aggregationFields = aggregationFields;
    }

    @Override
    public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        // Skip normal filtering and focus on aggregation
        if (aggregationFields != null && !aggregationFields.isEmpty()) {
            query.groupBy(aggregationFields.keySet().stream()
                    .map(root::get)
                    .toArray(Expression[]::new));

            // Add aggregations to the selection
            aggregationFields.forEach((field, aggType) -> {
                switch (aggType.toLowerCase()) {
                    case "count":
                        query.multiselect(root.get(field), criteriaBuilder.count(root.get(field)));
                        break;
                    case "average":
                        query.multiselect(root.get(field), criteriaBuilder.avg(root.get(field)));
                        break;
                    default:
                        throw new IllegalArgumentException("Unsupported aggregation type: " + aggType);
                }
            });
        }

        // No filter predicate for aggregation
        return criteriaBuilder.conjunction();
    }

    public Map<String, Object> getAggregations(Root<T> root, CriteriaBuilder criteriaBuilder) {
        Map<String, Object> aggregationResults = new HashMap<>();

        aggregationFields.forEach((field, aggType) -> {
            switch (aggType.toLowerCase()) {
                case "count":
                    aggregationResults.put("count_" + field, criteriaBuilder.count(root.get(field)));
                    break;
                case "average":
                    aggregationResults.put("average_" + field, criteriaBuilder.avg(root.get(field)));
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported aggregation type: " + aggType);
            }
        });

        return aggregationResults;
    }
}
