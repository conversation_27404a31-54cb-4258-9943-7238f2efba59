package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.NombreArretTrainsParClasseCause;
import dz.sonatrach.weblqs.mayaaback.repo.NombreArretTrainsParClasseCauseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("api/nombre-arret-trains-par-classe-cause")
public class NombreArretTrainsParClasseCauseController {
    @Autowired
    private NombreArretTrainsParClasseCauseRepository nombreArretTrainsParClasseCauseRepository;

    @GetMapping("/{unite}/{pdate}")
    public ResponseEntity<List<NombreArretTrainsParClasseCause>> getByUniteAndPdate(@PathVariable String unite, @PathVariable String pdate) {
        LocalDate date = LocalDate.parse(pdate, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<NombreArretTrainsParClasseCause> result = nombreArretTrainsParClasseCauseRepository.findByUniteAndPdate(unite, date);
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }
}
