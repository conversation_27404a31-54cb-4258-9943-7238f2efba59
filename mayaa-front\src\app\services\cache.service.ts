import { Injectable } from '@angular/core';
import { Observable, of, shareReplay, tap } from 'rxjs';

/**
 * Service de cache pour optimiser les performances en évitant les requêtes répétées
 */
@Injectable({
  providedIn: 'root'
})
export class CacheService {
  private cache = new Map<string, { data: any; timestamp: number; observable?: Observable<any> }>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes par défaut

  constructor() {}

  /**
   * Récupère une valeur du cache ou exécute la fonction si elle n'existe pas
   * @param key Clé du cache
   * @param factory Fonction qui retourne un Observable pour récupérer les données
   * @param ttl Durée de vie en millisecondes (optionnel)
   * @returns Observable avec les données
   */
  getOrSet<T>(key: string, factory: () => Observable<T>, ttl: number = this.DEFAULT_TTL): Observable<T> {
    const cached = this.cache.get(key);
    const now = Date.now();

    // Si les données sont en cache et encore valides
    if (cached && (now - cached.timestamp) < ttl) {
      if (cached.observable) {
        // Si une requête est en cours, la partager
        return cached.observable;
      } else {
        // Retourner les données en cache
        return of(cached.data);
      }
    }

    // Créer une nouvelle requête et la mettre en cache
    const observable = factory().pipe(
      tap(data => {
        // Stocker les données en cache une fois reçues
        this.cache.set(key, { data, timestamp: now });
      }),
      shareReplay(1) // Partager le résultat avec tous les abonnés
    );

    // Stocker l'observable en cours pour éviter les requêtes multiples
    this.cache.set(key, { data: null, timestamp: now, observable });

    return observable;
  }

  /**
   * Supprime une entrée du cache
   * @param key Clé à supprimer
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Supprime toutes les entrées du cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Supprime les entrées expirées du cache
   * @param ttl Durée de vie en millisecondes
   */
  cleanup(ttl: number = this.DEFAULT_TTL): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if ((now - value.timestamp) >= ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Génère une clé de cache pour les données d'une unité et période
   * @param endpoint Nom de l'endpoint
   * @param unite Code de l'unité
   * @param periode Période
   * @returns Clé de cache
   */
  generateKey(endpoint: string, unite?: string, periode?: string): string {
    const parts = [endpoint];
    if (unite) parts.push(unite);
    if (periode) parts.push(periode);
    return parts.join('_');
  }
}
