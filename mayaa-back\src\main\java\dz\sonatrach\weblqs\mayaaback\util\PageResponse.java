package dz.sonatrach.weblqs.mayaaback.util;

import java.util.List;

import org.springframework.data.domain.Page;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.search.Aggregation;
import dz.sonatrach.weblqs.mayaaback.views.View;

public class PageResponse<T> {

	@JsonView(View.basic.class)
	private List<T> content;
	@JsonView(View.basic.class)
    private int pageNumber;
	@JsonView(View.basic.class)
    private int pageSize;
	@JsonView(View.basic.class)
    private long totalRecords;
	@JsonView(View.basic.class)
    private int totalPages;
	@JsonView(View.basic.class)
	private List<Aggregation> aggregations;	

    
    public PageResponse(Page<T> page,List<Aggregation> aggregations) {
        this.content = page.getContent();
        this.pageNumber = page.getNumber();
        this.pageSize = page.getSize();
        this.totalRecords = page.getTotalElements();
        this.totalPages = page.getTotalPages();
        this.aggregations = aggregations;
    }
    
    public PageResponse(Page<T> page) {
        this.content = page.getContent();
        this.pageNumber = page.getNumber();
        this.pageSize = page.getSize();
        this.totalRecords = page.getTotalElements();
        this.totalPages = page.getTotalPages();
    }

    
    public List<T> getContent() {
        return content;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public int getPageSize() {
        return pageSize;
    }

    public long getTotalElements() {
        return totalRecords;
    }

    public int getTotalPages() {
        return totalPages;
    }


	public List<Aggregation> getAggregations() {
		return aggregations;
	}

	public void setAggregations(List<Aggregation> aggregations) {
		this.aggregations = aggregations;
	}
}
