package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * The persistent class for the Z_UTILISATEURS database table.
 * 
 */
@Entity
@Table(name="M_UTILISATEURS")
@NamedQuery(name="MUtilisateur.findAll", query="SELECT z FROM MUtilisateur z")
@ToString
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class MUtilisateur implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name="ID_UTILISATEUR")
	@JsonView(View.basic.class)
	@EqualsAndHashCode.Include
	private Long idUtilisateur;
	
	@Column(name="K_ID_USER")
	@JsonView(View.listUser.class)
	private String kiduser ;
	
	@JsonView(View.listUser.class)
	private String username;

	@JsonView(View.basic.class)
	private String nom;
	
	@JsonView(View.basic.class)
	private String prenom;
	
	@JsonView(View.listUser.class)
	private String matricule;

	@JsonView(View.listUser.class)
	private String structure;
	
	@JsonView(View.listUser.class)
	private String email;
	
	@JsonView(View.DetailUser.class)
	private String sexe ;
	
	@JsonView(View.listUser.class)
	private String fonction ;
	
	@Temporal(TemporalType.DATE)
	@Column(name="DATE_CREATION")
	@JsonView(View.DetailUser.class)
	private LocalDate dateCreation;
	
	@Temporal(TemporalType.DATE)
	@Column(name="DATE_MODIFICATION")
	@JsonView(View.DetailUser.class)
	private LocalDate dateModification;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="LAST_CONNECTED")
	@JsonView(View.listUser.class)
	private LocalDateTime  lastconnected;

	public MUtilisateur() {
	}

	public Long getIdUtilisateur() {
		return idUtilisateur;
	}

	public void setIdUtilisateur(Long idUtilisateur) {
		this.idUtilisateur = idUtilisateur;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getNom() {
		return nom;
	}

	public void setNom(String nom) {
		this.nom = nom;
	}

	public String getPrenom() {
		return prenom;
	}

	public void setPrenom(String prenom) {
		this.prenom = prenom;
	}

	public String getMatricule() {
		return matricule;
	}

	public void setMatricule(String matricule) {
		this.matricule = matricule;
	}

	public String getStructure() {
		return structure;
	}

	public void setStructure(String structure) {
		this.structure = structure;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSexe() {
		return sexe;
	}

	public void setSexe(String sexe) {
		this.sexe = sexe;
	}

	public LocalDate getDateCreation() {
		return dateCreation;
	}

	public void setDateCreation(LocalDate dateCreation) {
		this.dateCreation = dateCreation;
	}

	public LocalDate getDateModification() {
		return dateModification;
	}

	public void setDateModification(LocalDate dateModification) {
		this.dateModification = dateModification;
	}

	public String getFonction() {
		return fonction;
	}

	public void setFonction(String fonction) {
		this.fonction = fonction;
	}

	public String getkIdUser() {
		return kiduser;
	}

	public void setkIdUser(String kIdUser) {
		this.kiduser = kIdUser;
	}


}