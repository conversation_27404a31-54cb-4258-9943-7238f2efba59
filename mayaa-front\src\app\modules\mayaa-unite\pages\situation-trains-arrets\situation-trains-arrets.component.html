<div class="grid">
  <!-- Résumé global -->
  <div class="col-12">
    <p-card header="Résumé Global des Arrêts">
      <div class="grid">
        <div class="col-12 md:col-3">
          <div class="stat-card total">
            <div class="stat-value">{{ getTotalArrets() }}</div>
            <div class="stat-label">Total Arrêts</div>
          </div>
        </div>
        <div class="col-12 md:col-3">
          <div class="stat-card internes">
            <div class="stat-value">{{ situationInternes?.nombreArrets || 0 }}</div>
            <div class="stat-label">Arrêts Internes</div>
            <div class="stat-percentage">{{ formatNumber(getPourcentageInternes()) }}%</div>
          </div>
        </div>
        <div class="col-12 md:col-3">
          <div class="stat-card externes">
            <div class="stat-value">{{ situationExternes?.nombreArrets || 0 }}</div>
            <div class="stat-label">Arrêts Externes</div>
            <div class="stat-percentage">{{ formatNumber(getPourcentageExternes()) }}%</div>
          </div>
        </div>
        <div class="col-12 md:col-3" *ngIf="analyseComplexe">
          <div class="stat-card complexe">
            <div class="stat-value">{{ formatNumber(analyseComplexe.gazTorchePourcentage) }}%</div>
            <div class="stat-label">Gaz Torché</div>
          </div>
        </div>
      </div>
    </p-card>
  </div>

  <!-- Analyse des causes internes -->
  <div class="col-12 lg:col-6" *ngIf="situationInternes">
    <p-card header="Analyse des Causes Internes">
      <div class="situation-content">
        <div class="mb-3">
          <span [ngClass]="getTypeBadgeClass('INTERNES')" class="badge">
            {{ situationInternes.typeCause }}
          </span>
        </div>

        <div class="mb-4">
          <h5 class="section-title">Causes Principales</h5>
          <div class="causes-list">
            <span
              *ngFor="let cause of formatCausesList(situationInternes.causesPrincipales)"
              class="cause-tag">
              {{ cause }}
            </span>
          </div>
        </div>

        <div class="mb-4">
          <h5 class="section-title">Analyse</h5>
          <p class="analysis-text">{{ situationInternes.analyses }}</p>
        </div>

        <div class="stats-row">
          <div class="stat-item">
            <span class="stat-number">{{ situationInternes.nombreArrets }}</span>
            <span class="stat-desc">Arrêts</span>
          </div>
        </div>
      </div>
    </p-card>
  </div>

  <!-- Analyse des causes externes -->
  <div class="col-12 lg:col-6" *ngIf="situationExternes">
    <p-card header="Analyse des Causes Externes">
      <div class="situation-content">
        <div class="mb-3">
          <span [ngClass]="getTypeBadgeClass('EXTERNES')" class="badge">
            {{ situationExternes.typeCause }}
          </span>
        </div>

        <div class="mb-4">
          <h5 class="section-title">Causes Principales</h5>
          <div class="causes-list">
            <span
              *ngFor="let cause of formatCausesList(situationExternes.causesPrincipales)"
              class="cause-tag">
              {{ cause }}
            </span>
          </div>
        </div>

        <div class="mb-4">
          <h5 class="section-title">Analyse</h5>
          <p class="analysis-text">{{ situationExternes.analyses }}</p>
        </div>

        <div class="stats-row">
          <div class="stat-item">
            <span class="stat-number">{{ situationExternes.nombreArrets }}</span>
            <span class="stat-desc">Arrêts</span>
          </div>
        </div>
      </div>
    </p-card>
  </div>

  <!-- Analyse complexe -->
  <div class="col-12" *ngIf="analyseComplexe">
    <p-card header="Analyse Complexe - Autoconsommation et Gaz Torché">
      <div class="grid">
        <div class="col-12 md:col-6">
          <div class="analysis-section">
            <h5 class="section-title">Indicateurs de Performance</h5>
            <div class="indicators-grid">
              <div class="indicator-item">
                <span class="indicator-label">Autoconsommation</span>
                <span class="indicator-value">{{ formatNumber(analyseComplexe.autoconsommationPourcentage) }}%</span>
              </div>
              <div class="indicator-item">
                <span class="indicator-label">AC Nette</span>
                <span class="indicator-value">{{ formatNumber(analyseComplexe.autoconsommationNettePourcentage) }}%</span>
              </div>
              <div class="indicator-item">
                <span class="indicator-label">Gaz Torché</span>
                <span class="indicator-value">{{ formatNumber(analyseComplexe.gazTorchePourcentage) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="col-12 md:col-6">
          <div class="analysis-section">
            <h5 class="section-title">Sièges Causes Gaz Torché</h5>
            <div class="causes-list">
              <span
                *ngFor="let siege of formatCausesList(analyseComplexe.siegesCausesGazTorche)"
                class="cause-tag siege-tag">
                {{ siege }}
              </span>
            </div>

            <h5 class="section-title mt-4">Causes Récurrentes</h5>
            <div class="causes-list">
              <span
                *ngFor="let cause of formatCausesList(analyseComplexe.causesRecurrentes)"
                class="cause-tag recurrent-tag">
                {{ cause }}
              </span>
            </div>
          </div>
        </div>

        <div class="col-12">
          <div class="analysis-section">
            <h5 class="section-title">Analyse Globale</h5>
            <p class="analysis-text">{{ analyseComplexe.analyses }}</p>
          </div>
        </div>
      </div>
    </p-card>
  </div>

  <!-- Message de chargement -->
  <div class="col-12" *ngIf="loading">
    <p-card>
      <div class="text-center">
        <p-progressSpinner></p-progressSpinner>
        <p class="mt-3">Chargement des données...</p>
      </div>
    </p-card>
  </div>

  <!-- Message si aucune donnée -->
  <div class="col-12" *ngIf="!loading && situationData.length === 0">
    <p-card>
      <div class="text-center">
        <p-message severity="info" text="Aucune donnée disponible pour cette période"></p-message>
      </div>
    </p-card>
  </div>
</div>
