package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.TestDataComplexe;


@Repository
public interface TestDataComplexeRepository extends JpaRepository<TestDataComplexe, Long> {
	Optional<TestDataComplexe> findByPmoisAndUnite(LocalDate pMois, String pUnite);
	
}