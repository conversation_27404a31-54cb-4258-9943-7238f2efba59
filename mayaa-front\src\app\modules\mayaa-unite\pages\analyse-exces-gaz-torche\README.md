# Analyse de l'excès de l'autoconsommation - Gaz Torché

## Description

Ce composant affiche l'analyse de l'excès de l'autoconsommation pour le gaz torché. Contrairement au composant d'analyse générale, celui-ci ne concerne **que les trains** et n'a pas d'onglets séparés.

## Structure des fichiers

```
analyse-exces-gaz-torche/
├── analyse-exces-gaz-torche.component.ts      # Composant principal
├── analyse-exces-gaz-torche.component.html    # Template sans TabView
├── analyse-exces-gaz-torche.component.scss    # Styles du composant
├── analyse-exces-gaz-torche.component.spec.ts # Tests unitaires
└── README.md                                   # Documentation
```

## Fonctionnalités

### Affichage Principal
- **Page unique** sans onglets (contrairement à l'analyse générale)
- **Organisation par train** avec sections distinctes
- **Statistiques globales** en en-tête
- **Tableaux par train** avec tri et données détaillées

### Statistiques Affichées
- **Total AC** : Somme totale de l'autoconsommation (10³ CM³ GN)
- **Nombre d'éléments** : Total des problèmes identifiés
- **Trains concernés** : Nombre de trains avec des données

### Fonctionnalités par Train
- **En-tête coloré** avec nom du train
- **Statistiques du train** : Total AC et nombre d'éléments
- **Tableau détaillé** trié par AC décroissant
- **Badges visuels** pour états et criticité

## Colonnes du tableau

| Colonne | Description | Type | Tri |
|---------|-------------|------|-----|
| CODE AC | Code d'autoconsommation | string | ✓ |
| PROBLÈME SPÉCIFIQUE | Description du problème | string | ✓ |
| INTITULÉ | Intitulé de l'élément | string | ✓ |
| AC (10³ CM³ GN) | Quantité d'autoconsommation | number | ✓ |
| CLASSE CAUSES | Classification des causes | string | ✓ |
| CAUSES | Description des causes | string | ✗ |
| ACTIONS | Actions correctives | string | ✗ |
| CLASSES | Classe de criticité | string | ✓ |
| ÉTAT | État actuel | string | ✓ |
| N° | Numéro de référence | string | ✓ |

## Différences avec l'analyse générale

### Structure
- **Pas de TabView** : Affichage direct des trains
- **Focus trains uniquement** : Pas de section utilités
- **Statistiques enrichies** : Plus de métriques affichées

### Design
- **En-têtes colorés** : Dégradé rouge pour les trains
- **Cartes statistiques** : Dégradé bleu-violet
- **Badges spécialisés** : Criticité et état différenciés

### Fonctionnalités
- **Calculs automatiques** : Totaux par train et globaux
- **Distributions** : Répartition par état et criticité
- **Tri par défaut** : AC décroissant pour chaque train

## Intégration API

### Service AnalyseExcesGazTorcheService

```typescript
// Méthodes disponibles
getGazTorcheData(unite: string, mois: string): Observable<AnalyseExcesGazTorche[]>
getGazTorcheDataWithStats(unite: string, mois: string): Observable<AnalyseExcesGazTorcheResponse>
getGlobalStatistics(unite: string, mois: string): Observable<GlobalStatistics>
```

### Endpoints API à implémenter

```
GET /api/mayaa-unite/analyse-exces-gaz-torche?unite={unite}&mois={mois}
GET /api/mayaa-unite/analyse-exces-gaz-torche/stats?unite={unite}&mois={mois}
GET /api/mayaa-unite/analyse-exces-gaz-torche/global-stats?unite={unite}&mois={mois}
```

## Modèles de données

Les interfaces TypeScript sont définies dans `models/analyse-exces-gaz-torche.model.ts` :

- `AnalyseExcesGazTorche` : Structure pour les données de gaz torché
- `AnalyseExcesGazTorcheResponse` : Réponse avec statistiques
- `GlobalStatistics` : Statistiques globales et par train
- `TrainStatistics` : Statistiques spécifiques à un train

## Styles et thème

### Couleurs Spécifiques
- **Cartes statistiques** : Dégradé bleu-violet (#667eea → #764ba2)
- **En-têtes trains** : Dégradé rouge (#ff6b6b → #ee5a24)
- **Badges criticité** : Rouge (Critique), Jaune (Majeur), Vert (Mineur)
- **Badges état** : Selon l'état (En cours, Terminé, etc.)

### Design Responsive
- **Grilles adaptatives** : Statistiques et résumés
- **Tableaux responsifs** : Scroll horizontal sur mobile
- **En-têtes flexibles** : Réorganisation sur petits écrans

## Données mock

Le composant utilise des données mock réalistes incluant :

### Types de Problèmes
- **Torchage excessif** lors démarrage
- **Fuites vannes** torche secours
- **Gaz purge** compresseur
- **Arrêts urgence** avec dépressurisation
- **Perte efficacité** brûleur torche
- **Tests sécurité** réglementaires

### Trains Simulés
- **Train 100** : 3 problèmes
- **Train 200** : 3 problèmes  
- **Train 300** : 1 problème

### Classes de Criticité
- **Critique** : Problèmes majeurs nécessitant action immédiate
- **Majeur** : Problèmes importants à traiter rapidement
- **Mineur** : Problèmes mineurs pour optimisation

## Méthodes Utilitaires

### Calculs Statistiques
```typescript
getTotalAC(): number                           // Total global AC
getTotalItems(): number                        // Nombre total d'éléments
getUniqueTrains(): string[]                   // Liste des trains uniques
getTrainTotalAC(trainName: string): number    // Total AC par train
getTrainItemCount(trainName: string): number  // Nombre d'éléments par train
```

### Distributions
```typescript
getStatusDistribution(): { [key: string]: number }      // Répartition par état
getCriticalityDistribution(): { [key: string]: number } // Répartition par criticité
```

## Prochaines étapes

### Backend (À Implémenter)
1. **Créer la vue** : `ANALYSE_EXCES_GAZ_TORCHE`
2. **Implémenter le contrôleur** : `AnalyseExcesGazTorcheController`
3. **Créer les DTOs** : Classes Java pour les réponses
4. **Optimiser les requêtes** : Index et performances

### Frontend (Améliorations)
1. **Connecter aux contrôles** : Unité et mois depuis la topbar
2. **Remplacer les données mock** : Par les vrais appels API
3. **Ajouter graphiques** : Visualisations des distributions
4. **Export données** : PDF/Excel des rapports

### Intégration
1. **Tests d'intégration** : Avec données réelles
2. **Validation métier** : Conformité avec les processus
3. **Performance** : Optimisation chargement et affichage

## Route d'accès

Le composant est accessible via la route `/mayaa-unite/analyse-exces-gt` et s'intègre dans le module `MayaaUniteModule`.

## Tests

### Couverture des Tests
- ✅ **Création et initialisation** du composant
- ✅ **Chargement des données** avec service mock
- ✅ **Calculs statistiques** (totaux, distributions)
- ✅ **Filtrage par train** et méthodes utilitaires
- ✅ **Gestion des états** (loading, erreurs)
- ✅ **Configuration des colonnes** et tri

### Tests du Service
- ✅ **Appels API mock** avec délais réalistes
- ✅ **Structure des données** et validation
- ✅ **Calculs statistiques** automatiques
- ✅ **Distributions** par état et criticité
