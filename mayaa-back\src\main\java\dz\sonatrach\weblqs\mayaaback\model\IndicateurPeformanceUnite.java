package dz.sonatrach.weblqs.mayaaback.model;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.io.Serializable;
@Entity
@Table(name="INDICATEURS_PERFORMANCE_COMPLEXE")
@NamedQuery(name="IndicateurPeformanceUnite.findAll", query="SELECT a FROM IndicateurPeformanceUnite a")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class IndicateurPeformanceUnite implements Serializable {
   	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="ID")		
	@JsonView(View.basic.class)
	private long IdAuto;

    @Column(name = "UNITE")
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "MOIS")
    @JsonView(View.basic.class)
    private LocalDate mois;

    @Column(name = "PRODUCTION")
    @JsonView(View.basic.class)
    private BigDecimal production;

    @Column(name = "PRODUCTION_DESIGN")
    @JsonView(View.basic.class)
    private BigDecimal productionDesign;

    @Column(name = "TRAIN_MARCHE")
    @JsonView(View.basic.class)
    private BigDecimal trainMarche;

    @Column(name = "TP")
    @JsonView(View.basic.class)
    private BigDecimal tp;

    @Column(name = "TF")
    @JsonView(View.basic.class)
    private BigDecimal tf;

    @Column(name = "TC")
    @JsonView(View.basic.class)
    private BigDecimal tc;

    @Column(name = "TAP")
    @JsonView(View.basic.class)
    private BigDecimal tap;

    @Column(name = "TAI")
    @JsonView(View.basic.class)
    private BigDecimal tai;

    // Getters and Setters
    public long getIdAuto() {
        return IdAuto;
    }

    public void setIdAuto(long idAuto) {
        IdAuto = idAuto;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public BigDecimal getProduction() {
        return production;
    }

    public void setProduction(BigDecimal production) {
        this.production = production;
    }

    public BigDecimal getProductionDesign() {
        return productionDesign;
    }

    public void setProductionDesign(BigDecimal productionDesign) {
        this.productionDesign = productionDesign;
    }

    public BigDecimal getTp() {
        return tp;
    }

    public void setTp(BigDecimal tp) {
        this.tp = tp;
    }

    public BigDecimal getTrainMarche() {
        return trainMarche;
    }

    public void setTrainMarche(BigDecimal trainMarche) {
        this.trainMarche = trainMarche;
    }

    public BigDecimal getTf() {
        return tf;
    }

    public void setTf(BigDecimal tf) {
        this.tf = tf;
    }

    public BigDecimal getTc() {
        return tc;
    }

    public void setTc(BigDecimal tc) {
        this.tc = tc;
    }

    public BigDecimal getTap() {
        return tap;
    }

    public void setTap(BigDecimal tap) {
        this.tap = tap;
    }

    public BigDecimal getTai() {
        return tai;
    }

    public void setTai(BigDecimal tai) {
        this.tai = tai;
    }
}
