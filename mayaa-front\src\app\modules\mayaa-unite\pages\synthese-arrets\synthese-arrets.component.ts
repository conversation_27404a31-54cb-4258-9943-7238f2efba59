import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { ArretsService } from '../../services/arrets.service';
import { CalendarService } from '../../../../services/calendar.service';
import { SyntheseArrets } from '../../../../model/arrets.interface';

@Component({
  selector: 'app-synthese-arrets',
  templateUrl: './synthese-arrets.component.html',
  styleUrls: ['./synthese-arrets.component.scss']
})
export class SyntheseArretsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Données
  syntheseData: SyntheseArrets[] = [];
  trainsDisponibles: string[] = [];
  trainSelectionne = 'COMPLEXE';
  loading = false;

  // Configuration du tableau
  cols = [
    { field: 'codeTrain', header: 'Train', width: '100px' },
    { field: 'nbreHeures', header: 'Nb He<PERSON>', width: '100px' },
    { field: 'map', header: 'MAP (%)', width: '100px' },
    { field: 'acNette', header: 'AC Nette (%)', width: '120px' },
    { field: 'gtInterne', header: 'GT Interne', width: '100px' },
    { field: 'gtExterne', header: 'GT Externe', width: '100px' },
    { field: 'plus24hInterne', header: '>24h Int.', width: '100px' },
    { field: 'plus24hExterne', header: '>24h Ext.', width: '100px' },
    { field: 'moins24hInterne', header: '<24h Int.', width: '100px' },
    { field: 'moins24hExterne', header: '<24h Ext.', width: '100px' },
    { field: 'sid', header: 'SID', width: '80px' },
    { field: 'dei', header: 'DEI', width: '80px' },
    { field: 'av', header: 'AV', width: '80px' },
    { field: 'ap', header: 'AP', width: '80px' }
  ];

  // Données pour les graphiques
  chartDataMAP: any;
  chartDataAC: any;
  chartDataGT: any;
  chartOptions: any;

  constructor(
    private arretsService: ArretsService,
    private calendarService: CalendarService
  ) {
    this.initChartOptions();
  }

  ngOnInit(): void {
    // Écouter les changements de calendrier et d'unité
    combineLatest([
      this.calendarService.selectedDate$,
      this.calendarService.selectedUnite$
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([date, unite]) => {
      if (date && unite) {
        this.chargerDonnees(unite, this.formatDateForApi(date));
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private chargerDonnees(unite: string, mois: string): void {
    this.loading = true;

    // Charger les trains disponibles
    this.arretsService.getTrainsDisponibles(unite, mois).pipe(
      takeUntil(this.destroy$)
    ).subscribe(trains => {
      this.trainsDisponibles = trains;
      if (!trains.includes(this.trainSelectionne)) {
        this.trainSelectionne = trains[0] || 'COMPLEXE';
      }
    });

    // Charger les données de synthèse
    this.arretsService.getSyntheseArrets(unite, mois).pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.syntheseData = data;
      this.preparerDonneesGraphiques();
      this.loading = false;
    });
  }

  onTrainChange(): void {
    const currentDate = this.calendarService.pmoisFormat();
    const currentUnite = this.calendarService.selectedUnite();

    if (currentDate && currentUnite) {
      this.loading = true;

      if (this.trainSelectionne === 'COMPLEXE') {
        this.arretsService.getSyntheseArrets(currentUnite, currentDate).pipe(
          takeUntil(this.destroy$)
        ).subscribe(data => {
          this.syntheseData = data;
          this.preparerDonneesGraphiques();
          this.loading = false;
        });
      } else {
        this.arretsService.getSyntheseArretsParTrain(currentUnite, currentDate, this.trainSelectionne).pipe(
          takeUntil(this.destroy$)
        ).subscribe(data => {
          this.syntheseData = data;
          this.preparerDonneesGraphiques();
          this.loading = false;
        });
      }
    }
  }

  private preparerDonneesGraphiques(): void {
    if (this.syntheseData.length === 0) return;

    const labels = this.syntheseData.map(item => item.codeTrain);
    const mapData = this.syntheseData.map(item => item.map);
    const acData = this.syntheseData.map(item => item.acNette);
    const gtInterneData = this.syntheseData.map(item => item.gtInterne);
    const gtExterneData = this.syntheseData.map(item => item.gtExterne);

    // Graphique MAP
    this.chartDataMAP = {
      labels: labels,
      datasets: [{
        label: 'MAP (%)',
        data: mapData,
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    };

    // Graphique AC Nette
    this.chartDataAC = {
      labels: labels,
      datasets: [{
        label: 'AC Nette (%)',
        data: acData,
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1
      }]
    };

    // Graphique GT (Interne vs Externe)
    this.chartDataGT = {
      labels: labels,
      datasets: [
        {
          label: 'GT Interne',
          data: gtInterneData,
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        },
        {
          label: 'GT Externe',
          data: gtExterneData,
          backgroundColor: 'rgba(255, 159, 64, 0.6)',
          borderColor: 'rgba(255, 159, 64, 1)',
          borderWidth: 1
        }
      ]
    };
  }

  private initChartOptions(): void {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    };
  }

  private formatDateForApi(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  // Méthodes utilitaires pour l'affichage
  getRowClass(rowData: SyntheseArrets): string {
    if (rowData.codeTrain === 'TOTAL' || rowData.codeTrain === 'COMPLEXE') {
      return 'font-bold bg-blue-50';
    }
    return '';
  }

  formatNumber(value: number): string {
    return value?.toFixed(1) || '0.0';
  }
}
