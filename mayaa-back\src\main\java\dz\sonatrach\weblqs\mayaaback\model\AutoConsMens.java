package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Entity
@Table(name="AUTO_CONS_MOIS")
@NamedQuery(name="AutoConsMens.findAll", query="SELECT a FROM AutoConsMens a")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class AutoConsMens implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="IDAUTO")		
	@JsonView(View.basic.class)
	private long IdAuto;
	
	@Column(name="PMOIS")
	@JsonView(View.basic.class)
	private LocalDate pmois;
	
	@Column(name="UNITE")
	@JsonView(View.basic.class)
    private String unite;
	
	@Column(name="AUTO_CONS_MOIS_NET")
	@JsonView(View.basic.class)
    private BigDecimal autoConsMoisNet;
	
	@Column(name="RECEPTION_GN_MOIS")
	@JsonView(View.basic.class)
    private BigDecimal receptionGnMois;
	
	@Column(name="TRANSFORME_GN_MOIS")
	@JsonView(View.basic.class)
    private BigDecimal transformeGnMois;
	
	@Column(name="TAUX_GN_TRANS")
	@JsonView(View.basic.class)
    private BigDecimal tauxGnTrans;
	
	@Column(name="TAUX_AUTO_CONS_MOIS_GLOBAL")
	@JsonView(View.basic.class)
    private BigDecimal tauxAutoConsMoisGlobal;
	
	@Column(name="TAUX_AUTO_CONS_OBJ")
	@JsonView(View.basic.class)
    private BigDecimal tauxAutoConsObj;
	
	@Column(name="GAZ_TORCHEE_MOIS")
	@JsonView(View.basic.class)
    private BigDecimal gazTorcheeMois;
	
	@Column(name="TAUX_GAZ_TORCHEE_MOIS")
	@JsonView(View.basic.class)
    private BigDecimal tauxGazTorcheeMois;

    // Getters and Setters
    public LocalDate getPMois() {
        return pmois;
    }

    public void setPMois(LocalDate pmois) {
        this.pmois = pmois;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public BigDecimal getAutoConsMoisNet() {
        return autoConsMoisNet;
    }

    public void setAutoConsMoisNet(BigDecimal autoConsMoisNet) {
        this.autoConsMoisNet = autoConsMoisNet;
    }

    public BigDecimal getReceptionGnMois() {
        return receptionGnMois;
    }

    public void setReceptionGnMois(BigDecimal receptionGnMois) {
        this.receptionGnMois = receptionGnMois;
    }

    public BigDecimal getTransformeGnMois() {
        return transformeGnMois;
    }

    public void setTransformeGnMois(BigDecimal transformeGnMois) {
        this.transformeGnMois = transformeGnMois;
    }

    public BigDecimal getTauxGnTrans() {
        return tauxGnTrans;
    }

    public void setTauxGnTrans(BigDecimal tauxGnTrans) {
        this.tauxGnTrans = tauxGnTrans;
    }

    public BigDecimal getTauxAutoConsMoisGlobal() {
        return tauxAutoConsMoisGlobal;
    }

    public void setTauxAutoConsMoisGlobal(BigDecimal tauxAutoConsMoisGlobal) {
        this.tauxAutoConsMoisGlobal = tauxAutoConsMoisGlobal;
    }

    public BigDecimal getTauxAutoConsObj() {
        return tauxAutoConsObj;
    }

    public void setTauxAutoConsObj(BigDecimal tauxAutoConsObj) {
        this.tauxAutoConsObj = tauxAutoConsObj;
    }

    public BigDecimal getGazTorcheeMois() {
        return gazTorcheeMois;
    }

    public void setGazTorcheeMois(BigDecimal gazTorcheeMois) {
        this.gazTorcheeMois = gazTorcheeMois;
    }

    public BigDecimal getTauxGazTorcheeMois() {
        return tauxGazTorcheeMois;
    }

    public void setTauxGazTorcheeMois(BigDecimal tauxGazTorcheeMois) {
        this.tauxGazTorcheeMois = tauxGazTorcheeMois;
    }
}