package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.EvolutionCausesTrain;
import dz.sonatrach.weblqs.mayaaback.repo.EvolutionCausesTrainRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Contrôleur pour l'accès aux données de la vue EVOLUTION_CAUSES_TRAIN.
 *
 * Endpoints principaux :
 *   GET /api/evolution-causes-train/{unite}/{annee}
 *   GET /api/evolution-causes-train/{unite}/{annee}/{train}
 *   GET /api/evolution-causes-train/{unite}/{mois}/trains
 *   GET /api/evolution-causes-train/{unite}/{mois}/sieges
 *   GET /api/evolution-causes-train/{unite}/{mois}/classes
 *
 * - unite : code de l'unité (String, ex: "5X3")
 * - annee : année au format yyyy (ex: 2024)
 * - train : code du train ou "COMPLEXE" pour l'ensemble de l'unité
 * - mois : période au format ddMMyyyy (ex: 01012024)
 *
 * Réponses :
 *   200 OK : données trouvées
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/evolution-causes-train")
public class EvolutionCausesTrainController {
    
    @Autowired
    private EvolutionCausesTrainRepository evolutionCausesTrainRepository;
    
    /**
     * Récupère les données d'évolution pour une unité et une année (tous trains)
     * @param unite Code de l'unité
     * @param annee Année au format yyyy
     * @return Liste des données d'évolution pour tous les trains
     */
    @GetMapping("/{unite}/{annee}")
    public ResponseEntity<List<EvolutionCausesTrain>> getByUniteAndAnnee(
            @PathVariable String unite, 
            @PathVariable int annee) {
        try {
            List<EvolutionCausesTrain> result = evolutionCausesTrainRepository.findByUniteAndAnnee(unite, annee);
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Récupère les données d'évolution pour une unité, une année et un train spécifique
     * Si train = "COMPLEXE", retourne les données agrégées pour l'ensemble de l'unité
     * @param unite Code de l'unité
     * @param annee Année au format yyyy
     * @param train Code du train ou "COMPLEXE"
     * @return Liste des données d'évolution pour le train ou l'unité complète
     */
    @GetMapping("/{unite}/{annee}/{train}")
    public ResponseEntity<List<EvolutionCausesTrain>> getByUniteAndAnneeAndTrain(
            @PathVariable String unite, 
            @PathVariable int annee,
            @PathVariable String train) {
        try {
            List<EvolutionCausesTrain> result;
            
            if ("COMPLEXE".equalsIgnoreCase(train)) {
                // Données agrégées pour l'ensemble de l'unité
                result = evolutionCausesTrainRepository.findAggregatedByUniteAndAnnee(unite, annee);
            } else {
                // Données pour un train spécifique
                result = evolutionCausesTrainRepository.findByUniteAndCodeTrainAndAnnee(unite, train, annee);
            }
            
            if (result.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Récupère les trains disponibles pour une unité et un mois donnés
     * Inclut automatiquement "COMPLEXE" en première position
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des codes de trains avec "COMPLEXE" en premier
     */
    @GetMapping("/{unite}/{mois}/trains")
    public ResponseEntity<List<String>> getTrainsByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<String> trains = evolutionCausesTrainRepository.findDistinctTrainsByUniteAndMois(unite, date);
            
            // Ajouter "COMPLEXE" en première position
            trains.add(0, "COMPLEXE");
            
            return ResponseEntity.ok(trains);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Récupère les sièges de causes distincts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des sièges de causes distincts
     */
    @GetMapping("/{unite}/{mois}/sieges")
    public ResponseEntity<List<String>> getSiegesByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<String> sieges = evolutionCausesTrainRepository.findDistinctSiegesByUniteAndMois(unite, date);
            
            if (sieges.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(sieges);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Récupère les classes de causes distinctes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois au format ddMMyyyy
     * @return Liste des classes de causes distinctes
     */
    @GetMapping("/{unite}/{mois}/classes")
    public ResponseEntity<List<String>> getClassesByUniteAndMois(
            @PathVariable String unite, 
            @PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<String> classes = evolutionCausesTrainRepository.findDistinctClassesByUniteAndMois(unite, date);
            
            if (classes.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(classes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
