import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { KUser } from '../../model/KUser';

@Injectable({ providedIn: 'root' })
export class UserPermissionService {
  private kyCloakUser: KUser = new KUser();

  constructor(private keycloak: KeycloakService) {}

  public initUserPermissions(tokenParsed: any): void {
    if (!tokenParsed?.resource_access?.['mayaa']?.roles) {
      throw new Error('Invalid token structure');
    }

    const roles = tokenParsed.resource_access['mayaa'].roles;
    this.kyCloakUser.permissions = roles.filter((role: string) => role.startsWith('$'));
    this.kyCloakUser.roles = roles.filter((role: string) => !role.startsWith('$'));
  }

  public hasRoles(roles: string[]): boolean {
    return roles.some(role =>
      this.kyCloakUser.roles?.includes(role)
    );
  }

  public hasPermissions(permissions: string[]): boolean {
    return permissions.some(permission =>
      this.kyCloakUser.permissions?.includes(permission)
    );
  }

  /**
   * Vérifie si l'utilisateur a au moins un rôle commençant par le préfixe spécifié
   * @param prefix Le préfixe à vérifier
   * @returns true si l'utilisateur a au moins un rôle commençant par le préfixe, false sinon
   */
  public hasRoleStartingWith(prefix: string): boolean {
    return this.kyCloakUser.roles?.some(role => role.startsWith(prefix)) || false;
  }
}
