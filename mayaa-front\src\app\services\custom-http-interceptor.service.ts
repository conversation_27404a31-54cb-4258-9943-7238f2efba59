import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, delay, of, tap, throwError, timeout } from 'rxjs';
import { MessageService } from 'primeng/api';
import { SpinnerService } from './spinner.service';


@Injectable()
export class CustomHttpInterceptor implements HttpInterceptor {
  constructor(
    private spinnerService: SpinnerService,
    private messageService: MessageService
  ) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    this.spinnerService.show();
    return next.handle(req).pipe(
      delay(0),
      tap(
        (event: HttpEvent<any>) => {
          if (event instanceof HttpResponse) {
            this.spinnerService.hide();
          }
        },
        (error) => {
          this.spinnerService.count=1;
          this.spinnerService.hide();
          this.messageService.add({
            key: 'message',
            severity: 'error',
            summary: 'Erreur '+error.status,
            detail: error.statusText,
            closable:false,
            //life: 10000,
          });
          //console.log(error)
        }
      ),
      timeout({
        each: 10000,
        with: () => throwError(() => {
          this.spinnerService.hide();
          this.messageService.add({
            key:'toast',
            severity: 'warn',
            summary: 'Problème de connexion',
            detail: 'Votre connexion est trop lente',
            life:10000
          });
        } ),
      })
    );
  }
}
