{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-megamenu.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatform<PERSON>rowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, numberAttribute, booleanAttribute, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nconst _c0 = [\"menubar\"];\nconst _c1 = (a0, a1) => ({\n  \"p-megamenu-root-list\": a0,\n  \"p-submenu-list p-megamenu-submenu\": a1\n});\nconst _c2 = a0 => ({\n  \"p-menuitem-link\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction MegaMenuSub_ul_0_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getSubmenuHeaderClass(ctx_r1.submenu))(\"ngStyle\", ctx_r1.getItemProp(ctx_r1.submenu, \"style\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getItemLabel(ctx_r1.submenu));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 11);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"style\"))(\"ngClass\", ctx_r1.getSeparatorItemClass(processedItem_r3));\n    i0.ɵɵattribute(\"id\", ctx_r1.getItemId(processedItem_r3))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getItemProp(processedItem_r3, \"icon\"))(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getItemLabel(processedItem_r3), \" \");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.getItemLabel(processedItem_r3), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getItemProp(processedItem_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 27)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"horizontal\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"vertical\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 29);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 14)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 1, null, 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.megaMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.megaMenu.submenuIconTemplate);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_1_Template, 1, 4, \"span\", 19)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 20)(3, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_span_5_Template, 2, 2, \"span\", 21)(6, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(4);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"target\", ctx_r1.getItemProp(processedItem_r3, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(11, _c2, ctx_r1.getItemProp(processedItem_r3, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r1.getItemProp(processedItem_r3, \"url\"), i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r1.getItemProp(processedItem_r3, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"escape\"))(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemGroup(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getItemProp(processedItem_r3, \"icon\"))(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"tabindex\", -1);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getItemLabel(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.getItemLabel(processedItem_r3), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getItemProp(processedItem_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 27)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"horizontal\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orientation === \"vertical\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 29);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 14)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 1, null, 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.megaMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.megaMenu.submenuIconTemplate);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_1_Template, 1, 4, \"span\", 19)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_2_Template, 2, 1, \"span\", 20)(3, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(5, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_span_5_Template, 2, 2, \"span\", 21)(6, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.getItemProp(processedItem_r3, \"routerLink\"))(\"queryParams\", ctx_r1.getItemProp(processedItem_r3, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r1.getItemProp(processedItem_r3, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c3))(\"target\", ctx_r1.getItemProp(processedItem_r3, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, ctx_r1.getItemProp(processedItem_r3, \"disabled\")))(\"fragment\", ctx_r1.getItemProp(processedItem_r3, \"fragment\"))(\"queryParamsHandling\", ctx_r1.getItemProp(processedItem_r3, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r1.getItemProp(processedItem_r3, \"preserveFragment\"))(\"skipLocationChange\", ctx_r1.getItemProp(processedItem_r3, \"skipLocationChange\"))(\"replaceUrl\", ctx_r1.getItemProp(processedItem_r3, \"replaceUrl\"))(\"state\", ctx_r1.getItemProp(processedItem_r3, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r1.getItemProp(processedItem_r3, \"automationId\"))(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"escape\"))(\"ngIfElse\", htmlRouteLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemGroup(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_1_Template, 7, 13, \"a\", 16)(2, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_a_2_Template, 7, 23, \"a\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.getItemProp(processedItem_r3, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getItemProp(processedItem_r3, \"routerLink\"));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_1_Template, 1, 0, null, 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, processedItem_r3.item));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megaMenuSub_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-megaMenuSub\", 37);\n    i0.ɵɵlistener(\"itemClick\", function MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megaMenuSub_1_Template_p_megaMenuSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.itemClick.emit($event));\n    })(\"itemMouseEnter\", function MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megaMenuSub_1_Template_p_megaMenuSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const submenu_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"id\", ctx_r1.getSubListId(submenu_r8))(\"submenu\", submenu_r8)(\"items\", submenu_r8.items)(\"itemTemplate\", ctx_r1.itemTemplate)(\"menuId\", ctx_r1.menuId)(\"focusedItemId\", ctx_r1.focusedItemId)(\"level\", ctx_r1.level + 1)(\"root\", false);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_p_megaMenuSub_1_Template, 1, 8, \"p-megaMenuSub\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r9 = ctx.$implicit;\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getColumnClass(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", col_r9);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵtemplate(2, MegaMenuSub_ul_0_ng_template_3_li_1_div_5_div_2_Template, 2, 2, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"data-pc-section\", \"panel\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"grid\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", processedItem_r3.items);\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 12, 1)(2, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function MegaMenuSub_ul_0_ng_template_3_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemClick($event, processedItem_r3));\n    })(\"mouseenter\", function MegaMenuSub_ul_0_ng_template_3_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r3\n      }));\n    });\n    i0.ɵɵtemplate(3, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 14)(4, MegaMenuSub_ul_0_ng_template_3_li_1_ng_container_4_Template, 2, 4, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MegaMenuSub_ul_0_ng_template_3_li_1_div_5_Template, 3, 3, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    const processedItem_r3 = ctx_r9.$implicit;\n    const index_r11 = ctx_r9.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.getItemProp(processedItem_r3, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getItemProp(processedItem_r3, \"style\"))(\"ngClass\", ctx_r1.getItemClass(processedItem_r3))(\"tooltipOptions\", ctx_r1.getItemProp(processedItem_r3, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r1.getItemId(processedItem_r3))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r1.isItemActive(processedItem_r3))(\"data-p-focused\", ctx_r1.isItemFocused(processedItem_r3))(\"data-p-disabled\", ctx_r1.isItemDisabled(processedItem_r3))(\"aria-label\", ctx_r1.getItemLabel(processedItem_r3))(\"aria-disabled\", ctx_r1.isItemDisabled(processedItem_r3) || undefined)(\"aria-haspopup\", ctx_r1.isItemGroup(processedItem_r3) && !ctx_r1.getItemProp(processedItem_r3, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r1.isItemGroup(processedItem_r3) ? ctx_r1.isItemActive(processedItem_r3) : undefined)(\"aria-level\", ctx_r1.level + 1)(\"aria-setsize\", ctx_r1.getAriaSetSize())(\"aria-posinset\", ctx_r1.getAriaPosInset(index_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r3) && ctx_r1.isItemGroup(processedItem_r3));\n  }\n}\nfunction MegaMenuSub_ul_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenuSub_ul_0_ng_template_3_li_0_Template, 1, 4, \"li\", 9)(1, MegaMenuSub_ul_0_ng_template_3_li_1_Template, 6, 21, \"li\", 10);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r3) && ctx_r1.getItemProp(processedItem_r3, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r3) && !ctx_r1.getItemProp(processedItem_r3, \"separator\"));\n  }\n}\nfunction MegaMenuSub_ul_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 5, 0);\n    i0.ɵɵlistener(\"keydown\", function MegaMenuSub_ul_0_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuKeydown.emit($event));\n    })(\"focus\", function MegaMenuSub_ul_0_Template_ul_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuFocus.emit($event));\n    })(\"blur\", function MegaMenuSub_ul_0_Template_ul_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuBlur.emit($event));\n    });\n    i0.ɵɵtemplate(2, MegaMenuSub_ul_0_li_2_Template, 2, 3, \"li\", 6)(3, MegaMenuSub_ul_0_ng_template_3_Template, 2, 2, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, ctx_r1.root, !ctx_r1.root))(\"tabindex\", ctx_r1.tabindex);\n    i0.ɵɵattribute(\"role\", ctx_r1.root ? \"menubar\" : \"menu\")(\"id\", ctx_r1.id)(\"aria-orientation\", ctx_r1.orientation)(\"aria-activedescendant\", ctx_r1.focusedItemId)(\"data-pc-section\", ctx_r1.root ? \"root\" : \"submenu\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submenu);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.items);\n  }\n}\nconst _c5 = [\"menubutton\"];\nconst _c6 = [\"rootmenu\"];\nconst _c7 = [\"*\"];\nconst _c8 = (a0, a1) => ({\n  \"p-megamenu p-component\": true,\n  \"p-megamenu-horizontal\": a0,\n  \"p-megamenu-vertical\": a1\n});\nfunction MegaMenu_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MegaMenu_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, MegaMenu_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate);\n  }\n}\nfunction MegaMenu_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MegaMenu_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, MegaMenu_div_4_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate);\n  }\n}\nfunction MegaMenu_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nclass MegaMenuSub {\n  el;\n  megaMenu;\n  id;\n  items;\n  itemTemplate;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  disabled = false;\n  orientation;\n  activeItem;\n  submenu;\n  tabindex = 0;\n  root = false;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  menubarViewChild;\n  constructor(el, megaMenu) {\n    this.el = el;\n    this.megaMenu = megaMenu;\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n  }\n  getSubListId(processedItem) {\n    return `${this.getItemId(processedItem)}_list`;\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem': true,\n      'p-menuitem-active p-highlight': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem-separator': true\n    };\n  }\n  getColumnClass(processedItem) {\n    let length = this.isItemGroup(processedItem) ? processedItem.items.length : 0;\n    let columnClass;\n    switch (length) {\n      case 2:\n        columnClass = 'p-megamenu-col-6';\n        break;\n      case 3:\n        columnClass = 'p-megamenu-col-4';\n        break;\n      case 4:\n        columnClass = 'p-megamenu-col-3';\n        break;\n      case 6:\n        columnClass = 'p-megamenu-col-2';\n        break;\n      default:\n        columnClass = 'p-megamenu-col-12';\n        break;\n    }\n    return columnClass;\n  }\n  getSubmenuHeaderClass(processedItem) {\n    return {\n      'p-megamenu-submenu-header p-submenu-header': true,\n      'p-disabled': this.isItemDisabled(processedItem),\n      ...this.getItemProp(processedItem, 'class')\n    };\n  }\n  isSubmenuVisible(submenu) {\n    if (this.submenu && !this.root) {\n      return this.isItemVisible(submenu);\n    } else {\n      return true;\n    }\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    return ObjectUtils.isNotEmpty(this.activeItem) ? this.activeItem.key === processedItem.key : false;\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  onItemMouseEnter(param) {\n    const {\n      event,\n      processedItem\n    } = param;\n    this.itemMouseEnter.emit({\n      originalEvent: event,\n      processedItem\n    });\n  }\n  static ɵfac = function MegaMenuSub_Factory(t) {\n    return new (t || MegaMenuSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(forwardRef(() => MegaMenu)));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MegaMenuSub,\n    selectors: [[\"p-megaMenuSub\"]],\n    viewQuery: function MegaMenuSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubarViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      id: \"id\",\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"level\", \"level\", numberAttribute],\n      focusedItemId: \"focusedItemId\",\n      disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n      orientation: \"orientation\",\n      activeItem: \"activeItem\",\n      submenu: \"submenu\",\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n      root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute]\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"menubar\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [3, \"ngClass\", \"tabindex\", \"keydown\", \"focus\", \"blur\", 4, \"ngIf\"], [3, \"keydown\", \"focus\", \"blur\", \"ngClass\", \"tabindex\"], [\"role\", \"presentation\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"presentation\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"separator\", 3, \"ngStyle\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngStyle\", \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [\"class\", \"p-megamenu-panel\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"data-pc-section\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-megamenu-panel\"], [1, \"p-megamenu-grid\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [3, \"id\", \"submenu\", \"items\", \"itemTemplate\", \"menuId\", \"focusedItemId\", \"level\", \"root\", \"itemClick\", \"itemMouseEnter\", 4, \"ngFor\", \"ngForOf\"], [3, \"itemClick\", \"itemMouseEnter\", \"id\", \"submenu\", \"items\", \"itemTemplate\", \"menuId\", \"focusedItemId\", \"level\", \"root\"]],\n    template: function MegaMenuSub_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MegaMenuSub_ul_0_Template, 4, 12, \"ul\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isSubmenuVisible(ctx.submenu));\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, AngleDownIcon, AngleRightIcon, MegaMenuSub],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-megaMenuSub',\n      template: `\n        <ul\n            *ngIf=\"isSubmenuVisible(submenu)\"\n            #menubar\n            [ngClass]=\"{ 'p-megamenu-root-list': root, 'p-submenu-list p-megamenu-submenu': !root }\"\n            [attr.role]=\"root ? 'menubar' : 'menu'\"\n            [attr.id]=\"id\"\n            [attr.aria-orientation]=\"orientation\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.data-pc-section]=\"root ? 'root' : 'submenu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <li *ngIf=\"submenu\" [ngClass]=\"getSubmenuHeaderClass(submenu)\" [ngStyle]=\"getItemProp(submenu, 'style')\" role=\"presentation\">{{ getItemLabel(submenu) }}</li>\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!megaMenu.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'horizontal'\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'vertical'\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"megaMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!megaMenu.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'horizontal'\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'vertical'\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"megaMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <div *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\" class=\"p-megamenu-panel\" [attr.data-pc-section]=\"'panel'\">\n                        <div class=\"p-megamenu-grid\" [attr.data-pc-section]=\"'grid'\">\n                            <div *ngFor=\"let col of processedItem.items\" [ngClass]=\"getColumnClass(processedItem)\">\n                                <p-megaMenuSub\n                                    *ngFor=\"let submenu of col\"\n                                    [id]=\"getSubListId(submenu)\"\n                                    [submenu]=\"submenu\"\n                                    [items]=\"submenu.items\"\n                                    [itemTemplate]=\"itemTemplate\"\n                                    [menuId]=\"menuId\"\n                                    [focusedItemId]=\"focusedItemId\"\n                                    [level]=\"level + 1\"\n                                    [root]=\"false\"\n                                    (itemClick)=\"itemClick.emit($event)\"\n                                    (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                                >\n                                </p-megaMenuSub>\n                            </div>\n                        </div>\n                    </div>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: MegaMenu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => MegaMenu)]\n    }]\n  }], {\n    id: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    orientation: [{\n      type: Input\n    }],\n    activeItem: [{\n      type: Input\n    }],\n    submenu: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    menubarViewChild: [{\n      type: ViewChild,\n      args: ['menubar', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * MegaMenu is navigation component that displays submenus together.\n * @group Components\n */\nclass MegaMenu {\n  document;\n  platformId;\n  el;\n  renderer;\n  config;\n  cd;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Defines the orientation.\n   * @group Props\n   */\n  orientation = 'horizontal';\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @group Props\n   */\n  disabled = false;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  templates;\n  menubutton;\n  rootmenu;\n  startTemplate;\n  endTemplate;\n  menuIconTemplate;\n  submenuIconTemplate;\n  itemTemplate;\n  outsideClickListener;\n  resizeListener;\n  dirty = false;\n  focused = false;\n  activeItem = signal(null);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  get visibleItems() {\n    const processedItem = ObjectUtils.isNotEmpty(this.activeItem()) ? this.activeItem() : null;\n    return processedItem ? processedItem.items.reduce((items, col) => {\n      col.forEach(submenu => {\n        submenu.items.forEach(a => {\n          items.push(a);\n        });\n      });\n      return items;\n    }, []) : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItem = this.focusedItemInfo();\n    return focusedItem?.item && focusedItem.item?.id ? focusedItem.item.id : ObjectUtils.isNotEmpty(focusedItem.key) ? `${this.id}_${focusedItem.key}` : null;\n  }\n  constructor(document, platformId, el, renderer, config, cd) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.config = config;\n    this.cd = cd;\n    effect(() => {\n      const activeItem = this.activeItem();\n      if (ObjectUtils.isNotEmpty(activeItem)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    this.id = this.id || UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this.startTemplate = item.template;\n          break;\n        case 'end':\n          this.endTemplate = item.template;\n          break;\n        case 'menuicon':\n          this.menuIconTemplate = item.template;\n          break;\n        case 'submenuicon':\n          this.submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '', columnIndex) {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + (columnIndex !== undefined ? columnIndex + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey,\n        columnIndex: columnIndex !== undefined ? columnIndex : parent.columnIndex !== undefined ? parent.columnIndex : index\n      };\n      newItem['items'] = level === 0 && item.items && item.items.length > 0 ? item.items.map((_items, _index) => this.createProcessedItems(_items, level + 1, newItem, key, _index)) : this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItem.set(null);\n      this.focusedItemInfo.set({\n        index,\n        key,\n        parentKey,\n        item\n      });\n      this.dirty = !root;\n      DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        const rootProcessedItem = root ? processedItem : this.activeItem();\n        this.hide(originalEvent);\n        this.changeFocusedItemInfo(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n        DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!DomHandler.isTouchDevice()) {\n      this.onItemChange(event);\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = DomHandler.findSingle(this.rootmenu?.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (ObjectUtils.isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = ObjectUtils.isNotEmpty(items);\n    if (grouped) {\n      this.activeItem.set(processedItem);\n    }\n    this.focusedItemInfo.set({\n      index,\n      key,\n      parentKey,\n      item\n    });\n    grouped && (this.dirty = true);\n    isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n  }\n  hide(event, isFocus) {\n    this.activeItem.set(null);\n    this.focusedItemInfo.set({\n      index: -1,\n      key: '',\n      parentKey: '',\n      item: null\n    });\n    isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n    this.dirty = false;\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    if (this.focusedItemInfo().index === -1) {\n      const index = this.findFirstFocusedItemIndex();\n      const processedItem = this.findVisibleItem(index);\n      this.focusedItemInfo.set({\n        index,\n        key: processedItem.key,\n        parentKey: processedItem.parentKey,\n        item: processedItem.item\n      });\n    }\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return ObjectUtils.isNotEmpty(this.activeItem()) ? this.activeItem().key === processedItem.key : false;\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemInfo(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  changeFocusedItemInfo(event, index) {\n    const processedItem = this.findVisibleItem(index);\n    if (ObjectUtils.isNotEmpty(processedItem)) {\n      const {\n        key,\n        parentKey,\n        item\n      } = processedItem;\n      this.focusedItemInfo.set({\n        index,\n        key: key ? key : '',\n        parentKey,\n        item\n      });\n    }\n    this.scrollInView();\n  }\n  onArrowDownKey(event) {\n    if (this.orientation === 'horizontal') {\n      if (ObjectUtils.isNotEmpty(this.activeItem()) && this.activeItem().key === this.focusedItemInfo().key) {\n        const {\n          key,\n          item\n        } = this.activeItem();\n        this.focusedItemInfo.set({\n          index: -1,\n          key: '',\n          parentKey: key,\n          item\n        });\n      } else {\n        const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (grouped) {\n          const {\n            parentKey,\n            key,\n            item\n          } = processedItem;\n          this.onItemChange({\n            originalEvent: event,\n            processedItem\n          });\n          this.focusedItemInfo.set({\n            index: -1,\n            key: key,\n            parentKey: parentKey,\n            item: item\n          });\n          this.searchValue = '';\n        }\n      }\n    }\n    const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n    this.changeFocusedItemInfo(event, itemIndex);\n    event.preventDefault();\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n    const grouped = this.isProccessedItemGroup(processedItem);\n    if (grouped) {\n      if (this.orientation === 'vertical') {\n        if (ObjectUtils.isNotEmpty(this.activeItem()) && this.activeItem().key === processedItem.key) {\n          this.focusedItemInfo.set({\n            index: -1,\n            key: '',\n            parentKey: this.activeItem().key,\n            item: processedItem.item\n          });\n        } else {\n          const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n          const grouped = this.isProccessedItemGroup(processedItem);\n          if (grouped) {\n            this.onItemChange({\n              originalEvent: event,\n              processedItem\n            });\n            this.focusedItemInfo.set({\n              index: -1,\n              key: processedItem.key,\n              parentKey: processedItem.parentKey,\n              item: processedItem.item\n            });\n            this.searchValue = '';\n          }\n        }\n      }\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemInfo(event, itemIndex);\n    } else {\n      const columnIndex = processedItem.columnIndex + 1;\n      const itemIndex = this.visibleItems.findIndex(item => item.columnIndex === columnIndex);\n      itemIndex !== -1 && this.changeFocusedItemInfo(event, itemIndex);\n    }\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey && this.orientation === 'horizontal') {\n      if (this.focusedItemInfo().index !== -1) {\n        const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (!grouped && ObjectUtils.isNotEmpty(this.activeItem)) {\n          if (this.focusedItemInfo().index === 0) {\n            this.focusedItemInfo.set({\n              index: this.activeItem().index,\n              key: this.activeItem().key,\n              parentKey: this.activeItem().parentKey,\n              item: processedItem.item\n            });\n            this.activeItem.set(null);\n          } else {\n            this.changeFocusedItemInfo(event, this.findFirstItemIndex());\n          }\n        }\n      }\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemInfo(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n    const grouped = this.isProccessedItemGroup(processedItem);\n    if (grouped) {\n      if (this.orientation === 'horizontal') {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n        this.changeFocusedItemInfo(event, itemIndex);\n      }\n    } else {\n      if (this.orientation === 'vertical' && ObjectUtils.isNotEmpty(this.activeItem())) {\n        if (processedItem.columnIndex === 0) {\n          this.focusedItemInfo.set({\n            index: this.activeItem().index,\n            key: this.activeItem().key,\n            parentKey: this.activeItem().parentKey,\n            item: processedItem.item\n          });\n          this.activeItem.set(null);\n        }\n      }\n      const columnIndex = processedItem.columnIndex - 1;\n      const itemIndex = this.visibleItems.findIndex(item => item.columnIndex === columnIndex);\n      itemIndex !== -1 && this.changeFocusedItemInfo(event, itemIndex);\n    }\n    event.preventDefault();\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemInfo(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemInfo(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    if (ObjectUtils.isNotEmpty(this.activeItem())) {\n      this.focusedItemInfo.set({\n        index: this.activeItem().index,\n        key: this.activeItem().key,\n        item: this.activeItem().item\n      });\n      this.activeItem.set(null);\n    }\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = DomHandler.findSingle(this.rootmenu?.el?.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.changeFocusedItemInfo(event, this.findFirstFocusedItemIndex());\n    }\n    event.preventDefault();\n  }\n  findVisibleItem(index) {\n    return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return ObjectUtils.findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          this.hide(event, true);\n        });\n      }\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.rootmenu?.el.nativeElement !== event.target && !this.rootmenu?.el.nativeElement.contains(event.target);\n          if (isOutsideContainer) {\n            this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      this.outsideClickListener();\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n  }\n  static ɵfac = function MegaMenu_Factory(t) {\n    return new (t || MegaMenu)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MegaMenu,\n    selectors: [[\"p-megaMenu\"]],\n    contentQueries: function MegaMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function MegaMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubutton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      orientation: \"orientation\",\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c7,\n    decls: 7,\n    vars: 26,\n    consts: [[\"rootmenu\", \"\"], [\"legacy\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-megamenu-start\", 4, \"ngIf\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"itemTemplate\", \"items\", \"menuId\", \"root\", \"orientation\", \"ariaLabel\", \"disabled\", \"tabindex\", \"activeItem\", \"level\", \"ariaLabelledBy\", \"focusedItemId\"], [\"class\", \"p-megamenu-end\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-megamenu-start\"], [4, \"ngTemplateOutlet\"], [1, \"p-megamenu-end\"]],\n    template: function MegaMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 2);\n        i0.ɵɵtemplate(1, MegaMenu_div_1_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementStart(2, \"p-megaMenuSub\", 4, 0);\n        i0.ɵɵlistener(\"itemClick\", function MegaMenu_Template_p_megaMenuSub_itemClick_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event));\n        })(\"menuFocus\", function MegaMenu_Template_p_megaMenuSub_menuFocus_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuFocus($event));\n        })(\"menuBlur\", function MegaMenu_Template_p_megaMenuSub_menuBlur_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuBlur($event));\n        })(\"menuKeydown\", function MegaMenu_Template_p_megaMenuSub_menuKeydown_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"itemMouseEnter\", function MegaMenu_Template_p_megaMenuSub_itemMouseEnter_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemMouseEnter($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, MegaMenu_div_4_Template, 2, 1, \"div\", 5)(5, MegaMenu_ng_template_5_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const legacy_r3 = i0.ɵɵreference(6);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(23, _c8, ctx.orientation == \"horizontal\", ctx.orientation == \"vertical\"))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"megamenu\")(\"id\", ctx.id);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.startTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"itemTemplate\", ctx.itemTemplate)(\"items\", ctx.processedItems)(\"menuId\", ctx.id)(\"root\", true)(\"orientation\", ctx.orientation)(\"ariaLabel\", ctx.ariaLabel)(\"disabled\", ctx.disabled)(\"tabindex\", !ctx.disabled ? ctx.tabindex : -1)(\"activeItem\", ctx.activeItem())(\"level\", 0)(\"ariaLabelledBy\", ctx.ariaLabelledBy)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined);\n        i0.ɵɵattribute(\"id\", ctx.id + \"_list\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.endTemplate)(\"ngIfElse\", legacy_r3);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, MegaMenuSub],\n    styles: [\"@layer primeng{.p-megamenu-root-list{margin:0;padding:0;list-style:none}.p-megamenu-root-list>.p-menuitem{position:relative}.p-megamenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-megamenu .p-menuitem-text{line-height:1}.p-megamenu-panel{display:none;position:absolute;width:auto;z-index:1}.p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{display:block}.p-megamenu-submenu{margin:0;padding:0;list-style:none}.p-megamenu-horizontal{align-items:center}.p-megamenu-horizontal .p-megamenu-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-megamenu-horizontal .p-megamenu-end{margin-left:auto;align-self:center}.p-megamenu-vertical .p-megamenu-root-list{flex-direction:column}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{left:100%;top:0}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-content>.p-menuitem-link>.p-submenu-icon:not(svg){margin-left:auto}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-content>.p-menuitem-link>.p-icon-wrapper{margin-left:auto}.p-megamenu-grid{display:flex}.p-megamenu-col-2,.p-megamenu-col-3,.p-megamenu-col-4,.p-megamenu-col-6,.p-megamenu-col-12{flex:0 0 auto;padding:.5rem}.p-megamenu-col-2{width:16.6667%}.p-megamenu-col-3{width:25%}.p-megamenu-col-4{width:33.3333%}.p-megamenu-col-6{width:50%}.p-megamenu-col-12{width:100%}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-megaMenu',\n      template: `\n        <div\n            [ngClass]=\"{ 'p-megamenu p-component': true, 'p-megamenu-horizontal': orientation == 'horizontal', 'p-megamenu-vertical': orientation == 'vertical' }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'megamenu'\"\n            [attr.id]=\"id\"\n        >\n            <div class=\"p-megamenu-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <p-megaMenuSub\n                #rootmenu\n                [itemTemplate]=\"itemTemplate\"\n                [items]=\"processedItems\"\n                [attr.id]=\"id + '_list'\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [orientation]=\"orientation\"\n                [ariaLabel]=\"ariaLabel\"\n                [disabled]=\"disabled\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [activeItem]=\"activeItem()\"\n                [level]=\"0\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-megaMenuSub>\n            <div class=\"p-megamenu-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-megamenu-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-megamenu-root-list{margin:0;padding:0;list-style:none}.p-megamenu-root-list>.p-menuitem{position:relative}.p-megamenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-megamenu .p-menuitem-text{line-height:1}.p-megamenu-panel{display:none;position:absolute;width:auto;z-index:1}.p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{display:block}.p-megamenu-submenu{margin:0;padding:0;list-style:none}.p-megamenu-horizontal{align-items:center}.p-megamenu-horizontal .p-megamenu-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-megamenu-horizontal .p-megamenu-end{margin-left:auto;align-self:center}.p-megamenu-vertical .p-megamenu-root-list{flex-direction:column}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{left:100%;top:0}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-content>.p-menuitem-link>.p-submenu-icon:not(svg){margin-left:auto}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-content>.p-menuitem-link>.p-icon-wrapper{margin-left:auto}.p-megamenu-grid{display:flex}.p-megamenu-col-2,.p-megamenu-col-3,.p-megamenu-col-4,.p-megamenu-col-6,.p-megamenu-col-12{flex:0 0 auto;padding:.5rem}.p-megamenu-col-2{width:16.6667%}.p-megamenu-col-3{width:25%}.p-megamenu-col-4{width:33.3333%}.p-megamenu-col-6{width:50%}.p-megamenu-col-12{width:100%}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i5.PrimeNGConfig\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    menubutton: [{\n      type: ViewChild,\n      args: ['menubutton']\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }]\n  });\n})();\nclass MegaMenuModule {\n  static ɵfac = function MegaMenuModule_Factory(t) {\n    return new (t || MegaMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MegaMenuModule,\n    declarations: [MegaMenu, MegaMenuSub],\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon],\n    exports: [MegaMenu, RouterModule, TooltipModule, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon],\n      exports: [MegaMenu, RouterModule, TooltipModule, SharedModule],\n      declarations: [MegaMenu, MegaMenuSub]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MegaMenu, MegaMenuModule, MegaMenuSub };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,wBAAwB;AAAA,EACxB,qCAAqC;AACvC;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,mBAAmB;AAAA,EACnB,cAAc;AAChB;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,sBAAsB,OAAO,OAAO,CAAC,EAAE,WAAW,OAAO,YAAY,OAAO,SAAS,OAAO,CAAC;AAC7H,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa,OAAO,OAAO,CAAC;AAAA,EAC1D;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,EAAE;AAC5C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,WAAW,OAAO,sBAAsB,gBAAgB,CAAC;AACjI,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,WAAW;AAAA,EACzF;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,YAAY,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,aAAa,gBAAgB,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,iBAAiB,CAAC;AAChF,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EACpE;AACF;AACA,SAAS,8GAA8G,IAAI,KAAK;AAC9H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAC5C,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAC5C,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAC9G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+GAA+G,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,gHAAgH,GAAG,GAAG,kBAAkB,EAAE;AACxS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,YAAY;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,UAAU;AAAA,EACzD;AACF;AACA,SAAS,+FAA+F,IAAI,KAAK;AAAC;AAClH,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gGAAgG,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa;AAAA,EAChD;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+FAA+F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,MAAM,EAAE;AAC7O,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,mBAAmB;AAC1D,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,mBAAmB;AAAA,EACvE;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,gFAAgF,GAAG,GAAG,gBAAgB,EAAE;AAC3hB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC;AAChK,IAAG,YAAY,QAAQ,OAAO,YAAY,kBAAkB,KAAK,GAAM,aAAa,EAAE,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,mBAAmB,QAAQ,EAAE,YAAY,EAAE;AAC5M,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,YAAY;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,MAAM,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACnI,IAAG,YAAY,mBAAmB,MAAM,EAAE,YAAY,EAAE;AAAA,EAC1D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,gBAAgB,GAAM,cAAc;AACnF,IAAG,YAAY,mBAAmB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,iBAAiB,CAAC;AAChF,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EACpE;AACF;AACA,SAAS,8GAA8G,IAAI,KAAK;AAC9H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAC5C,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAC5C,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,8FAA8F,IAAI,KAAK;AAC9G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+GAA+G,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,gHAAgH,GAAG,GAAG,kBAAkB,EAAE;AACxS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,YAAY;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB,UAAU;AAAA,EACzD;AACF;AACA,SAAS,+FAA+F,IAAI,KAAK;AAAC;AAClH,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gGAAgG,GAAG,GAAG,eAAe,EAAE;AAAA,EAC1I;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,mBAAmB,aAAa;AAAA,EAChD;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+FAA+F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,kFAAkF,GAAG,GAAG,MAAM,EAAE;AAC7O,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS,mBAAmB;AAC1D,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,mBAAmB;AAAA,EACvE;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wEAAwE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,gFAAgF,GAAG,GAAG,gBAAgB,EAAE;AAC3hB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,eAAe,OAAO,YAAY,kBAAkB,aAAa,CAAC,EAAE,oBAAoB,wBAAwB,EAAE,2BAA2B,OAAO,YAAY,kBAAkB,yBAAyB,KAAQ,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC,EAAE,YAAY,OAAO,YAAY,kBAAkB,UAAU,CAAC,EAAE,uBAAuB,OAAO,YAAY,kBAAkB,qBAAqB,CAAC,EAAE,oBAAoB,OAAO,YAAY,kBAAkB,kBAAkB,CAAC,EAAE,sBAAsB,OAAO,YAAY,kBAAkB,oBAAoB,CAAC,EAAE,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC33B,IAAG,YAAY,qBAAqB,OAAO,YAAY,kBAAkB,cAAc,CAAC,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ;AACrI,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,MAAM,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,iBAAiB;AACnG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,OAAO,CAAC;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iEAAiE,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,iEAAiE,GAAG,IAAI,KAAK,EAAE;AACpL,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,kBAAkB,YAAY,CAAC;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,4EAA4E,IAAI,KAAK;AAAC;AAC/F,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6EAA6E,GAAG,GAAG,aAAa;AAAA,EACnH;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,MAAM,EAAE;AAC9F,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,iBAAiB,IAAI,CAAC;AAAA,EACrI;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,iBAAiB,EAAE;AACxC,IAAG,WAAW,aAAa,SAAS,4GAA4G,QAAQ;AACtJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,kBAAkB,SAAS,iHAAiH,QAAQ;AACrJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,MAAM,OAAO,aAAa,UAAU,CAAC,EAAE,WAAW,UAAU,EAAE,SAAS,WAAW,KAAK,EAAE,gBAAgB,OAAO,YAAY,EAAE,UAAU,OAAO,MAAM,EAAE,iBAAiB,OAAO,aAAa,EAAE,SAAS,OAAO,QAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,EACtP;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,iBAAiB,EAAE;AACpH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,eAAe,gBAAgB,CAAC;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,MAAM;AAAA,EACjC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,OAAO,EAAE;AAC1F,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,YAAY,mBAAmB,MAAM;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,iBAAiB,KAAK;AAAA,EACjD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE;AAC9C,IAAG,WAAW,SAAS,SAAS,kEAAkE,QAAQ;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,QAAQ,gBAAgB,CAAC;AAAA,IACpE,CAAC,EAAE,cAAc,SAAS,uEAAuE,QAAQ;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB;AAAA,QAC5C;AAAA,QACA,eAAe;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE;AAChM,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,EAAE;AACpF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAmB,OAAO;AAChC,UAAM,YAAY,OAAO;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAChE,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,WAAW,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,YAAY,kBAAkB,gBAAgB,CAAC;AAClM,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,mBAAmB,UAAU,EAAE,oBAAoB,OAAO,aAAa,gBAAgB,CAAC,EAAE,kBAAkB,OAAO,cAAc,gBAAgB,CAAC,EAAE,mBAAmB,OAAO,eAAe,gBAAgB,CAAC,EAAE,cAAc,OAAO,aAAa,gBAAgB,CAAC,EAAE,iBAAiB,OAAO,eAAe,gBAAgB,KAAK,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,IAAI,IAAI,SAAS,MAAS,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,IAAI,OAAO,aAAa,gBAAgB,IAAI,MAAS,EAAE,cAAc,OAAO,QAAQ,CAAC,EAAE,gBAAgB,OAAO,eAAe,CAAC,EAAE,iBAAiB,OAAO,gBAAgB,SAAS,CAAC;AACtuB,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,mBAAmB,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,CAAC;AAAA,EACtG;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,8CAA8C,GAAG,IAAI,MAAM,EAAE;AAAA,EAChJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,kBAAkB,WAAW,CAAC;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,CAAC,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EACpH;AACF;AACA,SAAS,0BAA0B,IAAI,KAAK;AAC1C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,WAAW,WAAW,SAAS,gDAAgD,QAAQ;AACxF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,SAAS,SAAS,8CAA8C,QAAQ;AACzE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,KAAK,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,QAAQ,SAAS,6CAA6C,QAAQ;AACvE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,KAAK,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,eAAe,CAAC;AAClI,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,YAAY,OAAO,QAAQ;AAC3G,IAAG,YAAY,QAAQ,OAAO,OAAO,YAAY,MAAM,EAAE,MAAM,OAAO,EAAE,EAAE,oBAAoB,OAAO,WAAW,EAAE,yBAAyB,OAAO,aAAa,EAAE,mBAAmB,OAAO,OAAO,SAAS,SAAS;AACpN,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,OAAO;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,UAAU;AACvB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,WAAW;AAAA,EACtD;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,YAAY,IAAI,aAAa;AAAA,EAC7B,iBAAiB,IAAI,aAAa;AAAA,EAClC,YAAY,IAAI,aAAa;AAAA,EAC7B,WAAW,IAAI,aAAa;AAAA,EAC5B,cAAc,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,YAAY,IAAI,UAAU;AACxB,SAAK,KAAK;AACV,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,OAAO,eAAe;AAChC,SAAK,YAAY,eAAe,WAAW;AAAA,MACzC,eAAe;AAAA,MACf,MAAM,cAAc;AAAA,IACtB,CAAC;AACD,SAAK,UAAU,KAAK;AAAA,MAClB,eAAe;AAAA,MACf;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe,MAAM,SAAS,MAAM;AAC9C,WAAO,iBAAiB,cAAc,OAAO,YAAY,aAAa,cAAc,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAC5G;AAAA,EACA,UAAU,eAAe;AACvB,WAAO,cAAc,QAAQ,cAAc,MAAM,KAAK,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,cAAc,GAAG;AAAA,EACnH;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,GAAG,KAAK,UAAU,aAAa,CAAC;AAAA,EACzC;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,cAAc;AAAA,MACd,iCAAiC,KAAK,aAAa,aAAa;AAAA,MAChE,WAAW,KAAK,cAAc,aAAa;AAAA,MAC3C,cAAc,KAAK,eAAe,aAAa;AAAA,IACjD;AAAA,EACF;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,YAAY,eAAe,OAAO;AAAA,EAChD;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iCACF,KAAK,YAAY,eAAe,OAAO,IADrC;AAAA,MAEL,wBAAwB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,eAAe,eAAe;AAC5B,QAAI,SAAS,KAAK,YAAY,aAAa,IAAI,cAAc,MAAM,SAAS;AAC5E,QAAI;AACJ,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,sBAAc;AACd;AAAA,MACF,KAAK;AACH,sBAAc;AACd;AAAA,MACF,KAAK;AACH,sBAAc;AACd;AAAA,MACF,KAAK;AACH,sBAAc;AACd;AAAA,MACF;AACE,sBAAc;AACd;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO;AAAA,MACL,8CAA8C;AAAA,MAC9C,cAAc,KAAK,eAAe,aAAa;AAAA,OAC5C,KAAK,YAAY,eAAe,OAAO;AAAA,EAE9C;AAAA,EACA,iBAAiB,SAAS;AACxB,QAAI,KAAK,WAAW,CAAC,KAAK,MAAM;AAC9B,aAAO,KAAK,cAAc,OAAO;AAAA,IACnC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,eAAe,SAAS,MAAM;AAAA,EACxD;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,YAAY,WAAW,KAAK,UAAU,IAAI,KAAK,WAAW,QAAQ,cAAc,MAAM;AAAA,EAC/F;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,YAAY,eAAe,UAAU;AAAA,EACnD;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,kBAAkB,KAAK,UAAU,aAAa;AAAA,EAC5D;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,YAAY,WAAW,cAAc,KAAK;AAAA,EACnD;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,CAAC,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE;AAAA,EAChI;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE,SAAS;AAAA,EAChK;AAAA,EACA,iBAAiB,OAAO;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,eAAe,KAAK;AAAA,MACvB,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,GAAG;AAC5C,WAAO,KAAK,KAAK,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,EACrH;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,OAAO,CAAI,WAAa,4BAA4B,SAAS,SAAS,eAAe;AAAA,MACrF,eAAe;AAAA,MACf,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MAC/F,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,MAC9F,MAAM,CAAI,WAAa,4BAA4B,QAAQ,QAAQ,gBAAgB;AAAA,IACrF;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,WAAW,YAAY,WAAW,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,SAAS,QAAQ,WAAW,UAAU,GAAG,CAAC,QAAQ,gBAAgB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,gBAAgB,GAAG,WAAW,SAAS,GAAG,CAAC,QAAQ,aAAa,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,WAAW,SAAS,GAAG,CAAC,QAAQ,YAAY,YAAY,IAAI,GAAG,WAAW,WAAW,gBAAgB,GAAG,CAAC,GAAG,sBAAsB,GAAG,SAAS,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,oBAAoB,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,WAAW,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,SAAS,GAAG,CAAC,SAAS,mBAAmB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,oBAAoB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,WAAW,IAAI,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,WAAW,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,MAAM,WAAW,SAAS,gBAAgB,UAAU,iBAAiB,SAAS,QAAQ,aAAa,kBAAkB,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,aAAa,kBAAkB,MAAM,WAAW,SAAS,gBAAgB,UAAU,iBAAiB,SAAS,MAAM,CAAC;AAAA,IACrlE,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2BAA2B,GAAG,IAAI,MAAM,CAAC;AAAA,MAC5D;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,iBAAiB,IAAI,OAAO,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAY,YAAe,kBAAqB,QAAW,SAAS,eAAe,gBAAgB,YAAW;AAAA,IAC5L,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuJV,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,QAAQ,CAAC;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,WAAN,MAAM,UAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AACd,SAAK,kBAAkB,KAAK,qBAAqB,KAAK,UAAU,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa,OAAO,IAAI;AAAA,EACxB,kBAAkB,OAAO;AAAA,IACvB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AAAA,EACD,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,eAAe;AACjB,UAAM,gBAAgB,YAAY,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,WAAW,IAAI;AACtF,WAAO,gBAAgB,cAAc,MAAM,OAAO,CAAC,OAAO,QAAQ;AAChE,UAAI,QAAQ,aAAW;AACrB,gBAAQ,MAAM,QAAQ,OAAK;AACzB,gBAAM,KAAK,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,IAAI,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB,QAAQ;AACzD,WAAK,kBAAkB,KAAK,qBAAqB,KAAK,SAAS,CAAC,CAAC;AAAA,IACnE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,cAAc,KAAK,gBAAgB;AACzC,WAAO,aAAa,QAAQ,YAAY,MAAM,KAAK,YAAY,KAAK,KAAK,YAAY,WAAW,YAAY,GAAG,IAAI,GAAG,KAAK,EAAE,IAAI,YAAY,GAAG,KAAK;AAAA,EACvJ;AAAA,EACA,YAAY,UAAU,YAAY,IAAI,UAAU,QAAQ,IAAI;AAC1D,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,KAAK;AACV,WAAO,MAAM;AACX,YAAM,aAAa,KAAK,WAAW;AACnC,UAAI,YAAY,WAAW,UAAU,GAAG;AACtC,aAAK,yBAAyB;AAC9B,aAAK,mBAAmB;AAAA,MAC1B,OAAO;AACL,aAAK,2BAA2B;AAChC,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,KAAK,KAAK,MAAM,kBAAkB;AAAA,EACzC;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,cAAc,KAAK;AACxB;AAAA,QACF,KAAK;AACH,eAAK,mBAAmB,KAAK;AAC7B;AAAA,QACF,KAAK;AACH,eAAK,sBAAsB,KAAK;AAChC;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF;AACE,eAAK,eAAe,KAAK;AACzB;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,OAAO,QAAQ,GAAG,SAAS,CAAC,GAAG,YAAY,IAAI,aAAa;AAC/E,UAAM,iBAAiB,CAAC;AACxB,aAAS,MAAM,QAAQ,CAAC,MAAM,UAAU;AACtC,YAAM,OAAO,cAAc,KAAK,YAAY,MAAM,OAAO,gBAAgB,SAAY,cAAc,MAAM,MAAM;AAC/G,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,gBAAgB,SAAY,cAAc,OAAO,gBAAgB,SAAY,OAAO,cAAc;AAAA,MACjH;AACA,cAAQ,OAAO,IAAI,UAAU,KAAK,KAAK,SAAS,KAAK,MAAM,SAAS,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ,WAAW,KAAK,qBAAqB,QAAQ,QAAQ,GAAG,SAAS,KAAK,MAAM,CAAC,IAAI,KAAK,qBAAqB,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAC9O,qBAAe,KAAK,OAAO;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM,MAAM;AACtB,WAAO,OAAO,YAAY,aAAa,KAAK,IAAI,CAAC,IAAI;AAAA,EACvD;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,KAAK,qBAAqB,aAAa;AACvD,UAAM,OAAO,YAAY,QAAQ,cAAc,MAAM;AACrD,UAAM,WAAW,KAAK,WAAW,aAAa;AAC9C,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,WAAW,IAAI,IAAI;AACxB,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,CAAC;AACd,iBAAW,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,IACjE,OAAO;AACL,UAAI,SAAS;AACX,aAAK,aAAa,KAAK;AAAA,MACzB,OAAO;AACL,cAAM,oBAAoB,OAAO,gBAAgB,KAAK,WAAW;AACjE,aAAK,KAAK,aAAa;AACvB,aAAK,sBAAsB,eAAe,oBAAoB,kBAAkB,QAAQ,EAAE;AAC1F,mBAAW,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,CAAC,WAAW,cAAc,GAAG;AAC/B,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,aAAa,QAAQ,IAAI;AACvB,UAAM,KAAK,UAAU,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AACvD,UAAM,UAAU,WAAW,WAAW,KAAK,UAAU,GAAG,eAAe,UAAU,EAAE,IAAI;AACvF,QAAI,SAAS;AACX,cAAQ,kBAAkB,QAAQ,eAAe;AAAA,QAC/C,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,QAAQ,aAAa;AAAG;AACxC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,YAAY,WAAW,KAAK;AAC5C,QAAI,SAAS;AACX,WAAK,WAAW,IAAI,aAAa;AAAA,IACnC;AACA,SAAK,gBAAgB,IAAI;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,gBAAY,KAAK,QAAQ;AACzB,eAAW,WAAW,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAAA,EAC5E;AAAA,EACA,KAAK,OAAO,SAAS;AACnB,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,eAAW,WAAW,MAAM,KAAK,UAAU,kBAAkB,aAAa;AAC1E,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU;AACf,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,QAAQ,KAAK,0BAA0B;AAC7C,YAAM,gBAAgB,KAAK,gBAAgB,KAAK;AAChD,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA,KAAK,cAAc;AAAA,QACnB,WAAW,cAAc;AAAA,QACzB,MAAM,cAAc;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,gBAAgB,IAAI;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM,WAAW,MAAM;AACvC,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB,KAAK;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,YAAY,KAAK;AACtB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF;AACE,YAAI,CAAC,WAAW,YAAY,qBAAqB,MAAM,GAAG,GAAG;AAC3D,eAAK,YAAY,OAAO,MAAM,GAAG;AAAA,QACnC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,mBAAmB,IAAI;AAAA,EACzD;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EACrF;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,UAAU,mBAAiB,KAAK,oBAAoB,aAAa,CAAC;AAAA,EAC7F;AAAA,EACA,qBAAqB,eAAe;AAClC,WAAO,iBAAiB,YAAY,WAAW,cAAc,KAAK;AAAA,EACpE;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,YAAY,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,WAAW,EAAE,QAAQ,cAAc,MAAM;AAAA,EACnG;AAAA,EACA,oBAAoB,eAAe;AACjC,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,WAAW,aAAa;AAAA,EACzE;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,CAAC,CAAC,iBAAiB,CAAC,KAAK,eAAe,cAAc,IAAI,KAAK,CAAC,KAAK,gBAAgB,cAAc,IAAI;AAAA,EAChH;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,KAAK,YAAY,MAAM,UAAU;AAAA,EAC1C;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,YAAY,MAAM,WAAW;AAAA,EAC3C;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,uBAAuB,aAAa,EAAE,kBAAkB,EAAE,WAAW,KAAK,YAAY,kBAAkB,CAAC;AAAA,EAC1J;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,iBAAiB,YAAY,WAAW,cAAc,KAAK;AAAA,EACpE;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,eAAe,KAAK,eAAe,MAAM;AAC9C,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,kBAAY,KAAK,aAAa,MAAM,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAC9H,kBAAY,cAAc,KAAK,KAAK,aAAa,MAAM,GAAG,KAAK,gBAAgB,EAAE,KAAK,EAAE,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC,IAAI,YAAY,KAAK,gBAAgB,EAAE;AAAA,IAC7L,OAAO;AACL,kBAAY,KAAK,aAAa,UAAU,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAAA,IAC5F;AACA,QAAI,cAAc,IAAI;AACpB,gBAAU;AAAA,IACZ;AACA,QAAI,cAAc,MAAM,KAAK,gBAAgB,EAAE,UAAU,IAAI;AAC3D,kBAAY,KAAK,0BAA0B;AAAA,IAC7C;AACA,QAAI,cAAc,IAAI;AACpB,WAAK,sBAAsB,OAAO,SAAS;AAAA,IAC7C;AACA,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAAA,IACjC;AACA,SAAK,gBAAgB,WAAW,MAAM;AACpC,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB,GAAG,GAAG;AACN,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,eAAe;AACpC,WAAO,gBAAgB,KAAK,aAAa,cAAc,IAAI,IAAI;AAAA,EACjE;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,YAAY,MAAM,OAAO;AAAA,EACvC;AAAA,EACA,sBAAsB,OAAO,OAAO;AAClC,UAAM,gBAAgB,KAAK,gBAAgB,KAAK;AAChD,QAAI,YAAY,WAAW,aAAa,GAAG;AACzC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,gBAAgB,IAAI;AAAA,QACvB;AAAA,QACA,KAAK,MAAM,MAAM;AAAA,QACjB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,gBAAgB,cAAc;AACrC,UAAI,YAAY,WAAW,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,EAAE,QAAQ,KAAK,gBAAgB,EAAE,KAAK;AACrG,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,KAAK,WAAW;AACpB,aAAK,gBAAgB,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,cAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,YAAI,SAAS;AACX,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,eAAK,aAAa;AAAA,YAChB,eAAe;AAAA,YACf;AAAA,UACF,CAAC;AACD,eAAK,gBAAgB,IAAI;AAAA,YACvB,OAAO;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,SAAK,sBAAsB,OAAO,SAAS;AAC3C,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,UAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,QAAI,SAAS;AACX,UAAI,KAAK,gBAAgB,YAAY;AACnC,YAAI,YAAY,WAAW,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,EAAE,QAAQ,cAAc,KAAK;AAC5F,eAAK,gBAAgB,IAAI;AAAA,YACvB,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW,KAAK,WAAW,EAAE;AAAA,YAC7B,MAAM,cAAc;AAAA,UACtB,CAAC;AAAA,QACH,OAAO;AACL,gBAAMA,iBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,gBAAMC,WAAU,KAAK,sBAAsBD,cAAa;AACxD,cAAIC,UAAS;AACX,iBAAK,aAAa;AAAA,cAChB,eAAe;AAAA,cACf,eAAAD;AAAA,YACF,CAAC;AACD,iBAAK,gBAAgB,IAAI;AAAA,cACvB,OAAO;AAAA,cACP,KAAKA,eAAc;AAAA,cACnB,WAAWA,eAAc;AAAA,cACzB,MAAMA,eAAc;AAAA,YACtB,CAAC;AACD,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,0BAA0B;AAC9I,WAAK,sBAAsB,OAAO,SAAS;AAAA,IAC7C,OAAO;AACL,YAAM,cAAc,cAAc,cAAc;AAChD,YAAM,YAAY,KAAK,aAAa,UAAU,UAAQ,KAAK,gBAAgB,WAAW;AACtF,oBAAc,MAAM,KAAK,sBAAsB,OAAO,SAAS;AAAA,IACjE;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,UAAU,KAAK,gBAAgB,cAAc;AACrD,UAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,cAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,cAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,YAAI,CAAC,WAAW,YAAY,WAAW,KAAK,UAAU,GAAG;AACvD,cAAI,KAAK,gBAAgB,EAAE,UAAU,GAAG;AACtC,iBAAK,gBAAgB,IAAI;AAAA,cACvB,OAAO,KAAK,WAAW,EAAE;AAAA,cACzB,KAAK,KAAK,WAAW,EAAE;AAAA,cACvB,WAAW,KAAK,WAAW,EAAE;AAAA,cAC7B,MAAM,cAAc;AAAA,YACtB,CAAC;AACD,iBAAK,WAAW,IAAI,IAAI;AAAA,UAC1B,OAAO;AACL,iBAAK,sBAAsB,OAAO,KAAK,mBAAmB,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,YAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,WAAK,sBAAsB,OAAO,SAAS;AAC3C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,UAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,QAAI,SAAS;AACX,UAAI,KAAK,gBAAgB,cAAc;AACrC,cAAM,YAAY,KAAK,gBAAgB,EAAE,UAAU,KAAK,KAAK,kBAAkB,KAAK,gBAAgB,EAAE,KAAK,IAAI,KAAK,yBAAyB;AAC7I,aAAK,sBAAsB,OAAO,SAAS;AAAA,MAC7C;AAAA,IACF,OAAO;AACL,UAAI,KAAK,gBAAgB,cAAc,YAAY,WAAW,KAAK,WAAW,CAAC,GAAG;AAChF,YAAI,cAAc,gBAAgB,GAAG;AACnC,eAAK,gBAAgB,IAAI;AAAA,YACvB,OAAO,KAAK,WAAW,EAAE;AAAA,YACzB,KAAK,KAAK,WAAW,EAAE;AAAA,YACvB,WAAW,KAAK,WAAW,EAAE;AAAA,YAC7B,MAAM,cAAc;AAAA,UACtB,CAAC;AACD,eAAK,WAAW,IAAI,IAAI;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,cAAc,cAAc,cAAc;AAChD,YAAM,YAAY,KAAK,aAAa,UAAU,UAAQ,KAAK,gBAAgB,WAAW;AACtF,oBAAc,MAAM,KAAK,sBAAsB,OAAO,SAAS;AAAA,IACjE;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,UAAU,OAAO;AACf,SAAK,sBAAsB,OAAO,KAAK,mBAAmB,CAAC;AAC3D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,sBAAsB,OAAO,KAAK,kBAAkB,CAAC;AAC1D,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,YAAY,WAAW,KAAK,WAAW,CAAC,GAAG;AAC7C,WAAK,gBAAgB,IAAI;AAAA,QACvB,OAAO,KAAK,WAAW,EAAE;AAAA,QACzB,KAAK,KAAK,WAAW,EAAE;AAAA,QACvB,MAAM,KAAK,WAAW,EAAE;AAAA,MAC1B,CAAC;AACD,WAAK,WAAW,IAAI,IAAI;AAAA,IAC1B;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,KAAK;AACvE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,OAAC,WAAW,KAAK,aAAa;AAAA,QAC5B,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,gBAAgB,EAAE,UAAU,IAAI;AACvC,YAAM,UAAU,WAAW,WAAW,KAAK,UAAU,IAAI,eAAe,UAAU,GAAG,KAAK,aAAa,EAAE,IAAI;AAC7G,YAAM,gBAAgB,WAAW,WAAW,WAAW,SAAS,6BAA6B;AAC7F,sBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AACjE,YAAM,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,EAAE,KAAK;AACpE,YAAM,UAAU,KAAK,sBAAsB,aAAa;AACxD,OAAC,WAAW,KAAK,sBAAsB,OAAO,KAAK,0BAA0B,CAAC;AAAA,IAChF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,YAAY,WAAW,KAAK,YAAY,IAAI,KAAK,aAAa,KAAK,IAAI;AAAA,EAChF;AAAA,EACA,2BAA2B;AACzB,UAAM,gBAAgB,KAAK,sBAAsB;AACjD,WAAO,gBAAgB,IAAI,KAAK,kBAAkB,IAAI;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,WAAO,YAAY,cAAc,KAAK,cAAc,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EACtG;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,IAAI,YAAY,cAAc,KAAK,aAAa,MAAM,GAAG,KAAK,GAAG,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AACtJ,WAAO,mBAAmB,KAAK,mBAAmB;AAAA,EACpD;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,mBAAmB,QAAQ,KAAK,aAAa,SAAS,IAAI,KAAK,aAAa,MAAM,QAAQ,CAAC,EAAE,UAAU,mBAAiB,KAAK,YAAY,aAAa,CAAC,IAAI;AACjK,WAAO,mBAAmB,KAAK,mBAAmB,QAAQ,IAAI;AAAA,EAChE;AAAA,EACA,qBAAqB;AACnB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,SAAS,OAAO,KAAK,SAAS,aAAa,UAAU,WAAS;AACvF,eAAK,KAAK,OAAO,IAAI;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAK,uBAAuB,KAAK,SAAS,OAAO,KAAK,UAAU,SAAS,WAAS;AAChF,gBAAM,qBAAqB,KAAK,UAAU,GAAG,kBAAkB,MAAM,UAAU,CAAC,KAAK,UAAU,GAAG,cAAc,SAAS,MAAM,MAAM;AACrI,cAAI,oBAAoB;AACtB,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,2BAA2B;AAChC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,GAAG;AACzC,WAAO,KAAK,KAAK,WAAa,kBAAkB,QAAQ,GAAM,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,aAAa,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,EAC3P;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MAC/F,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,IAChG;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,SAAS,oBAAoB,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,aAAa,YAAY,eAAe,kBAAkB,gBAAgB,SAAS,UAAU,QAAQ,eAAe,aAAa,YAAY,YAAY,cAAc,SAAS,kBAAkB,eAAe,GAAG,CAAC,SAAS,kBAAkB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gBAAgB,CAAC;AAAA,IAChd,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,OAAO,CAAC;AACxD,QAAG,eAAe,GAAG,iBAAiB,GAAG,CAAC;AAC1C,QAAG,WAAW,aAAa,SAAS,qDAAqD,QAAQ;AAC/F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,aAAa,SAAS,qDAAqD,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,QAC/C,CAAC,EAAE,YAAY,SAAS,oDAAoD,QAAQ;AAClF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,WAAW,MAAM,CAAC;AAAA,QAC9C,CAAC,EAAE,eAAe,SAAS,uDAAuD,QAAQ;AACxF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,QAC7C,CAAC,EAAE,kBAAkB,SAAS,0DAA0D,QAAQ;AAC9F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,QACpD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACrJ,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,YAAY,CAAC;AAClC,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,IAAI,eAAe,cAAc,IAAI,eAAe,UAAU,CAAC,EAAE,WAAW,IAAI,KAAK;AAC1I,QAAG,YAAY,mBAAmB,MAAM,EAAE,gBAAgB,UAAU,EAAE,MAAM,IAAI,EAAE;AAClF,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,aAAa;AACvC,QAAG,UAAU;AACb,QAAG,WAAW,gBAAgB,IAAI,YAAY,EAAE,SAAS,IAAI,cAAc,EAAE,UAAU,IAAI,EAAE,EAAE,QAAQ,IAAI,EAAE,eAAe,IAAI,WAAW,EAAE,aAAa,IAAI,SAAS,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,CAAC,IAAI,WAAW,IAAI,WAAW,EAAE,EAAE,cAAc,IAAI,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,kBAAkB,IAAI,cAAc,EAAE,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,MAAS;AAChY,QAAG,YAAY,MAAM,IAAI,KAAK,OAAO;AACrC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,WAAW,EAAE,YAAY,SAAS;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,MAAS,kBAAqB,SAAS,WAAW;AAAA,IAChF,QAAQ,CAAC,s5CAAs5C;AAAA,IAC/5C,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2CV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,s5CAAs5C;AAAA,IACj6C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,GAAG;AAC/C,WAAO,KAAK,KAAK,iBAAgB;AAAA,EACnC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,UAAU,WAAW;AAAA,IACpC,SAAS,CAAC,cAAc,cAAc,cAAc,eAAe,cAAc,eAAe,cAAc;AAAA,IAC9G,SAAS,CAAC,UAAU,cAAc,eAAe,YAAY;AAAA,EAC/D,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,cAAc,eAAe,cAAc,eAAe,gBAAgB,cAAc,eAAe,YAAY;AAAA,EAC3J,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,cAAc,eAAe,cAAc,eAAe,cAAc;AAAA,MAC9G,SAAS,CAAC,UAAU,cAAc,eAAe,YAAY;AAAA,MAC7D,cAAc,CAAC,UAAU,WAAW;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["processedItem", "grouped"]}