<p-progressSpinner
  *ngIf="loading"
  strokeWidth="4"
  styleClass="spinner"
></p-progressSpinner>

<!-- Message d’erreur -->
<p-message
  *ngIf="error"
  severity="error"
  text="{{ error }}"
  styleClass="error-message"
>
</p-message>
<div class="grid">
  <div class="col-12 md:col-3">
    <div class="card">
      <div class="flex justify-content-between align-items-center p-3">
        <div>
          <i class="pi pi-percentage text-blue-500 text-6xl"></i>
        </div>
        <div class="text-right flex flex-column">
          <span class="text-4xl">{{ tauxTotal | number : "1.2-2" }} %</span>
          <span class="text-color-secondary mt-2">Autoconsomation AC</span>
        </div>
      </div>

      <div
        class="border-top-1 surface-border flex justify-content-between mt-3"
      >
        <div
          class="w-6 text-center p-3 flex flex-column border-right-1 surface-border"
        >
          <span class="font-medium"
            >Design: {{ tauxDesign | number : "1.2-2" }} %</span
          >
          <span [ngClass]="getEcartColor(getEcartPoints(tauxTotal, tauxDesign))" class="mb-2">
            {{ getEcartPoints(tauxTotal, tauxDesign) >= 0 ? '+' : '' }}{{ getEcartPoints(tauxTotal, tauxDesign) | number : "1.2-2" }} pts
          </span>
          <p-progressBar
            [styleClass]="getProgressBarColor(tauxTotal, tauxDesign)"
            [value]="(tauxTotal / tauxDesign) * 100"
            [showValue]="false"
          >
          </p-progressBar>
        </div>
        <div class="w-6 text-center p-3 flex flex-column">
          <span class="font-medium"
            >Objectif: {{ tauxObjectif | number : "1.2-2" }} %</span
          >
          <span [ngClass]="getEcartColor(getEcartPoints(tauxTotal, tauxObjectif))" class="mb-2">
            {{ getEcartPoints(tauxTotal, tauxObjectif) >= 0 ? '+' : '' }}{{ getEcartPoints(tauxTotal, tauxObjectif) | number : "1.2-2" }} pts
          </span>
          <p-progressBar
            [styleClass]="getProgressBarColor(tauxTotal, tauxObjectif)"
            [value]="(tauxTotal / tauxObjectif) * 100"
            [showValue]="false"
          >
          </p-progressBar>
        </div>
      </div>
    </div>
  </div>
  <div class="col-12 md:col-5">
    <div class="card">
      <p-chart
        type="bar"
        [data]="chartData"
        height="240px"
        [options]="chartOptions"
      ></p-chart>
    </div>
  </div>
  <div class="col-12 md:col-4">
    <div class="card">
      <p-chart
        type="pie"
        [data]="pieChartData"
        height="240px"
        [options]="pieChartOptions"
      ></p-chart>
    </div>
  </div>
  <div class="col-12 md:col-6">
    <p-accordion
      [multiple]="false"
      [activeIndex]="0"
      styleClass="losses-accordion"
    >
      <p-accordionTab
        [header]="
          'Autoconsomation Nette : ' +
          (totalNetValue | number : '1.0-0') +
          ' ' +
          bilan?.consommationEnergetiqueGlobale?.unit +
          ' (' +
          (totalNetPercent | number : '1.2-2') +
          '%)'
        "
      >
        <div class="loss-list">
          <div *ngFor="let poste of bilan?.postes; trackBy: trackByPoste" class="loss-item">
            <span class="loss-label">{{ poste.label }}:</span>
            <span class="loss-value">
              {{ poste.value | number : "1.0-0" }}
              {{ bilan?.consommationEnergetiqueGlobale?.unit }} ({{
                poste.percent
              }}
              %)
            </span>
            <p-badge
              [value]="poste.causeType"
              [severity]="poste.causeType === 'Interne' ? 'success' : 'info'"
            ></p-badge>
          </div>
        </div>
      </p-accordionTab>
    </p-accordion>
  </div>
  <div class="col-12 md:col-6">
    <p-accordion
      [multiple]="false"
      [activeIndex]="0"
      styleClass="losses-accordion"
    >
      <p-accordionTab
        [header]="
          'Gaz Torchés : ' +
          (totalLossValue | number : '1.0-0') +
          ' Cm³ (' +
          (totalLossPercent | number : '1.2-2') +
          '%)'
        "
      >
        <div class="loss-list">
          <div *ngFor="let loss of gazTorchLossItems; trackBy: trackByLoss" class="loss-item">
            <span class="loss-label">{{ loss.label }}:</span>
            <span class="loss-value">
              {{ loss.value | number : "1.0-0" }}
              Cm³ ({{ loss.percent | number : "1.2-2" }}%)
            </span>
            <p-badge
              [value]="loss.causeType"
              [severity]="loss.causeType === 'Interne' ? 'success' : 'info'"
            ></p-badge>
          </div>
        </div>
      </p-accordionTab>
    </p-accordion>
  </div>
</div>
