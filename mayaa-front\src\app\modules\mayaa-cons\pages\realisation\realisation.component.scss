.autoconsommation-container {
  padding: 1rem;
  background-color: #f8fafc;
  min-height: 100vh;

  .realisation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;

    .realisation-title {
      font-size: 2rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }

    .realisation-info {
      .last-update {
        color: #64748b;
        font-size: 0.875rem;
      }
    }
  }

  .loading-container,
  .error-container {
    margin-bottom: 2rem;
  }

  .realisation-content {
    .stats-card {
      height: 100%;
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .stats-content {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;

        .stats-icon {
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
        }

        .stats-info {
          flex: 1;

          .stats-value {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            color: #1e293b;
          }

          .stats-label {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
            font-weight: 500;
          }
        }
      }
    }

    .card-header {
      padding: 1rem;
      border-bottom: 1px solid #e2e8f0;

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.25rem 0;
      }

      .card-subtitle {
        color: #64748b;
        font-size: 0.875rem;
      }
    }

    .chart-container {
      padding: 1rem;
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .totals-summary {
      padding: 1rem;
      background-color: #f8fafc;
      border-radius: 8px;
      margin: 1rem;

      .total-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e2e8f0;

        &:last-child {
          border-bottom: none;
        }

        .total-label {
          font-weight: 500;
          color: #475569;
        }

        .total-value {
          font-weight: 600;
          font-size: 1rem;
        }
      }
    }

    .unite-info {
      strong {
        color: #1e293b;
        font-size: 0.875rem;
      }

      small {
        font-size: 0.75rem;
      }
    }

    .taux-realisation {
      min-width: 80px;

      span {
        font-weight: 600;
        font-size: 0.875rem;
      }
    }
  }
}

// Classes utilitaires pour les couleurs
.text-green-600 {
  color: #059669 !important;
}

.text-red-600 {
  color: #dc2626 !important;
}

.text-orange-500 {
  color: #f97316 !important;
}

.text-orange-600 {
  color: #ea580c !important;
}

.text-blue-600 {
  color: #2563eb !important;
}

.text-purple-600 {
  color: #9333ea !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

.bg-green-100 {
  background-color: #dcfce7 !important;
}

.bg-red-100 {
  background-color: #fee2e2 !important;
}

.bg-orange-100 {
  background-color: #ffedd5 !important;
}

.bg-blue-100 {
  background-color: #dbeafe !important;
}

// Responsive design
@media (max-width: 768px) {
  .realisation-container {
    padding: 0.5rem;

    .realisation-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .realisation-title {
        font-size: 1.5rem;
      }
    }

    .stats-card {
      .stats-content {
        padding: 0.75rem;

        .stats-icon {
          width: 2.5rem;
          height: 2.5rem;
          font-size: 1.25rem;
        }

        .stats-info {
          .stats-value {
            font-size: 1.5rem;
          }
        }
      }
    }

    .chart-container {
      min-height: 250px;
      padding: 0.5rem;
    }
  }
}

// Améliorations pour les petits écrans
@media (max-width: 576px) {
  .realisation-container {
    .realisation-header {
      .realisation-title {
        font-size: 1.25rem;
      }
    }

    .stats-card {
      .stats-content {
        padding: 0.5rem;

        .stats-info {
          .stats-value {
            font-size: 1.25rem;
          }
        }
      }
    }

    .chart-container {
      min-height: 200px;
      padding: 0.25rem;
    }
  }

  :host ::ng-deep {
    .p-datatable {
      font-size: 0.75rem;

      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 0.5rem;
      }
    }
  }
}
