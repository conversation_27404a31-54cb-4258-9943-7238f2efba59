import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import {
  AnalyseExcesUtilite,
  AnalyseExcesTrain,
  AnalyseExcesResponse
} from '../../../model/analyse-exces.model';

@Injectable({
  providedIn: 'root'
})
export class AnalyseExcesService {

  private baseUrl = '/api/mayaa-unite'; // À ajuster selon votre configuration

  constructor(private http: HttpClient) { }

  /**
   * Récupère les données d'analyse d'excès pour une unité et un mois donnés
   * @param unite Code de l'unité
   * @param mois Mois au format YYYY-MM
   * @returns Observable contenant les données d'analyse d'excès
   */
  getAnalyseExcesData(unite: string, mois: string): Observable<AnalyseExcesResponse> {
    // TODO: Remplacer par l'appel API réel quand le contrôleur sera prêt
    // const params = new HttpParams()
    //   .set('unite', unite)
    //   .set('mois', mois);
    // return this.http.get<AnalyseExcesResponse>(`${this.baseUrl}/analyse-exces`, { params });

    // Données mock en attendant l'API
    return this.getMockData().pipe(delay(500));
  }

  /**
   * Récupère les données d'utilités pour l'analyse d'excès
   * @param unite Code de l'unité
   * @param mois Mois au format YYYY-MM
   * @returns Observable contenant les données d'utilités
   */
  getUtilitesData(unite: string, mois: string): Observable<AnalyseExcesUtilite[]> {
    // TODO: Remplacer par l'appel API réel
    // const params = new HttpParams()
    //   .set('unite', unite)
    //   .set('mois', mois);
    // return this.http.get<AnalyseExcesUtilite[]>(`${this.baseUrl}/analyse-exces/utilites`, { params });

    return this.getMockUtilitesData().pipe(delay(300));
  }

  /**
   * Récupère les données de trains pour l'analyse d'excès
   * @param unite Code de l'unité
   * @param mois Mois au format YYYY-MM
   * @returns Observable contenant les données de trains
   */
  getTrainsData(unite: string, mois: string): Observable<AnalyseExcesTrain[]> {
    // TODO: Remplacer par l'appel API réel
    // const params = new HttpParams()
    //   .set('unite', unite)
    //   .set('mois', mois);
    // return this.http.get<AnalyseExcesTrain[]>(`${this.baseUrl}/analyse-exces/trains`, { params });

    return this.getMockTrainsData().pipe(delay(300));
  }

  // Méthodes pour les données mock (à supprimer quand l'API sera prête)
  private getMockData(): Observable<AnalyseExcesResponse> {
    const mockData: AnalyseExcesResponse = {
      utilites: [
        {
          codeAc: 'UT001',
          problemeSpecifique: 'Fuite vapeur ligne principale',
          intitule: 'Système vapeur utilités',
          ac: 150.5,
          classeCauses: 'Technique',
          causes: 'Défaillance joint',
          actions: 'Remplacement joint + inspection',
          classes: 'Critique',
          etat: 'En cours',
          numero: '001'
        },
        {
          codeAc: 'UT002',
          problemeSpecifique: 'Surconsommation compresseur',
          intitule: 'Air comprimé atelier',
          ac: 89.3,
          classeCauses: 'Opérationnelle',
          causes: 'Réglage incorrect',
          actions: 'Recalibrage système',
          classes: 'Majeur',
          etat: 'Terminé',
          numero: '002'
        },
        {
          codeAc: 'UT003',
          problemeSpecifique: 'Perte réseau eau refroidissement',
          intitule: 'Circuit refroidissement',
          ac: 234.7,
          classeCauses: 'Maintenance',
          causes: 'Usure équipement',
          actions: 'Maintenance préventive',
          classes: 'Mineur',
          etat: 'Planifié',
          numero: '003'
        }
      ],
      trains: [
        {
          codeAc: 'TR001',
          problemeSpecifique: 'Fuite gaz combustible',
          intitule: 'Turbine principale',
          ac: 245.8,
          classeCauses: 'Technique',
          causes: 'Corrosion tuyauterie',
          actions: 'Remplacement section',
          classes: 'Critique',
          etat: 'En cours',
          numero: '001',
          trainName: 'Train 100'
        },
        {
          codeAc: 'TR002',
          problemeSpecifique: 'Surconsommation pilote',
          intitule: 'Système allumage',
          ac: 178.2,
          classeCauses: 'Réglage',
          causes: 'Dérive paramètres',
          actions: 'Recalibrage',
          classes: 'Majeur',
          etat: 'Planifié',
          numero: '002',
          trainName: 'Train 100'
        },
        {
          codeAc: 'TR003',
          problemeSpecifique: 'Perte efficacité brûleur',
          intitule: 'Brûleur principal',
          ac: 312.4,
          classeCauses: 'Maintenance',
          causes: 'Encrassement',
          actions: 'Nettoyage complet',
          classes: 'Critique',
          etat: 'En cours',
          numero: '003',
          trainName: 'Train 200'
        },
        {
          codeAc: 'TR004',
          problemeSpecifique: 'Fuite vanne régulation',
          intitule: 'Système régulation gaz',
          ac: 156.9,
          classeCauses: 'Technique',
          causes: 'Usure siège vanne',
          actions: 'Remplacement vanne',
          classes: 'Majeur',
          etat: 'Terminé',
          numero: '004',
          trainName: 'Train 200'
        }
      ]
    };

    return of(mockData);
  }

  private getMockUtilitesData(): Observable<AnalyseExcesUtilite[]> {
    return this.getMockData().pipe(
      delay(200),
      // Extraire seulement les utilités
      map((data: AnalyseExcesResponse) => data.utilites)
    );
  }

  private getMockTrainsData(): Observable<AnalyseExcesTrain[]> {
    return this.getMockData().pipe(
      delay(200),
      // Extraire seulement les trains
      map((data: AnalyseExcesResponse) => data.trains)
    );
  }
}
