/** Point de données quantifié avec pourcentage et unité */
export interface DataPoint {
    /** valeur absolue (en m³×10³) */
    value: number;
    /** unité d’affichage (ex. "10³ m³") */
    unit: string;
    /** pourcentage relatif (%) par rapport au gaz naturel reçu */
    percent: number;
  }
  
  /** Spécifique à l’autoconsommation brute, inclut cibles et écarts */
  export interface AutoconsommationBrute extends DataPoint {
    /** valeur design (ex. 11 %) */
    designTarget: number;
    /** objectif interne (ex. 16 %) */
    internalTarget: number;
    /** écart vs design (ex. +6,31 points) */
    deviationFromDesign: number;
    /** écart vs mois précédent (ex. -5,81 points) */
    deviationFromLastMonth: number;
  }
  
  /** Élément de perte (gaz torchés, arrêts, etc.) */
  export interface LossItem {
    /** libellé (ex. "Gaz torchés", "Dégivrage", ...) */
    label: string;
    /** quantité perdue (10³ m³) */
    value: number;
    /** pourcentage correspondant (%) */
    percent: number;
    /** catégorisation simplifiée */
    category:
      | 'GazTorchés'
      | 'Arrêts'
      | 'Déclenchement'
      | 'Redémarrage'
      | 'Dégivrage'
      | 'CompresseurRegénération'
      | 'CompresseurFuelGaz'
      | 'PompesReflux'
      | 'ExcèsFuelGaz'
      | 'PertesRéfrigérants'
      | 'BoilOff'
      | 'DiversInterne'
      | 'Autres';
    /** cause interne ou externe */
    causeType: 'Interne' | 'Externe';
  }
  
  export interface PosteItem {
    /** libellé (ex. "Gaz torchés", "Dégivrage", ...) */
    label: string;
    /** quantité perdue (10³ m³) */
    value: number;
    /** pourcentage correspondant (%) */
    percent: number;
    /** catégorisation simplifiée */
    category:
      | 'Poste1'
      | 'Poste2'
      | 'Poste3';
    /** cause interne ou externe */
    causeType: 'Interne' | 'Externe';
  }
  /** Modèle complet du bilan global pour un complexe donné */
  export interface BilanGlobalComplexe {
    /** identifiant du complexe */
    complexId: string;
    /** période du rapport (YYYY-MM) */
    period: string;
  
    consommationEnergetiqueGlobale: DataPoint;
    autoconsommationBrute: AutoconsommationBrute;
    ceSonelgaz: DataPoint;
    ceKahrama: DataPoint;
  
    totalAcCauseInterne: DataPoint;
    totalAcCauseExterne: DataPoint;
    autoconsommationNette: DataPoint;
  
    postes: PosteItem[];
    /** liste des pertes détaillées */
    losses: LossItem[];
    
  }
  