package dz.sonatrach.weblqs.mayaaback.model;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Entity
@Table(name="TEST_DATA_COMPLEXE")
@NamedQuery(name="TestDataComplexe.findAll", query="SELECT a FROM TestDataComplexe a")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class TestDataComplexe {

	@Id
	@Column(name="ID")	
	private long Id;
    
    @Column(name = "PMOIS")    
    private LocalDate pmois;

    
    @Column(name = "UNITE")    
    private String unite;

    @Column(name = "TAUX_AUTO_CONS_MOIS_GLOBAL")
    @JsonView(View.basic.class)
    private BigDecimal tauxAutoConsMoisGlobal;

    @Column(name = "TAUX_AUTO_CONS_MOIS_GLOBAL_COMPLEXE")
    @JsonView(View.basic.class)
    private BigDecimal tauxAutoConsMoisGlobalComplexe;

    @Column(name = "GAZ_TORCHEE_MOIS")
    @JsonView(View.basic.class)
    private BigDecimal gazTorcheeMois;

    @Column(name = "GAZ_TORCHEE_MOIS_COMPLEXE")
    @JsonView(View.basic.class)
    private BigDecimal gazTorcheeMoisComplexe;

    // Getters & Setters
    public LocalDate getPmois() {
        return pmois;
    }
    public void setPmois(LocalDate pmois) {
        this.pmois = pmois;
    }
    public String getUnite() {
        return unite;
    }
    public void setUnite(String unite) {
        this.unite = unite;
    }
    public BigDecimal getTauxAutoConsMoisGlobal() {
        return tauxAutoConsMoisGlobal;
    }
    public void setTauxAutoConsMoisGlobal(BigDecimal tauxAutoConsMoisGlobal) {
        this.tauxAutoConsMoisGlobal = tauxAutoConsMoisGlobal;
    }
    public BigDecimal getTauxAutoConsMoisGlobalComplexe() {
        return tauxAutoConsMoisGlobalComplexe;
    }
    public void setTauxAutoConsMoisGlobalComplexe(BigDecimal tauxAutoConsMoisGlobalComplexe) {
        this.tauxAutoConsMoisGlobalComplexe = tauxAutoConsMoisGlobalComplexe;
    }
    public BigDecimal getGazTorcheeMois() {
        return gazTorcheeMois;
    }
    public void setGazTorcheeMois(BigDecimal gazTorcheeMois) {
        this.gazTorcheeMois = gazTorcheeMois;
    }
    public BigDecimal getGazTorcheeMoisComplexe() {
        return gazTorcheeMoisComplexe;
    }
    public void setGazTorcheeMoisComplexe(BigDecimal gazTorcheeMoisComplexe) {
        this.gazTorcheeMoisComplexe = gazTorcheeMoisComplexe;
    }
	public long getId() {
		return Id;
	}
	public void setId(long id) {
		Id = id;
	}
}
