package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.RepartitionArretsParCause;

/**
 * Repository pour l'accès aux données de la vue REPARTITION_ARRETS_PAR_CAUSE
 */
@Repository
public interface RepartitionArretsParCauseRepository extends JpaRepository<RepartitionArretsParCause, Long> {

    /**
     * Récupère la répartition des arrêts par cause pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition par cause
     */
    List<RepartitionArretsParCause> findByUniteAndMois(String unite, LocalDate mois);

    /**
     * Récupère la répartition des arrêts par cause pour un type spécifique (INTERNE/EXTERNE/CLASSE)
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @param typeCause Type de cause (INTERNE, EXTERNE ou CLASSE)
     * @return Liste des données de répartition pour le type de cause
     */
    List<RepartitionArretsParCause> findByUniteAndMoisAndTypeCause(String unite, LocalDate mois, String typeCause);

    /**
     * Récupère les causes internes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition pour les causes internes
     */
    @Query("SELECT r FROM RepartitionArretsParCause r WHERE r.unite = :unite AND r.mois = :mois AND r.typeCause = 'INTERNE' ORDER BY r.numeroCause")
    List<RepartitionArretsParCause> findCausesInternesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les causes externes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition pour les causes externes
     */
    @Query("SELECT r FROM RepartitionArretsParCause r WHERE r.unite = :unite AND r.mois = :mois AND r.typeCause = 'EXTERNE' ORDER BY r.numeroCause")
    List<RepartitionArretsParCause> findCausesExternesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les classes de causes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition pour les classes de causes
     */
    @Query("SELECT r FROM RepartitionArretsParCause r WHERE r.unite = :unite AND r.mois = :mois AND r.typeCause = 'CLASSE' ORDER BY r.numeroCause")
    List<RepartitionArretsParCause> findClassesCausesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les causes distinctes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des causes distinctes
     */
    @Query("SELECT DISTINCT r.cause FROM RepartitionArretsParCause r WHERE r.unite = :unite AND r.mois = :mois ORDER BY r.cause")
    List<String> findDistinctCausesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les classes de causes distinctes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des classes de causes distinctes
     */
    @Query("SELECT DISTINCT r.classeCause FROM RepartitionArretsParCause r WHERE r.unite = :unite AND r.mois = :mois AND r.classeCause IS NOT NULL ORDER BY r.classeCause")
    List<String> findDistinctClassesCausesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les données pour les graphiques en secteurs (avec pourcentages)
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @param typeCause Type de cause (INTERNE, EXTERNE ou CLASSE)
     * @return Liste des données avec pourcentages pour les graphiques
     */
    @Query("SELECT r FROM RepartitionArretsParCause r WHERE r.unite = :unite AND r.mois = :mois AND r.typeCause = :typeCause AND r.pourcentage > 0 ORDER BY r.pourcentage DESC")
    List<RepartitionArretsParCause> findDataForPieChart(@Param("unite") String unite, 
                                                        @Param("mois") LocalDate mois, 
                                                        @Param("typeCause") String typeCause);
}
