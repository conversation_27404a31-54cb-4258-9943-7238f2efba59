package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.RepartitionArretsParSiege;

/**
 * Repository pour l'accès aux données de la vue REPARTITION_ARRETS_PAR_SIEGE
 */
@Repository
public interface RepartitionArretsParSiegeRepository extends JpaRepository<RepartitionArretsParSiege, Long> {

    /**
     * Récupère la répartition des arrêts par siège pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition par siège
     */
    List<RepartitionArretsParSiege> findByUniteAndMois(String unite, LocalDate mois);

    /**
     * Récupère la répartition des arrêts par siège pour un type spécifique (INTERNE/EXTERNE)
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @param typeSiege Type de siège (INTERNE ou EXTERNE)
     * @return Liste des données de répartition pour le type de siège
     */
    List<RepartitionArretsParSiege> findByUniteAndMoisAndTypeSiege(String unite, LocalDate mois, String typeSiege);

    /**
     * Récupère les sièges internes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition pour les sièges internes
     */
    @Query("SELECT r FROM RepartitionArretsParSiege r WHERE r.unite = :unite AND r.mois = :mois AND r.typeSiege = 'INTERNE' ORDER BY r.numeroSiege")
    List<RepartitionArretsParSiege> findSiegesInternesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les sièges externes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de répartition pour les sièges externes
     */
    @Query("SELECT r FROM RepartitionArretsParSiege r WHERE r.unite = :unite AND r.mois = :mois AND r.typeSiege = 'EXTERNE' ORDER BY r.numeroSiege")
    List<RepartitionArretsParSiege> findSiegesExternesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les sièges distincts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des sièges de causes distincts
     */
    @Query("SELECT DISTINCT r.siegeCause FROM RepartitionArretsParSiege r WHERE r.unite = :unite AND r.mois = :mois ORDER BY r.siegeCause")
    List<String> findDistinctSiegesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les données pour les graphiques en secteurs (avec pourcentages)
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @param typeSiege Type de siège (INTERNE ou EXTERNE)
     * @return Liste des données avec pourcentages pour les graphiques
     */
    @Query("SELECT r FROM RepartitionArretsParSiege r WHERE r.unite = :unite AND r.mois = :mois AND r.typeSiege = :typeSiege AND r.pourcentage > 0 ORDER BY r.pourcentage DESC")
    List<RepartitionArretsParSiege> findDataForPieChart(@Param("unite") String unite, 
                                                        @Param("mois") LocalDate mois, 
                                                        @Param("typeSiege") String typeSiege);
}
