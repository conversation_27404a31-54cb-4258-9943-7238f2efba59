package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Entité pour la situation des trains (arrêts)
 * Représente les données du tableau "Situation des Trains (arrêts)" avec les causes et analyses
 */
@Entity
@Table(name = "SITUATION_TRAINS_ARRETS")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class SituationTrainsArrets implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE", length = 10)
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "MOIS")
    @JsonView(View.basic.class)
    private LocalDate mois;

    @Column(name = "TYPE_CAUSE", length = 50)
    @JsonView(View.basic.class)
    private String typeCause; // "INTERNES" ou "EXTERNES"

    @Column(name = "CAUSES_PRINCIPALES", length = 500)
    @JsonView(View.basic.class)
    private String causesPrincipales;

    @Column(name = "ANALYSES", length = 1000)
    @JsonView(View.basic.class)
    private String analyses;

    @Column(name = "NOMBRE_ARRETS")
    @JsonView(View.basic.class)
    private Integer nombreArrets;

    // Données pour l'analyse du complexe
    @Column(name = "AUTOCONSOMMATION_POURCENTAGE")
    @JsonView(View.basic.class)
    private BigDecimal autoconsommationPourcentage;

    @Column(name = "AUTOCONSOMMATION_NETTE_POURCENTAGE")
    @JsonView(View.basic.class)
    private BigDecimal autoconsommationNettePourcentage;

    @Column(name = "GAZ_TORCHE_POURCENTAGE")
    @JsonView(View.basic.class)
    private BigDecimal gazTorchePourcentage;

    @Column(name = "SIEGES_CAUSES_GAZ_TORCHE", length = 500)
    @JsonView(View.basic.class)
    private String siegesCausesGazTorche;

    @Column(name = "CAUSES_RECURRENTES", length = 500)
    @JsonView(View.basic.class)
    private String causesRecurrentes;

    // Constructeurs
    public SituationTrainsArrets() {}

    public SituationTrainsArrets(String unite, LocalDate mois, String typeCause) {
        this.unite = unite;
        this.mois = mois;
        this.typeCause = typeCause;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public String getTypeCause() {
        return typeCause;
    }

    public void setTypeCause(String typeCause) {
        this.typeCause = typeCause;
    }

    public String getCausesPrincipales() {
        return causesPrincipales;
    }

    public void setCausesPrincipales(String causesPrincipales) {
        this.causesPrincipales = causesPrincipales;
    }

    public String getAnalyses() {
        return analyses;
    }

    public void setAnalyses(String analyses) {
        this.analyses = analyses;
    }

    public Integer getNombreArrets() {
        return nombreArrets;
    }

    public void setNombreArrets(Integer nombreArrets) {
        this.nombreArrets = nombreArrets;
    }

    public BigDecimal getAutoconsommationPourcentage() {
        return autoconsommationPourcentage;
    }

    public void setAutoconsommationPourcentage(BigDecimal autoconsommationPourcentage) {
        this.autoconsommationPourcentage = autoconsommationPourcentage;
    }

    public BigDecimal getAutoconsommationNettePourcentage() {
        return autoconsommationNettePourcentage;
    }

    public void setAutoconsommationNettePourcentage(BigDecimal autoconsommationNettePourcentage) {
        this.autoconsommationNettePourcentage = autoconsommationNettePourcentage;
    }

    public BigDecimal getGazTorchePourcentage() {
        return gazTorchePourcentage;
    }

    public void setGazTorchePourcentage(BigDecimal gazTorchePourcentage) {
        this.gazTorchePourcentage = gazTorchePourcentage;
    }

    public String getSiegesCausesGazTorche() {
        return siegesCausesGazTorche;
    }

    public void setSiegesCausesGazTorche(String siegesCausesGazTorche) {
        this.siegesCausesGazTorche = siegesCausesGazTorche;
    }

    public String getCausesRecurrentes() {
        return causesRecurrentes;
    }

    public void setCausesRecurrentes(String causesRecurrentes) {
        this.causesRecurrentes = causesRecurrentes;
    }
}
