import { Directive, ElementRef, HostListener, Renderer2 } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[zUppercase]',
})
export class UppercaseDirective {
  constructor(private el: ElementRef, private control: NgControl) {}

  @HostListener('input') onInput() {
    const value = this.el.nativeElement.value.toUpperCase();
    //this.this.renderer.setProperty(this.el.nativeElement, 'value', value);
     this.control.control!.setValue(value);
  }
}
