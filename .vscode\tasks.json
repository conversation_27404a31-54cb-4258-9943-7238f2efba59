{"version": "2.0.0", "tasks": [{"label": "Start Angular Dev Server", "type": "npm", "script": "start", "options": {"cwd": "${workspaceFolder}/mayaa-front"}, "isBackground": true, "problemMatcher": {"owner": "typescript", "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "bundle generation complete|Compiled successfully"}}}, "group": {"kind": "build", "isDefault": false}}, {"label": "Start Angular Test Server", "type": "npm", "script": "test", "options": {"cwd": "${workspaceFolder}/mayaa-front"}, "isBackground": true, "problemMatcher": {"owner": "typescript", "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "bundle generation complete|Compiled successfully"}}}}, {"label": "Terminate Angular Dev Server", "command": "echo ${input:terminate}", "type": "shell", "problemMatcher": []}, {"label": "Start Chrome with Debug", "type": "shell", "command": "start", "args": ["chrome", "--remote-debugging-port=9222", "--user-data-dir=${workspaceFolder}/.chrome-debug", "http://localhost:4200"], "windows": {"command": "cmd", "args": ["/c", "start", "chrome", "--remote-debugging-port=9222", "--user-data-dir=${workspaceFolder}/.chrome-debug", "http://localhost:4200"]}, "group": "build", "problemMatcher": []}, {"label": "Build Backend", "type": "shell", "command": "./mvnw", "args": ["clean", "compile"], "options": {"cwd": "${workspaceFolder}/mayaa-back"}, "group": {"kind": "build", "isDefault": false}, "problemMatcher": "$javac"}, {"label": "Build Frontend", "type": "npm", "script": "build", "options": {"cwd": "${workspaceFolder}/mayaa-front"}, "group": {"kind": "build", "isDefault": false}, "problemMatcher": "$tsc"}, {"label": "Build Full Stack", "dependsOrder": "parallel", "dependsOn": ["Build Backend", "Build Frontend"], "group": {"kind": "build", "isDefault": true}}], "inputs": [{"id": "terminate", "type": "command", "command": "workbench.action.tasks.terminate", "args": "terminateAll"}]}