import { Injectable } from '@angular/core';

/**
 * Service pour optimiser le chargement des assets et images
 */
@Injectable({
  providedIn: 'root'
})
export class AssetOptimizationService {

  constructor() {}

  /**
   * Précharge les images critiques
   */
  preloadCriticalImages(): void {
    const criticalImages = [
      'assets/layout/images/logo-dark.svg',
      'assets/layout/images/logo-white.svg'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }

  /**
   * Charge une image de manière lazy avec fallback
   */
  loadImageLazy(src: string, fallbackSrc?: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => resolve(src);
      img.onerror = () => {
        if (fallbackSrc) {
          const fallbackImg = new Image();
          fallbackImg.onload = () => resolve(fallbackSrc);
          fallbackImg.onerror = () => reject(new Error(`Failed to load image: ${src} and fallback: ${fallbackSrc}`));
          fallbackImg.src = fallbackSrc;
        } else {
          reject(new Error(`Failed to load image: ${src}`));
        }
      };
      
      img.src = src;
    });
  }

  /**
   * Optimise le chargement des polices
   */
  optimizeFonts(): void {
    // Précharger les polices critiques
    const fontPreloads = [
      'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
    ];

    fontPreloads.forEach(href => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      link.onload = () => {
        link.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    });
  }

  /**
   * Nettoie les ressources inutilisées
   */
  cleanupUnusedResources(): void {
    // Supprimer les event listeners inutilisés
    this.removeUnusedEventListeners();
    
    // Nettoyer les timers et intervals
    this.cleanupTimers();
  }

  private removeUnusedEventListeners(): void {
    // Identifier et supprimer les event listeners orphelins
    const elements = document.querySelectorAll('[data-cleanup-listeners]');
    elements.forEach(element => {
      const clonedElement = element.cloneNode(true);
      element.parentNode?.replaceChild(clonedElement, element);
    });
  }

  private cleanupTimers(): void {
    // Cette méthode peut être étendue pour nettoyer des timers spécifiques
    // En général, les composants Angular gèrent déjà cela avec OnDestroy
  }

  /**
   * Optimise les performances de rendu
   */
  optimizeRendering(): void {
    // Utiliser requestIdleCallback pour les tâches non critiques
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.performNonCriticalTasks();
      });
    } else {
      // Fallback pour les navigateurs qui ne supportent pas requestIdleCallback
      setTimeout(() => {
        this.performNonCriticalTasks();
      }, 100);
    }
  }

  private performNonCriticalTasks(): void {
    // Tâches non critiques comme le préchargement d'assets secondaires
    this.preloadSecondaryAssets();
  }

  private preloadSecondaryAssets(): void {
    const secondaryAssets = [
      'assets/demo/images/avatar/amyelsner.png',
      'assets/demo/images/avatar/asiyajavayant.png'
    ];

    secondaryAssets.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = src;
      document.head.appendChild(link);
    });
  }
}
