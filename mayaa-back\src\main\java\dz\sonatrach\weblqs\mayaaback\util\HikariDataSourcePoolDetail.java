package dz.sonatrach.weblqs.mayaaback.util;

import org.springframework.beans.DirectFieldAccessor;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.pool.HikariPool;

public class HikariDataSourcePoolDetail {

    private final HikariDataSource dataSource;

    public HikariDataSourcePoolDetail(HikariDataSource dataSource) {
        this.dataSource = dataSource;
    }

    public HikariPool getHikariPool() {
        return (HikariPool) new DirectFieldAccessor(dataSource).getPropertyValue("pool");
    }

    public int getActive() {
        try {
            return getHikariPool().getActiveConnections();
        } catch (Exception ex) {
            return -1;
        }
    }

    public int getMax() {
        return dataSource.getMaximumPoolSize();
    }
}
