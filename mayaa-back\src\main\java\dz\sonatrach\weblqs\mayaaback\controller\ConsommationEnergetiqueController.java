package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.ConsommationEnergetique;
import dz.sonatrach.weblqs.mayaaback.repo.ConsommationEnergetiqueRepository;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur pour les données de consommation énergétique
 */
@RestController
@RequestMapping("api/")
public class ConsommationEnergetiqueController {

    @Autowired
    private ConsommationEnergetiqueRepository consommationEnergetiqueRepository;

    
    /**
     * 
     * Récupère les données de consommation énergétique pour une unité et une période données
     * @param pmois Période au format ddMMyyyy (ex: "01122024")
     * @param unite Code de l'unité (ex: "5X2")
     * @return ResponseEntity contenant les données de consommation énergétique ou 204 si aucune donnée trouvée
     */
    @GetMapping("/consommation-energetique/{pmois}/{unite}")
    @JsonView(View.basic.class)
    public ResponseEntity<ConsommationEnergetique> getConsommationEnergetique(
            @PathVariable String pmois, 
            @PathVariable String unite) {
        try {
            System.out.println("=== REQUETE CONSOMMATION ENERGETIQUE ===");
            System.out.println("Période demandée: " + pmois + ", Unité: " + unite);
            
            // Convertir pmois (ddMMyyyy) vers LocalDate
            LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            Optional<ConsommationEnergetique> result = consommationEnergetiqueRepository
                    .findByUniteAndMois(unite, date);
            
            if (result.isPresent()) {
                System.out.println("Données de consommation énergétique trouvées pour " + unite + " / " + pmois);
                return ResponseEntity.ok(result.get());
            } else {
                System.out.println("Aucune donnée de consommation énergétique trouvée pour " + unite + " / " + pmois);
                return ResponseEntity.noContent().build();
            }
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des données de consommation énergétique: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère toutes les données de consommation énergétique pour un mois donné
     * @param pmois Période au format ddMMyyyy (ex: "01122024")
     * @return ResponseEntity contenant la liste des données de consommation énergétique
     */
    @GetMapping("/consommation-energetique/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<ConsommationEnergetique>> getConsommationEnergetiqueParMois(
            @PathVariable String pmois) {
        try {
            System.out.println("=== REQUETE CONSOMMATION ENERGETIQUE PAR MOIS ===");
            System.out.println("Période demandée: " + pmois);
            
            // Convertir pmois (ddMMyyyy) vers LocalDate
            LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            List<ConsommationEnergetique> result = consommationEnergetiqueRepository.findByMois(date);
            
            if (!result.isEmpty()) {
                System.out.println("Données de consommation énergétique trouvées pour " + result.size() + " unités");
                return ResponseEntity.ok(result);
            } else {
                System.out.println("Aucune donnée de consommation énergétique trouvée pour " + pmois);
                return ResponseEntity.noContent().build();
            }
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des données de consommation énergétique par mois: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Récupère l'historique des données de consommation énergétique pour une unité donnée
     * @param unite Code de l'unité (ex: "5X2")
     * @return ResponseEntity contenant la liste des données de consommation énergétique triées par mois décroissant
     */
    @GetMapping("/consommation-energetique/historique/{unite}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<ConsommationEnergetique>> getHistoriqueConsommationEnergetique(
            @PathVariable String unite) {
        try {
            System.out.println("=== REQUETE HISTORIQUE CONSOMMATION ENERGETIQUE ===");
            System.out.println("Unité demandée: " + unite);
            
            List<ConsommationEnergetique> result = consommationEnergetiqueRepository
                    .findByUniteOrderByMoisDesc(unite);
            
            if (!result.isEmpty()) {
                System.out.println("Historique de consommation énergétique trouvé pour " + result.size() + " périodes");
                return ResponseEntity.ok(result);
            } else {
                System.out.println("Aucun historique de consommation énergétique trouvé pour " + unite);
                return ResponseEntity.noContent().build();
            }
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération de l'historique de consommation énergétique: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
}
