{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-message.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ExclamationTriangleIcon } from 'primeng/icons/exclamationtriangle';\nimport { InfoCircleIcon } from 'primeng/icons/infocircle';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\n\n/**\n * Message groups a collection of contents in tabs.\n * @group Components\n */\nfunction UIMessage_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_InfoCircleIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"InfoCircleIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_TimesCircleIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesCircleIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_ExclamationTriangleIcon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ExclamationTriangleIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_div_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.text, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UIMessage_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, UIMessage_div_5_span_1_Template, 1, 1, \"span\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.escape);\n  }\n}\nfunction UIMessage_ng_template_6_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.text);\n  }\n}\nfunction UIMessage_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UIMessage_ng_template_6_span_0_Template, 2, 1, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.escape);\n  }\n}\nclass UIMessage {\n  /**\n   * Severity level of the message.\n   * @group Props\n   */\n  severity;\n  /**\n   * Text content.\n   * @group Props\n   */\n  text;\n  /**\n   * Whether displaying messages would be escaped or not.\n   * @group Props\n   */\n  escape = true;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  get icon() {\n    if (this.severity) {\n      return this.severity;\n    } else {\n      return 'info';\n    }\n  }\n  get containerClass() {\n    return {\n      [`p-inline-message-${this.severity}`]: this.severity,\n      'p-inline-message-icon-only': this.text == null\n    };\n  }\n  static ɵfac = function UIMessage_Factory(t) {\n    return new (t || UIMessage)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: UIMessage,\n    selectors: [[\"p-message\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      severity: \"severity\",\n      text: \"text\",\n      escape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"escape\", \"escape\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 8,\n    vars: 10,\n    consts: [[\"escapeOut\", \"\"], [\"aria-live\", \"polite\", 1, \"p-inline-message\", \"p-component\", \"p-inline-message\", 3, \"ngStyle\", \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"styleClass\"], [\"class\", \"p-inline-message-text\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"p-inline-message-text\", 3, \"innerHTML\"], [\"class\", \"p-inline-message-text\", 4, \"ngIf\"], [1, \"p-inline-message-text\"]],\n    template: function UIMessage_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵtemplate(1, UIMessage_CheckIcon_1_Template, 1, 1, \"CheckIcon\", 2)(2, UIMessage_InfoCircleIcon_2_Template, 1, 1, \"InfoCircleIcon\", 2)(3, UIMessage_TimesCircleIcon_3_Template, 1, 1, \"TimesCircleIcon\", 2)(4, UIMessage_ExclamationTriangleIcon_4_Template, 1, 1, \"ExclamationTriangleIcon\", 2)(5, UIMessage_div_5_Template, 2, 1, \"div\", 3)(6, UIMessage_ng_template_6_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const escapeOut_r2 = i0.ɵɵreference(7);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.containerClass);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.icon === \"success\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.icon === \"info\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.icon === \"error\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.icon === \"warn\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.escape)(\"ngIfElse\", escapeOut_r2);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgStyle, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon],\n    styles: [\"@layer primeng{.p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UIMessage, [{\n    type: Component,\n    args: [{\n      selector: 'p-message',\n      template: `\n        <div aria-live=\"polite\" class=\"p-inline-message p-component p-inline-message\" [ngStyle]=\"style\" [class]=\"styleClass\" [ngClass]=\"containerClass\">\n            <CheckIcon *ngIf=\"icon === 'success'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <InfoCircleIcon *ngIf=\"icon === 'info'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <TimesCircleIcon *ngIf=\"icon === 'error'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <ExclamationTriangleIcon *ngIf=\"icon === 'warn'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <div *ngIf=\"!escape; else escapeOut\">\n                <span *ngIf=\"!escape\" class=\"p-inline-message-text\" [innerHTML]=\"text\"></span>\n            </div>\n            <ng-template #escapeOut>\n                <span *ngIf=\"escape\" class=\"p-inline-message-text\">{{ text }}</span>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}}\\n\"]\n    }]\n  }], null, {\n    severity: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\nclass MessageModule {\n  static ɵfac = function MessageModule_Factory(t) {\n    return new (t || MessageModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MessageModule,\n    declarations: [UIMessage],\n    imports: [CommonModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon],\n    exports: [UIMessage]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MessageModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon],\n      exports: [UIMessage],\n      declarations: [UIMessage]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MessageModule, UIMessage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,uBAAuB;AAAA,EACrD;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,CAAC;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,uBAAuB;AAAA,EACrD;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,CAAC;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,uBAAuB;AAAA,EACrD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,2BAA2B,CAAC;AAAA,EAC9C;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,uBAAuB;AAAA,EACrD;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,MAAS,cAAc;AAAA,EAC3D;AACF;AACA,SAAS,yBAAyB,IAAI,KAAK;AACzC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,iCAAiC,GAAG,GAAG,QAAQ,CAAC;AACjE,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,MAAM;AAAA,EACtC;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,IAAI;AAAA,EAClC;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC3E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,MAAM;AAAA,EACrC;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,IAAI,OAAO;AACT,QAAI,KAAK,UAAU;AACjB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,CAAC,oBAAoB,KAAK,QAAQ,EAAE,GAAG,KAAK;AAAA,MAC5C,8BAA8B,KAAK,QAAQ;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,GAAG;AAC1C,WAAO,KAAK,KAAK,YAAW;AAAA,EAC9B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ,CAAI,WAAa,4BAA4B,UAAU,UAAU,gBAAgB;AAAA,MACzF,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,aAAa,UAAU,GAAG,oBAAoB,eAAe,oBAAoB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,SAAS,yBAAyB,GAAG,aAAa,GAAG,MAAM,GAAG,CAAC,GAAG,yBAAyB,GAAG,WAAW,GAAG,CAAC,SAAS,yBAAyB,GAAG,MAAM,GAAG,CAAC,GAAG,uBAAuB,CAAC;AAAA,IAC1Y,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,qCAAqC,GAAG,GAAG,kBAAkB,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,mBAAmB,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,2BAA2B,CAAC,EAAE,GAAG,0BAA0B,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC7a,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,eAAkB,YAAY,CAAC;AACrC,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,KAAK,EAAE,WAAW,IAAI,cAAc;AACjE,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS,SAAS;AAC5C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS,MAAM;AACzC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS,OAAO;AAC1C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,SAAS,MAAM;AACzC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,MAAM,EAAE,YAAY,YAAY;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,MAAS,SAAS,WAAW,gBAAgB,iBAAiB,uBAAuB;AAAA,IACzH,QAAQ,CAAC,4OAA4O;AAAA,IACrP,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,4OAA4O;AAAA,IACvP,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAe;AAAA,EAClC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,SAAS;AAAA,IACxB,SAAS,CAAC,cAAc,WAAW,gBAAgB,iBAAiB,uBAAuB;AAAA,IAC3F,SAAS,CAAC,SAAS;AAAA,EACrB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,WAAW,gBAAgB,iBAAiB,uBAAuB;AAAA,EAC7F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,WAAW,gBAAgB,iBAAiB,uBAAuB;AAAA,MAC3F,SAAS,CAAC,SAAS;AAAA,MACnB,cAAc,CAAC,SAAS;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}