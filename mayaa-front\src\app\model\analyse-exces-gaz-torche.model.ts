/**
 * Interface pour les données d'analyse d'excès de gaz torché
 * Concerne uniquement les trains
 */
export interface AnalyseExcesGazTorche {
  codeAc: string;
  problemeSpecifique: string;
  intitule: string;
  ac: number; // 10³ CM³ GN
  classeCauses: string;
  causes: string;
  actions: string;
  classes: string;
  etat: string;
  numero: string;
  trainName: string;
}

/**
 * Interface pour la réponse de l'API d'analyse d'excès de gaz torché
 */
export interface AnalyseExcesGazTorcheResponse {
  trains: AnalyseExcesGazTorche[];
  totalAC: number;
  totalItems: number;
  trainsCount: number;
}

/**
 * Interface pour les paramètres de requête
 */
export interface AnalyseExcesGazTorcheParams {
  unite: string;
  mois: string; // Format YYYY-MM
}

/**
 * Énumération pour les états possibles
 */
export enum EtatGazTorche {
  EN_COURS = 'En cours',
  TERMINE = 'Terminé',
  PLANIFIE = 'Planifié',
  ANNULE = 'Annulé'
}

/**
 * Énumération pour les classes de criticité
 */
export enum ClasseCriticiteGazTorche {
  CRITIQUE = 'Critique',
  MAJEUR = 'Majeur',
  MINEUR = 'Mineur'
}

/**
 * Interface pour les colonnes de tableau
 */
export interface TableColumn {
  field: string;
  header: string;
  sortable?: boolean;
  width?: string;
}

/**
 * Interface pour les statistiques par train
 */
export interface TrainStatistics {
  trainName: string;
  totalAC: number;
  itemCount: number;
  statusDistribution: { [key: string]: number };
  criticalityDistribution: { [key: string]: number };
}

/**
 * Interface pour les statistiques globales
 */
export interface GlobalStatistics {
  totalAC: number;
  totalItems: number;
  trainsCount: number;
  statusDistribution: { [key: string]: number };
  criticalityDistribution: { [key: string]: number };
  trainStatistics: TrainStatistics[];
}
