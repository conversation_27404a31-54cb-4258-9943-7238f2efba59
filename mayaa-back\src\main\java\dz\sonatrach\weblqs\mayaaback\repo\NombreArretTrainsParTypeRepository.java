package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.NombreArretTrainsParType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface NombreArretTrainsParTypeRepository extends JpaRepository<NombreArretTrainsParType, Long> {
    List<NombreArretTrainsParType> findByUniteAndPdate(String unite, LocalDate pDate);
}
