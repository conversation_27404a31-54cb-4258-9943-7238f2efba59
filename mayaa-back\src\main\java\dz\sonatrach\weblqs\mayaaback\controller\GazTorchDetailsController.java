package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.GazTorchDetails;
import dz.sonatrach.weblqs.mayaaback.service.GazTorchDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("api/")
public class GazTorchDetailsController {

    @Autowired
    private GazTorchDetailsService gazTorchDetailsService;

    @GetMapping("/gaz-torche/{pmois}/{unite}")
    public ResponseEntity<List<GazTorchDetails>> getGazTorcheDetails(@PathVariable String pmois, @PathVariable String unite) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<GazTorchDetails> gazTorchDetails = gazTorchDetailsService.getListCauseGazTorche(date, unite);
        if (gazTorchDetails.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(gazTorchDetails);
    }
}
