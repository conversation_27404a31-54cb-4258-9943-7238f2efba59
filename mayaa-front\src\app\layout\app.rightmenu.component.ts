import { LayoutService } from './service/app.layout.service';
import { Component, Input, OnChanges, SimpleChanges, ViewChild, ViewContainerRef, ComponentRef, Type } from '@angular/core';


@Component({
  selector: 'app-rightmenu',
  templateUrl: './app.rightmenu.component.html',
})
export class AppRightMenuComponent implements OnChanges {
  @Input() component!: Type<any>;
  @ViewChild('dynamicComponentContainer', {
    read: ViewContainerRef,
    static: true,
  })
  container!: ViewContainerRef;
  private componentRef!: ComponentRef<any>;

  constructor(public layoutService: LayoutService) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['component'] && this.component) {
      this.loadComponent();
    }
  }

  loadComponent() {
    this.container.clear();
    this.componentRef = this.container.createComponent(this.component);
  }

  get rightMenuActive(): boolean {
    return this.layoutService.state.rightMenuActive;
  }

  set rightMenuActive(_val: boolean) {
    this.layoutService.state.rightMenuActive = _val;
  }
}
