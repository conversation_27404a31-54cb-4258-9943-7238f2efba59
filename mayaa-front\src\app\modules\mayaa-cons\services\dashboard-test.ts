// Script de test pour le service dashboard
// À exécuter dans la console du navigateur pour déboguer

import { DashboardConsolideService } from './dashboard-consolide.service';
import { HttpClient } from '@angular/common/http';

// Test du service dashboard
export function testDashboardService() {
  console.log('=== TEST DASHBOARD SERVICE ===');
  
  // Simuler un HttpClient (pour test en console)
  const mockHttp = {
    get: (url: string) => {
      console.log('Mock HTTP GET:', url);
      return new Promise((resolve, reject) => {
        // Simuler une réponse vide ou erreur selon l'URL
        if (url.includes('list-unites')) {
          resolve([]);
        } else if (url.includes('auto-cons-mens')) {
          reject(new Error('Endpoint not found'));
        } else if (url.includes('gaz-torchee-par-cause-torchage')) {
          reject(new Error('Endpoint not found'));
        } else {
          reject(new Error('Unknown endpoint'));
        }
      });
    }
  };

  const service = new DashboardConsolideService(mockHttp as any);
  
  // Test avec une période
  const pmois = '01122024';
  console.log('Test avec période:', pmois);
  
  service.getDashboardConsolide(pmois).subscribe({
    next: (data) => {
      console.log('✅ Données reçues:', data);
      console.log('Nombre d\'unités dans repartitionGnRecu:', data.repartitionGnRecu.length);
      console.log('Nombre d\'unités dans productionGnl:', data.productionGnl.length);
      console.log('Nombre d\'unités dans autoconsommation:', data.autoconsommation.length);
      console.log('Évolution mensuelle:', data.evolutionMensuelle.length, 'mois');
    },
    error: (err) => {
      console.error('❌ Erreur:', err);
    }
  });
}

// Test des données mockées
export function testMockData() {
  console.log('=== TEST DONNÉES MOCKÉES ===');
  
  const service = new DashboardConsolideService(null as any);
  
  // Accéder aux méthodes privées pour test
  const serviceAny = service as any;
  
  console.log('Unités mockées:', serviceAny.getMockUnites());
  console.log('AutoCons mockées:', serviceAny.getMockAutoConsData());
  console.log('GazTorches mockées:', serviceAny.getMockGazTorchesData());
  console.log('Évolution mockée:', serviceAny.getMockEvolutionMensuelle());
  console.log('Réalisation mockée:', serviceAny.getMockRealisationData('01122024'));
}

// Fonction pour tester depuis la console du navigateur
(window as any).testDashboard = testDashboardService;
(window as any).testMockData = testMockData;

console.log('Tests disponibles:');
console.log('- testDashboard() : Test complet du service');
console.log('- testMockData() : Test des données mockées');
