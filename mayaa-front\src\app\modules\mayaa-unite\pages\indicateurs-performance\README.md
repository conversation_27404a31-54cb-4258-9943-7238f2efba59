# Composant Indicateurs de Performance

Ce composant affiche un tableau moderne des indicateurs de performance du complexe avec les données design et réelles, ainsi qu'un graphique interactif du Bilan GN-GNL.

## Utilisation

```html
<app-indicateurs-performance 
  [unite]="'5X2'" 
  [pmois]="'01122024'">
</app-indicateurs-performance>
```

## Propriétés d'entrée

| Propriété | Type   | Description                                    | Exemple      |
|-----------|--------|------------------------------------------------|--------------|
| `unite`   | string | Code de l'unité                               | "5X2"        |
| `pmois`   | string | Période au format ddMMyyyy                    | "01122024"   |

## Fonctionnalités

### Tableau des Indicateurs de Performance
- ✅ Affichage des données design (statiques)
- ✅ Récupération des données réelles via API
- ✅ Design moderne et responsive
- ✅ États de chargement et d'erreur
- ✅ Bouton de rafraîchissement
- ✅ Formatage automatique des nombres et pourcentages

### Graphique Bilan GN-GNL
- ✅ **Graphiques côte à côte** : Affichage simultané des deux graphiques
  - **Graphique principal** : GN Reçu Total (bleu/orange)
  - **Sous-graphique** : Utilisation Interne (nuances d'orange)
- ✅ **Couleurs cohérentes** : Nuances d'orange pour l'utilisation interne
  - Gaz Torchés : Orange clair (#fb923c)
  - Autoconsommation Nette : Orange foncé (#f97316)
- ✅ **Tableau avec sous-lignes** : Structure hiérarchique
  - Lignes principales : GN Transformé, Utilisation Interne
  - Sous-lignes : └─ Gaz Torchés, └─ Autoconsommation Nette
- ✅ **Design moderne** : Grille responsive 2 colonnes

### Tableau d'Utilisation des Chaudières
- ✅ **Données par type** : ABB et IHI avec badges colorés
- ✅ **Capacités installées** : Affichage en T/H
- ✅ **Nombre de chaudières utilisées** : Comparaison avec le design
- ✅ **Arrêts détaillés** : Volontaires vs Déclenchements
- ✅ **Production vapeur** : Valeurs précises avec 3 décimales
- ✅ **Consommation fuel** : En équivalent GN (CM³)
- ✅ **Ligne de totaux** : Calculs automatiques des sommes

## Structure du tableau

Le tableau affiche deux lignes :
1. **Design** : Données de référence (statiques)
2. **Réelle** : Données récupérées via l'API

### Colonnes affichées

1. **Nombre de trains en service** : `trainMarche` de l'API
2. **Indicateurs de Performance (%)** :
   - TP : Taux de Production
   - TF : Taux de Fonctionnement  
   - TC : Taux de Charge
   - TAP : Taux d'Arrêt Programmé
   - TAI : Taux d'Arrêt Imprévu
3. **Consommations** :
   - Production GNL M³ : `production` de l'API
   - Cons ED L/m3 GNL : À calculer/configurer
   - Taux d'appoint ED/Vap : À calculer/configurer
   - Cons vapeur T/m3 GNL : À calculer/configurer
   - Cons Elect MWH/m3 GNL : À calculer/configurer

## APIs utilisées

Le composant utilise plusieurs endpoints :

### Pour les indicateurs de performance
```
GET /api/indicateur-performance/{pmois}/{unite}
```

### Pour le bilan GN-GNL
```
GET /api/auto-cons-mens/01{mois}{annee}/{unite}
GET /api/gaz-torchee-par-cause-torchage/{unite}/{pmois}
```

### Pour l'utilisation des chaudières
```
GET /api/utilisation-chaudieres/{unite}/{pmois}
```

## Personnalisation

### Données design
Les données design sont configurées dans le composant (`designData`). Vous pouvez les modifier selon vos besoins.

### Calculs des consommations
Actuellement, certaines colonnes de consommation sont à 0. Vous devez implémenter les calculs ou récupérer ces données d'autres sources.

### Styles
Le composant utilise des classes CSS personnalisées que vous pouvez modifier dans le fichier SCSS.

## Dépendances

- PrimeNG Table
- PrimeNG ProgressSpinner  
- PrimeNG Button
- Service MayaaUniteService
