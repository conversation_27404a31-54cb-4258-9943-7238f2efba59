# Évolution des Causes - Page Unifiée

## Description

Cette page unifiée remplace les anciennes pages séparées pour les sièges et classes de causes. Elle offre une interface ergonomique avec un switch button pour basculer entre les modes "Sièges" et "Classes", et inclut l'option "Complexe" pour afficher les données de l'ensemble de l'unité.

## Fonctionnalités Principales

### ✅ Interface Unifiée
- **Switch Button** : Bascule entre "Sièges de Causes" et "Classes de Causes"
- **Dropdown Train** : Inclut "Complexe" (sélectionné par défaut) + trains individuels
- **Tableau d'évolution** : Affichage mensuel (J, F, M, A, M, J, J, A, S, O, N, D, Année)
- **Statistiques** : Cartes avec nombre de trains, causes, total gaz torché, mois le plus actif

### ✅ Modes d'Affichage
| Mode | Source Données | Colonne Principale | Icône |
|------|---------------|-------------------|-------|
| **Sièges** | `siege_cause` | "Sièges de Causes" | `pi-sitemap` |
| **Classes** | `classe_cause` | "Classes de Causes" | `pi-tags` |

### ✅ Options de Train
| Option | Description | Icône | Comportement |
|--------|-------------|-------|--------------|
| **Complexe** | Ensemble de l'unité | `pi-building` | Agrège tous les trains |
| **Train Tx** | Train individuel | `pi-cog` | Données spécifiques au train |

## Architecture

### Backend (Spring Boot)
```
EVOLUTION_CAUSES_TRAIN (Vue SQL unifiée)
├── ID, UNITE, CODE_TRAIN, MOIS
├── SIEGE_CAUSE (VARCHAR2 120)
├── CLASSE_CAUSE (VARCHAR2 90)  
└── QUANTITE_GAZ_TORCHEE (NUMBER)
```

**Contrôleur** : `EvolutionCausesTrainController`
- `GET /{unite}/{annee}` - Toutes les données
- `GET /{unite}/{annee}/{train}` - Train spécifique ou "COMPLEXE"
- `GET /{unite}/{mois}/trains` - Liste des trains (avec "COMPLEXE")
- `GET /{unite}/{mois}/sieges` - Sièges disponibles
- `GET /{unite}/{mois}/classes` - Classes disponibles

### Frontend (Angular)
```
evolution-causes/
├── evolution-causes.component.ts      # Logique principale
├── evolution-causes.component.html    # Template avec switch button
├── evolution-causes.component.scss    # Styles modernes
├── evolution-causes.component.spec.ts # Tests unitaires
└── README.md                          # Documentation
```

**Service** : `EvolutionCausesTrainService`
**Modèles** : `evolution-causes-train.model.ts`

## Utilisation

### Route d'Accès
```
/unite/evolution-causes
```

### Contrôles Utilisateur
1. **Switch Button** : Sélectionner "Sièges de Causes" ou "Classes de Causes"
2. **Dropdown Train** : Choisir "Complexe" ou un train spécifique
3. **Calendrier Global** : Changer l'année via le service calendrier

### Comportement par Défaut
- **Mode** : Sièges de Causes
- **Train** : Complexe (ensemble de l'unité)
- **Année** : Année courante

## Données Affichées

### Mode "Complexe"
Les données sont **agrégées** pour tous les trains de l'unité :
- Somme des quantités par cause et par mois
- Total annuel global
- Statistiques consolidées

### Mode Train Spécifique
Les données sont **filtrées** pour le train sélectionné :
- Quantités spécifiques au train
- Causes propres au train
- Statistiques du train

### Transformation des Données
```typescript
// Exemple de transformation
siege_cause: "DESHYDRATATION" → Colonne "Sièges de Causes"
classe_cause: "Process" → Colonne "Classes de Causes"
quantite_gaz_torchee: 2129731 → Cellule "Janvier"
```

## Interface Utilisateur

### En-tête
- **Titre** : "Évolution des Causes - Gaz Torché par Train"
- **Switch Button** : Sièges ↔ Classes
- **Dropdown** : Sélection du train
- **Bouton Refresh** : Actualisation des données

### Informations Contextuelles
- Unité, Année, Train sélectionné, Mode actuel

### Statistiques Rapides
- Nombre de trains
- Nombre de causes/classes total
- Total gaz torché
- Mois le plus actif

### Tableau d'Évolution
- Colonnes mensuelles (J, F, M, A, M, J, J, A, S, O, N, D)
- Colonne "Année" avec total annuel
- Formatage des nombres à la française
- Tri par cause/classe et total annuel

### Légende
- Icônes explicatives pour Sièges, Classes, et Complexe

## Avantages de la Solution

### ✅ Ergonomie Améliorée
- **Une seule page** au lieu de deux
- **Switch intuitif** entre les modes
- **Option Complexe** pour vue d'ensemble
- **Interface cohérente** et moderne

### ✅ Performance Optimisée
- **Vue SQL unifiée** : `EVOLUTION_CAUSES_TRAIN`
- **Requêtes optimisées** avec agrégation côté base
- **Chargement unique** des données
- **Cache intelligent** des options

### ✅ Maintenance Simplifiée
- **Code unifié** : Un composant au lieu de trois
- **Logique centralisée** : Un service, un contrôleur
- **Tests consolidés** : Couverture complète
- **Documentation unique** : Plus facile à maintenir

## Migration

### Suppression des Anciens Composants
- ❌ `EvolutionSiegeCauseTrainComponent`
- ❌ `EvolutionClasseCauseTrainComponent`  
- ❌ `EvolutionCommonComponent`
- ❌ Anciens modèles et services

### Nouvelle Route
- ❌ `/unite/evolution-siege-cause-train`
- ❌ `/unite/evolution-classe-cause-train`
- ✅ `/unite/evolution-causes`

### Nouvelle Vue SQL
- ❌ `EVOLUTION_SIEGE_CAUSE_TRAIN`
- ❌ `EVOLUTION_CLASS_CAUSE_TRAIN`
- ✅ `EVOLUTION_CAUSES_TRAIN` (unifiée)

## Tests

### Couverture Complète
- ✅ **Création et initialisation** du composant
- ✅ **Switch entre modes** Sièges/Classes
- ✅ **Sélection de train** et mode Complexe
- ✅ **Chargement des données** avec agrégation
- ✅ **Transformation des données** selon le mode
- ✅ **Formatage et affichage** du tableau
- ✅ **Gestion des états** (loading, erreurs)
- ✅ **Intégration calendrier** global

### Tests du Service
- ✅ **Appels API** avec gestion d'erreurs
- ✅ **Transformation des données** brutes
- ✅ **Calculs statistiques** automatiques
- ✅ **Agrégation mode Complexe**

## Configuration

### Modules Mis à Jour
- ✅ `MayaaUniteModule` : Nouveau composant déclaré
- ✅ `MayaaUniteRoutingModule` : Route unifiée configurée
- ✅ Imports PrimeNG : SelectButton, Table, Dropdown, etc.

### Dépendances
```typescript
// PrimeNG requis
import { SelectButtonModule } from 'primeng/selectbutton';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';
import { MessageModule } from 'primeng/message';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
```

La solution est prête à être utilisée dès que votre vue `EVOLUTION_CAUSES_TRAIN` sera disponible en base de données !
