import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class UserActivityService {
  // Délai d'inactivité (30 minutes)
  private IDLE_TIMEOUT = 1800000;
  // Événements d'activité utilisateur surveillés
  private USER_ACTIVITY_EVENTS = ['click', 'keypress', 'scroll', 'touchstart'];
  private LAST_ACTIVITY_KEY = 'lastActivityTime';
  private lastActivityTime: number = Date.now();

  constructor() {
    this.setupActivityListeners();
    this.setupStorageListener();
  }

  // Configure les écouteurs d'événements pour mettre à jour le temps de la dernière activité
  private setupActivityListeners(): void {
    const updateActivity = () => this.updateLastActivity();
    this.USER_ACTIVITY_EVENTS.forEach(eventName => {
      window.addEventListener(eventName, updateActivity, { passive: true });
    });
  }

  // Configure l'écouteur d'événements de stockage pour synchroniser l'activité entre les onglets
  private setupStorageListener(): void {
    window.addEventListener('storage', (event) => {
      if (event.key === this.LAST_ACTIVITY_KEY) {
        this.updateLastActivityFromStorage();
      }
    });
  }

  // Met à jour le temps de la dernière activité à l'heure actuelle
  private updateLastActivity(): void {
    const currentTime = Date.now();
    localStorage.setItem(this.LAST_ACTIVITY_KEY, currentTime.toString());
    this.lastActivityTime = currentTime
  }

  // Met à jour le temps de la dernière activité à partir de localStorage
  private updateLastActivityFromStorage(): void {
    const lastActivity = localStorage.getItem(this.LAST_ACTIVITY_KEY);
    if (lastActivity) {
      this.lastActivityTime = parseInt(lastActivity, 10);
    }
  }

  // Vérifie si l'utilisateur est actif
  public isUserActive(): boolean {
    const idleTime = Date.now() - this.lastActivityTime;
    return idleTime < this.IDLE_TIMEOUT;
  }

  // Renvoie le temps écoulé depuis la dernière activité utilisateur
  public getIdleTime(): number {
    return Date.now() - this.lastActivityTime;
  }
}
