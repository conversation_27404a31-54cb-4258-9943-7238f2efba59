package dz.sonatrach.weblqs.mayaaback.service;

import java.time.LocalDate;

/**
 * Test simple pour le service de génération de rapport mensuel
 */
public class RapportMensuelServiceTest {

    public static void main(String[] args) {
        System.out.println("=== TEST RAPPORT MENSUEL SERVICE ===");

        try {
            // Test de base
            testGenererRapportMensuel();

            // Test avec différentes dates
            testGenererRapportMensuelAvecDifferentesDates();

            System.out.println("🎉 TOUS LES TESTS SONT RÉUSSIS !");

        } catch (Exception e) {
            System.err.println("❌ ERREUR DANS LES TESTS: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void testGenererRapportMensuel() throws Exception {
        System.out.println("\n--- Test génération rapport mensuel ---");

        // Arrange
        RapportMensuelService service = new RapportMensuelService();
        LocalDate moisTest = LocalDate.of(2024, 11, 1);

        // Act
        byte[] pdfBytes = service.genererRapportMensuel(moisTest);

        // Assert
        if (pdfBytes == null) {
            throw new AssertionError("Le PDF généré ne doit pas être null");
        }

        if (pdfBytes.length == 0) {
            throw new AssertionError("Le PDF doit contenir des données");
        }

        if (pdfBytes.length < 1000) {
            throw new AssertionError("Le PDF doit avoir une taille raisonnable");
        }

        // Vérification que c'est bien un PDF (commence par %PDF)
        String pdfHeader = new String(pdfBytes, 0, Math.min(4, pdfBytes.length));
        if (!"%PDF".equals(pdfHeader)) {
            throw new AssertionError("Le fichier doit commencer par %PDF, mais commence par: " + pdfHeader);
        }

        System.out.println("✅ Test réussi - PDF généré: " + pdfBytes.length + " bytes");
    }

    public static void testGenererRapportMensuelAvecDifferentesDates() throws Exception {
        System.out.println("\n--- Test avec différentes dates ---");

        // Test avec différentes dates
        RapportMensuelService service = new RapportMensuelService();

        LocalDate[] datesTest = {
            LocalDate.of(2024, 1, 1),   // Janvier
            LocalDate.of(2024, 6, 15),  // Juin
            LocalDate.of(2024, 12, 31)  // Décembre
        };

        for (LocalDate date : datesTest) {
            byte[] pdfBytes = service.genererRapportMensuel(date);

            if (pdfBytes == null) {
                throw new AssertionError("PDF null pour la date: " + date);
            }

            if (pdfBytes.length == 0) {
                throw new AssertionError("PDF vide pour la date: " + date);
            }

            System.out.println("✅ Test réussi pour " + date + " - PDF: " + pdfBytes.length + " bytes");
        }
    }
}
