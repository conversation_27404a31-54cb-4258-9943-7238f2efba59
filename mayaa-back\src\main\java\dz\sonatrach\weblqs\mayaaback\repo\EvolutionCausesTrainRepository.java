package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.EvolutionCausesTrain;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository pour l'accès aux données de la vue EVOLUTION_CAUSES_TRAIN
 */
@Repository
public interface EvolutionCausesTrainRepository extends JpaRepository<EvolutionCausesTrain, Long> {
    
    /**
     * Récupère les données d'évolution pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données d'évolution
     */
    List<EvolutionCausesTrain> findByUniteAndMois(String unite, LocalDate mois);
    
    /**
     * Récupère les données d'évolution pour une unité, un train et un mois donnés
     * @param unite Code de l'unité
     * @param codeTrain Code du train
     * @param mois Mois de référence
     * @return Liste des données d'évolution pour le train spécifique
     */
    List<EvolutionCausesTrain> findByUniteAndCodeTrainAndMois(String unite, String codeTrain, LocalDate mois);
    
    /**
     * Récupère les trains distincts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des codes de trains distincts
     */
    @Query("SELECT DISTINCT e.codeTrain FROM EvolutionCausesTrain e WHERE e.unite = :unite AND e.mois = :mois ORDER BY e.codeTrain")
    List<String> findDistinctTrainsByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);
    
    /**
     * Récupère les sièges de causes distincts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des sièges de causes distincts
     */
    @Query("SELECT DISTINCT e.siegeCause FROM EvolutionCausesTrain e WHERE e.unite = :unite AND e.mois = :mois AND e.siegeCause IS NOT NULL ORDER BY e.siegeCause")
    List<String> findDistinctSiegesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);
    
    /**
     * Récupère les classes de causes distinctes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des classes de causes distinctes
     */
    @Query("SELECT DISTINCT e.classeCause FROM EvolutionCausesTrain e WHERE e.unite = :unite AND e.mois = :mois AND e.classeCause IS NOT NULL ORDER BY e.classeCause")
    List<String> findDistinctClassesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);
    
    /**
     * Récupère les données pour une année complète (12 mois) pour une unité donnée
     * @param unite Code de l'unité
     * @param annee Année de référence
     * @return Liste des données d'évolution pour l'année
     */
    @Query("SELECT e FROM EvolutionCausesTrain e WHERE e.unite = :unite AND YEAR(e.mois) = :annee ORDER BY e.mois, e.codeTrain, e.siegeCause, e.classeCause")
    List<EvolutionCausesTrain> findByUniteAndAnnee(@Param("unite") String unite, @Param("annee") int annee);
    
    /**
     * Récupère les données pour une année complète et un train spécifique
     * @param unite Code de l'unité
     * @param codeTrain Code du train
     * @param annee Année de référence
     * @return Liste des données d'évolution pour l'année et le train
     */
    @Query("SELECT e FROM EvolutionCausesTrain e WHERE e.unite = :unite AND e.codeTrain = :codeTrain AND YEAR(e.mois) = :annee ORDER BY e.mois, e.siegeCause, e.classeCause")
    List<EvolutionCausesTrain> findByUniteAndCodeTrainAndAnnee(@Param("unite") String unite, @Param("codeTrain") String codeTrain, @Param("annee") int annee);
    
    /**
     * Récupère les données agrégées pour l'ensemble de l'unité (équivalent "Complexe")
     * @param unite Code de l'unité
     * @param annee Année de référence
     * @return Liste des données d'évolution agrégées par mois et cause
     */
    @Query("""
        SELECT new dz.sonatrach.weblqs.mayaaback.model.EvolutionCausesTrain(
            MIN(e.id), e.unite, 'COMPLEXE', e.mois, e.siegeCause, e.classeCause, SUM(e.quantiteGazTorchee)
        )
        FROM EvolutionCausesTrain e 
        WHERE e.unite = :unite AND YEAR(e.mois) = :annee 
        GROUP BY e.unite, e.mois, e.siegeCause, e.classeCause
        ORDER BY e.mois, e.siegeCause, e.classeCause
    """)
    List<EvolutionCausesTrain> findAggregatedByUniteAndAnnee(@Param("unite") String unite, @Param("annee") int annee);
}
