package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "UTILISATION_CHAUDIERES")
public class UtilisationChaudieres implements Serializable {
    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "UNITE", nullable = false, length = 3)
    private String unite;
    
    @Column(name = "TYPE_CHAUDIERES", length = 21)
    private String typeChaudieres;

    @Column(name = "MOIS")
    private LocalDate mois;

    @Column(name = "CAPACITE_INSTALLEE")
    private BigDecimal capaciteInstallee;

    @Column(name = "CHAUDIERES_UTILISE_DESIGN")
    private BigDecimal chaudieresUtiliseDesign;

    @Column(name = "PRODUCTION_VAPEUR")
    private BigDecimal productionVapeur;

    @Column(name = "NOMBRE_ARRET_VOLONTAIRE")
    private BigDecimal nombreArretVolontaire;

    @Column(name = "NOMBRE_ARRET_DECLENCHEMENT")
    private BigDecimal nombreArretDeclenchement;

    @Column(name = "CONSOMMATION_FUEL_GAZ")
    private BigDecimal consommationFuelGaz;

    // Getters and Setters
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getUnite() {
        return unite;
    }
    public void setUnite(String unite) {
        this.unite = unite;
    }
    public LocalDate getMois() {
        return mois;
    }
    public void setMois(LocalDate mois) {
        this.mois = mois;
    }
    public BigDecimal getCapaciteInstallee() {
        return capaciteInstallee;
    }
    public void setCapaciteInstallee(BigDecimal capaciteInstallee) {
        this.capaciteInstallee = capaciteInstallee;
    }
    public BigDecimal getChaudieresUtiliseDesign() {
        return chaudieresUtiliseDesign;
    }
    public void setChaudieresUtiliseDesign(BigDecimal chaudieresUtiliseDesign) {
        this.chaudieresUtiliseDesign = chaudieresUtiliseDesign;
    }
    public BigDecimal getProductionVapeur() {
        return productionVapeur;
    }
    public void setProductionVapeur(BigDecimal productionVapeur) {
        this.productionVapeur = productionVapeur;
    }
    public String getTypeChaudieres() {
        return typeChaudieres;
    }
    public void setTypeChaudieres(String typeChaudieres) {
        this.typeChaudieres = typeChaudieres;
    }
    public BigDecimal getNombreArretVolontaire() {
        return nombreArretVolontaire;
    }
    public void setNombreArretVolontaire(BigDecimal nombreArretVolontaire) {
        this.nombreArretVolontaire = nombreArretVolontaire;
    }
    public BigDecimal getNombreArretDeclenchement() {
        return nombreArretDeclenchement;
    }
    public void setNombreArretDeclenchement(BigDecimal nombreArretDeclenchement) {
        this.nombreArretDeclenchement = nombreArretDeclenchement;
    }
    public BigDecimal getConsommationFuelGaz() {
        return consommationFuelGaz;
    }
    public void setConsommationFuelGaz(BigDecimal consommationFuelGaz) {
        this.consommationFuelGaz = consommationFuelGaz;
    }
}
