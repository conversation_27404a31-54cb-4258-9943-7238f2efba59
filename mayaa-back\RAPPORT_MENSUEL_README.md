# Rapport Mensuel PDF - Documentation

## 🎯 Fonctionnalités Implémentées

### ✅ Backend (Spring Boot)

#### 1. **Service de Génération PDF**
- **Classe** : `RapportMensuelService`
- **Technologie** : iText Core 9.2
- **Fonctionnalités** :
  - Page de garde avec logo Sonatrach
  - Sommaire automatique
  - Introduction avec tableau récapitulatif
  - Sections simplifiées (prêtes pour l'extension)

#### 2. **Contrôleurs REST**
- **Classe** : `RapportMensuelController`
- **Endpoints** :
  - `GET /api/rapport-mensuel/{periode}` - Génération et téléchargement
  - `GET /api/rapport-mensuel/{periode}/preview` - Prévisualisation
  - `GET /api/rapport-mensuel/{periode}/status` - Vérification des données

- **Classe** : `TestRapportController` (pour tests)
- **Endpoints de test** :
  - `GET /api/test-rapport/test` - Test de génération
  - `GET /api/test-rapport/status` - Test de statut

#### 3. **Modèles de Données**
- **Classe** : `RapportMensuelDto`
- **Structure complète** pour toutes les sections du rapport

### ✅ Frontend (Angular)

#### 1. **Composant Principal**
- **Classe** : `RapportMensuelComponent`
- **Route** : `/unite/rapport-mensuel`
- **Fonctionnalités** :
  - Interface moderne avec PrimeNG
  - Vérification de disponibilité des données
  - Génération et téléchargement PDF
  - Prévisualisation dans nouvel onglet

#### 2. **Service Angular**
- **Classe** : `RapportMensuelService`
- **Méthodes** :
  - `verifierDisponibilite(periode)`
  - `genererRapport(periode)`
  - `previsualiserRapport(periode)`
  - Utilitaires de formatage de dates

#### 3. **Interface Utilisateur**
- Design moderne avec cartes et couleurs
- Indicateurs de statut visuels
- Boutons d'action avec états de chargement
- Messages toast pour feedback utilisateur

## 🚀 Tests et Utilisation

### 1. **Test Backend Simple**

```bash
# Démarrer le backend
cd mayaa-back
mvn spring-boot:run

# Tester le statut
curl http://localhost:8889/api/test-rapport/status

# Tester la génération PDF
curl -o test_rapport.pdf http://localhost:8889/api/test-rapport/test
```

### 2. **Test Frontend**

```bash
# Démarrer le frontend
cd mayaa-front
ng serve

# Accéder à l'interface
http://localhost:4200/unite/rapport-mensuel
```

### 3. **Test Complet**

1. **Démarrer les deux services**
2. **Naviguer vers** : `http://localhost:4200/unite/rapport-mensuel`
3. **Vérifier** que le statut des données s'affiche
4. **Cliquer** sur "Prévisualiser" ou "Télécharger PDF"

## 📋 Structure du Rapport PDF

### Page de Garde
- Logo Sonatrach
- Informations organisation
- Titre et période
- Tableau de validation

### Sommaire
- Liste des 10 sections principales
- 2 annexes

### Section 1 - Introduction
- Texte d'introduction avec données clés
- **Tableau récapitulatif complet** avec :
  - Consommation nette
  - Gaz torchés
  - Autoconsommation complexe
  - Répartition causes internes/externes
- Événements majeurs

### Sections 2-10
- Structures prêtes pour l'implémentation
- Titres et contenus de base

## 🔧 Configuration

### Dépendances Backend
```xml
<!-- iText Core 9.2 -->
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext-core</artifactId>
    <version>9.2.0</version>
    <type>pom</type>
</dependency>
```

### Modules Frontend
- `ToastModule` pour les messages
- `ButtonModule` pour les actions
- `CardModule` pour la mise en page

## 🎨 Menu Navigation

Le rapport mensuel est accessible via :
**Mayaa - Unité** → **Rapports** → **Rapport Mensuel**

## 📊 Données Intégrées

### Sources Existantes
- ✅ `AutoconsommationService` - Données d'autoconsommation
- ✅ `RealisationService` - Indicateurs de performance
- ✅ `EvolutionCausesTrainService` - Évolution des causes
- ✅ `GazTorcheeParCauseTorchageRepository` - Gaz torché

### À Intégrer
- 🔄 Actions entreprises et finalisées
- 🔄 Annexes détaillées
- 🔄 Graphiques et visualisations

## 🚧 Prochaines Étapes

1. **Intégration des données réelles** dans les sections 2-10
2. **Ajout des graphiques** avec génération d'images
3. **Implémentation des annexes** détaillées
4. **Tests avec données de production**
5. **Optimisation des performances**

## 🐛 Dépannage

### Erreur de compilation
```bash
mvn clean compile
```

### Erreur iText
Vérifier que iText Core 9.2 est bien installé dans le pom.xml

### Erreur Angular
```bash
npm install
ng build
```

## 📞 Support

Pour toute question ou problème :
1. Vérifier les logs backend dans la console
2. Vérifier les erreurs frontend dans la console navigateur
3. Tester les endpoints avec curl ou Postman
