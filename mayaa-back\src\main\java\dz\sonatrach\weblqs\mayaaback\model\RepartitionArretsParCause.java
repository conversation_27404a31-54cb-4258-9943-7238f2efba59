package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Entité pour la répartition des arrêts par cause (internes/externes) et par classe
 * Représente les données pour les graphiques en secteurs de répartition par cause
 */
@Entity
@Table(name = "REPARTITION_ARRETS_PAR_CAUSE")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class RepartitionArretsParCause implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE", length = 10)
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "MOIS")
    @JsonView(View.basic.class)
    private LocalDate mois;

    @Column(name = "CAUSE", length = 120)
    @JsonView(View.basic.class)
    private String cause;

    @Column(name = "TYPE_CAUSE", length = 20)
    @JsonView(View.basic.class)
    private String typeCause; // "INTERNE", "EXTERNE", ou "CLASSE"

    @Column(name = "QUANTITE_ARRETS")
    @JsonView(View.basic.class)
    private BigDecimal quantiteArrets;

    @Column(name = "POURCENTAGE")
    @JsonView(View.basic.class)
    private BigDecimal pourcentage;

    @Column(name = "NUMERO_CAUSE")
    @JsonView(View.basic.class)
    private Integer numeroCause; // 1, 2, 3, 4, 5, 6

    @Column(name = "CLASSE_CAUSE", length = 90)
    @JsonView(View.basic.class)
    private String classeCause; // Pour le troisième graphique (classes)

    // Constructeurs
    public RepartitionArretsParCause() {}

    public RepartitionArretsParCause(String unite, LocalDate mois, String cause, String typeCause) {
        this.unite = unite;
        this.mois = mois;
        this.cause = cause;
        this.typeCause = typeCause;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public String getTypeCause() {
        return typeCause;
    }

    public void setTypeCause(String typeCause) {
        this.typeCause = typeCause;
    }

    public BigDecimal getQuantiteArrets() {
        return quantiteArrets;
    }

    public void setQuantiteArrets(BigDecimal quantiteArrets) {
        this.quantiteArrets = quantiteArrets;
    }

    public BigDecimal getPourcentage() {
        return pourcentage;
    }

    public void setPourcentage(BigDecimal pourcentage) {
        this.pourcentage = pourcentage;
    }

    public Integer getNumeroCause() {
        return numeroCause;
    }

    public void setNumeroCause(Integer numeroCause) {
        this.numeroCause = numeroCause;
    }

    public String getClasseCause() {
        return classeCause;
    }

    public void setClasseCause(String classeCause) {
        this.classeCause = classeCause;
    }
}
