package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.NombreArretTrainsParClasseCause;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface NombreArretTrainsParClasseCauseRepository extends JpaRepository<NombreArretTrainsParClasseCause, Long> {
    List<NombreArretTrainsParClasseCause> findByUniteAndPdate(String unite, LocalDate pdate);
}
