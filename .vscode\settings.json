{"java.compile.nullAnalysis.mode": "automatic", "java.debug.settings.onBuildFailureProceed": true, "java.configuration.workspaceCacheLimit": 90, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "angular.experimental-ivy": true, "debug.allowBreakpointsEverywhere": true, "debug.javascript.usePreview": true, "debug.javascript.codelens.npmScripts": "all", "files.exclude": {"**/node_modules": true, "**/target": true, "**/.git": true, "**/.DS_Store": true, "**/.chrome-debug": true}, "search.exclude": {"**/node_modules": true, "**/target": true, "**/dist": true, "**/.chrome-debug": true}, "java.configuration.updateBuildConfiguration": "automatic"}