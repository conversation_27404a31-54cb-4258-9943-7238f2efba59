package dz.sonatrach.weblqs.mayaaback.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import dz.sonatrach.weblqs.mayaaback.model.ArretsConsolide;
import dz.sonatrach.weblqs.mayaaback.model.ArretsConsolide.RepartitionCauseConsolide;
import dz.sonatrach.weblqs.mayaaback.model.ArretsConsolide.RepartitionSiegeConsolide;
import dz.sonatrach.weblqs.mayaaback.model.ArretsConsolide.SituationTrainsConsolide;
import dz.sonatrach.weblqs.mayaaback.model.ArretsConsolide.StatistiquesGlobales;
import dz.sonatrach.weblqs.mayaaback.model.ArretsConsolide.SyntheseArretsConsolide;

/**
 * Contrôleur pour les données consolidées des arrêts.
 *
 * Endpoints principaux :
 *   GET /api/arrets-consolide/{mois}
 *   GET /api/arrets-consolide/{mois}/synthese-globale
 *   GET /api/arrets-consolide/{mois}/repartition-siege
 *   GET /api/arrets-consolide/{mois}/repartition-cause
 *   GET /api/arrets-consolide/{mois}/situation-trains
 *   GET /api/arrets-consolide/{mois}/statistiques
 *
 * - mois : période au format ddMMyyyy (ex: 01012024)
 *
 * Réponses :
 *   200 OK : données trouvées
 *   400 Bad Request : format de date invalide
 */
@RestController
@RequestMapping("api/arrets-consolide")
public class ArretsConsolideController {

    /**
     * Récupère toutes les données consolidées des arrêts pour un mois donné
     * @param mois Mois au format ddMMyyyy
     * @return Données consolidées complètes
     */
    @GetMapping("/{mois}")
    public ResponseEntity<ArretsConsolide> getArretsConsolide(@PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            ArretsConsolide consolide = createMockArretsConsolide(date);
            
            return ResponseEntity.ok(consolide);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la synthèse globale des arrêts pour toutes les unités
     * @param mois Mois au format ddMMyyyy
     * @return Liste de la synthèse par unité
     */
    @GetMapping("/{mois}/synthese-globale")
    public ResponseEntity<List<SyntheseArretsConsolide>> getSyntheseGlobale(@PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            List<SyntheseArretsConsolide> synthese = createMockSyntheseGlobale(date);
            
            return ResponseEntity.ok(synthese);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la répartition consolidée par siège
     * @param mois Mois au format ddMMyyyy
     * @return Liste de la répartition par siège consolidée
     */
    @GetMapping("/{mois}/repartition-siege")
    public ResponseEntity<List<RepartitionSiegeConsolide>> getRepartitionSiege(@PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            List<RepartitionSiegeConsolide> repartition = createMockRepartitionSiege(date);
            
            return ResponseEntity.ok(repartition);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la répartition consolidée par cause
     * @param mois Mois au format ddMMyyyy
     * @return Liste de la répartition par cause consolidée
     */
    @GetMapping("/{mois}/repartition-cause")
    public ResponseEntity<List<RepartitionCauseConsolide>> getRepartitionCause(@PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            List<RepartitionCauseConsolide> repartition = createMockRepartitionCause(date);
            
            return ResponseEntity.ok(repartition);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère la situation consolidée des trains
     * @param mois Mois au format ddMMyyyy
     * @return Liste de la situation des trains consolidée
     */
    @GetMapping("/{mois}/situation-trains")
    public ResponseEntity<List<SituationTrainsConsolide>> getSituationTrains(@PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            List<SituationTrainsConsolide> situation = createMockSituationTrains(date);
            
            return ResponseEntity.ok(situation);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère les statistiques globales consolidées
     * @param mois Mois au format ddMMyyyy
     * @return Statistiques globales
     */
    @GetMapping("/{mois}/statistiques")
    public ResponseEntity<StatistiquesGlobales> getStatistiques(@PathVariable String mois) {
        try {
            LocalDate date = LocalDate.parse(mois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            
            StatistiquesGlobales stats = createMockStatistiques(date);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Méthodes privées pour créer des données mockées
    private ArretsConsolide createMockArretsConsolide(LocalDate date) {
        ArretsConsolide consolide = new ArretsConsolide(date);
        consolide.setPeriode(date.format(DateTimeFormatter.ofPattern("MM/yyyy")));
        
        consolide.setSyntheseGlobale(createMockSyntheseGlobale(date));
        consolide.setRepartitionSiege(createMockRepartitionSiege(date));
        consolide.setRepartitionCause(createMockRepartitionCause(date));
        consolide.setSituationTrains(createMockSituationTrains(date));
        consolide.setStatistiques(createMockStatistiques(date));
        
        return consolide;
    }

    private List<SyntheseArretsConsolide> createMockSyntheseGlobale(LocalDate date) {
        List<SyntheseArretsConsolide> synthese = new ArrayList<>();
        
        // Données pour différentes unités
        List<String> unites = Arrays.asList("5X3", "6X2", "7X1", "8X4");
        
        for (int i = 0; i < unites.size(); i++) {
            SyntheseArretsConsolide unite = new SyntheseArretsConsolide();
            unite.setUnite(unites.get(i));
            unite.setTotalArrets(45 + (i * 8)); // 45, 53, 61, 69
            unite.setMapPourcentage(new BigDecimal("85.5").add(new BigDecimal(i * 2))); // 85.5, 87.5, 89.5, 91.5
            unite.setAutoconsommationNette(new BigDecimal("11.8").add(new BigDecimal(i * 0.5))); // 11.8, 12.3, 12.8, 13.3
            unite.setGazTorche(new BigDecimal("3.2").subtract(new BigDecimal(i * 0.3))); // 3.2, 2.9, 2.6, 2.3
            unite.setTrainsEnService(4 + (i % 2)); // 4, 5, 4, 5
            synthese.add(unite);
        }
        
        return synthese;
    }

    private List<RepartitionSiegeConsolide> createMockRepartitionSiege(LocalDate date) {
        List<RepartitionSiegeConsolide> repartition = new ArrayList<>();
        
        // Sièges internes consolidés
        String[] siegesInternes = {"Compresseur principal", "Turbine à gaz", "Système de contrôle", "Équipement auxiliaire"};
        Integer[] arretsInternes = {85, 67, 52, 38};
        
        for (int i = 0; i < siegesInternes.length; i++) {
            RepartitionSiegeConsolide siege = new RepartitionSiegeConsolide();
            siege.setSiegeCause(siegesInternes[i]);
            siege.setTypeSiege("INTERNE");
            siege.setTotalArrets(arretsInternes[i]);
            siege.setPourcentageGlobal(new BigDecimal(arretsInternes[i] * 100.0 / 242).setScale(1, RoundingMode.HALF_UP));
            repartition.add(siege);
        }
        
        // Sièges externes consolidés
        String[] siegesExternes = {"Réseau électrique", "Fournisseur gaz", "Conditions météorologiques"};
        Integer[] arretsExternes = {72, 48, 35};
        
        for (int i = 0; i < siegesExternes.length; i++) {
            RepartitionSiegeConsolide siege = new RepartitionSiegeConsolide();
            siege.setSiegeCause(siegesExternes[i]);
            siege.setTypeSiege("EXTERNE");
            siege.setTotalArrets(arretsExternes[i]);
            siege.setPourcentageGlobal(new BigDecimal(arretsExternes[i] * 100.0 / 242).setScale(1, RoundingMode.HALF_UP));
            repartition.add(siege);
        }
        
        return repartition;
    }

    private List<RepartitionCauseConsolide> createMockRepartitionCause(LocalDate date) {
        List<RepartitionCauseConsolide> repartition = new ArrayList<>();
        
        // Causes internes consolidées
        String[] causesInternes = {"Maintenance préventive", "Défaillance équipement", "Arrêt programmé", "Problème technique"};
        Integer[] arretsInternes = {95, 78, 45, 32};
        
        for (int i = 0; i < causesInternes.length; i++) {
            RepartitionCauseConsolide cause = new RepartitionCauseConsolide();
            cause.setCause(causesInternes[i]);
            cause.setTypeCause("INTERNE");
            cause.setTotalArrets(arretsInternes[i]);
            cause.setPourcentageGlobal(new BigDecimal(arretsInternes[i] * 100.0 / 250).setScale(1, RoundingMode.HALF_UP));
            repartition.add(cause);
        }
        
        return repartition;
    }

    private List<SituationTrainsConsolide> createMockSituationTrains(LocalDate date) {
        List<SituationTrainsConsolide> situation = new ArrayList<>();
        
        // Situation internes consolidée
        SituationTrainsConsolide internes = new SituationTrainsConsolide();
        internes.setTypeCause("INTERNES");
        internes.setTotalArrets(158);
        internes.setCausesPrincipales("Maintenance préventive, Défaillance compresseur, Problème turbine, Arrêt programmé");
        internes.setAnalyseGlobale("Consolidation des arrêts internes sur l'ensemble des unités. Performance globale satisfaisante avec une prédominance des maintenances préventives programmées.");
        situation.add(internes);
        
        // Situation externes consolidée
        SituationTrainsConsolide externes = new SituationTrainsConsolide();
        externes.setTypeCause("EXTERNES");
        externes.setTotalArrets(84);
        externes.setCausesPrincipales("Panne réseau électrique, Arrêt fournisseur gaz, Conditions météorologiques, Maintenance réseau");
        externes.setAnalyseGlobale("Consolidation des arrêts externes sur l'ensemble des unités. Impact significatif nécessitant des améliorations de redondance.");
        situation.add(externes);
        
        return situation;
    }

    private StatistiquesGlobales createMockStatistiques(LocalDate date) {
        StatistiquesGlobales stats = new StatistiquesGlobales();
        
        stats.setTotalArretsGlobal(242);
        stats.setNombreUnites(4);
        stats.setMoyenneMapPourcentage(new BigDecimal("88.5"));
        stats.setMoyenneAutoconsommation(new BigDecimal("12.6"));
        stats.setMoyenneGazTorche(new BigDecimal("2.8"));
        stats.setTotalTrainsEnService(18);
        
        return stats;
    }
}
