package dz.sonatrach.weblqs.mayaaback.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import org.springframework.web.multipart.MultipartFile;


public class FileCompressor {

	public static byte[] compress(MultipartFile fileToCompress) throws IOException {
        byte[] buffer = new byte[1024];
        byte[] compressedBytes;
        try (
            ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
            ZipOutputStream zipOutputStream = new ZipOutputStream(byteOutputStream);
        ) {
            ZipEntry zipEntry = new ZipEntry(fileToCompress.getOriginalFilename());
            zipOutputStream.putNextEntry(zipEntry);

            InputStream input = fileToCompress.getInputStream();
            int len;
            while ((len = input.read(buffer)) > 0) {
                zipOutputStream.write(buffer, 0, len);
            }

            zipOutputStream.closeEntry();

            zipOutputStream.finish();

            compressedBytes = byteOutputStream.toByteArray();

        }
        return compressedBytes; 
    }
	
	public static byte[] decompressToByteArray(byte[] compressedBytes) throws IOException {
        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedBytes);
             ZipInputStream zipInputStream = new ZipInputStream(byteArrayInputStream)) {

            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int bytesRead;

                while ((bytesRead = zipInputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                }

                byteArrayOutputStream.close();
                return byteArrayOutputStream.toByteArray(); // Return the decompressed bytes
            }
        }

        return null;
    }	
	
}
