import { NgModule } from '@angular/core';
import { ExtraOptions, RouterModule, Routes } from '@angular/router';
import { AppLayoutComponent } from './layout/app.layout.component';

const routerOptions: ExtraOptions = {
    anchorScrolling: 'enabled'
};

const routes: Routes = [
  {
    path: '',
    component: AppLayoutComponent,
    children: [
      {
        path: 'unite',
        data: { breadcrumb: 'Unite' },
        loadChildren: () =>
          import('./modules/mayaa-unite/mayaa-unite.module').then(
            (m) => m.MayaaUniteModule
          ),
      },
      {
        path: 'conso',
        data: { breadcrumb: 'Consolidé' },
        loadChildren: () =>
          import('./modules/mayaa-cons/mayaa-cons.module').then(
            (m) => m.MayaaConsModule
          ),
      },
    ],
  },
];

@NgModule({
    imports: [RouterModule.forRoot(routes, routerOptions)],
    exports: [RouterModule]
})
export class AppRoutingModule { }
