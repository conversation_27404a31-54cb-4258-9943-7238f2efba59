package dz.sonatrach.weblqs.mayaaback.exception;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;

@JsonView(View.basic.class)
public class ErrorEntity {

	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp;
	private HttpStatus status;
    private String message;
    private String debugMessage;
    private int silentMode;
    
    private ErrorEntity() {
        timestamp = LocalDateTime.now();
        this.silentMode = 0;
    }

    public ErrorEntity(HttpStatus status) {
        this();
        this.status = status;
    }

    public ErrorEntity(HttpStatus status, Throwable ex) {
        this();
        this.status = status;
        this.message = "Unexpected error";
        this.debugMessage = ex.getLocalizedMessage();
    }
    
    public ErrorEntity(HttpStatus status, String message) {
        this();
        this.status = status;
        this.message = message;
    }
    public ErrorEntity(HttpStatus status, String message,int silentMode) {
        this();
        this.status = status;
        this.message = message;
        this.silentMode = silentMode;
    }

    public ErrorEntity(HttpStatus status, String message, Throwable ex) {
        this();
        this.status = status;
        this.message = message;
        this.debugMessage = getStackTraceAsString(ex);
    }
    
    private String getStackTraceAsString(Throwable ex) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        ex.printStackTrace(pw);
        return sw.toString();
    }

	public LocalDateTime getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(LocalDateTime timestamp) {
		this.timestamp = timestamp;
	}

	public HttpStatus getStatus() {
		return status;
	}

	public void setStatus(HttpStatus status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getDebugMessage() {
		return debugMessage;
	}

	public void setDebugMessage(String debugMessage) {
		this.debugMessage = debugMessage;
	}

	public int getSilentMode() {
		return silentMode;
	}

	public void setSilentMode(int silentMode) {
		this.silentMode = silentMode;
	}

}
