package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Entity
@Table(name="GAZ_TORCHEE_PAR_TYPE_DYSFONCT")
@NamedQuery(name="GazTorchDetails.findAll", query="SELECT g FROM GazTorchDetails g")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class GazTorchDetails implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private long Id;

    @Column(name="UNITE")
    @JsonView(View.basic.class)
    private String unite;

    @Column(name="PMOIS")
    @JsonView(View.basic.class)
    private LocalDate pmois;

    @Column(name="CODE_TYPE_DYSFONCT")
    @JsonView(View.basic.class)
    private String codeTypeDysfonct;

     @Column(name="LIB_TYPE_DYSFONCT")
    @JsonView(View.basic.class)
    private String libTypeDysfonct;

    @Column(name="CODE_CAUSE_TORCHAGE")
    @JsonView(View.basic.class)
    private String codeCauseTorchage;

    @Column(name="QUANTITE_GAZ_TORCHEE")
    @JsonView(View.basic.class)
    private BigDecimal quantiteGazTorchee;

    // Getters and Setters
    
    public String getCodeCauseTorchage() {
        return codeCauseTorchage;
    }

    public void setCodeCauseTorchage(String codeCauseTorchage) {
        this.codeCauseTorchage = codeCauseTorchage;
    }

    public long getId() {
        return Id;
    }

    public void setId(long id) {
        this.Id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getPmois() {
        return pmois;
    }

    public void setPmois(LocalDate pmois) {
        this.pmois = pmois;
    }

    public String getCodeTypeDysfonct() {
        return codeTypeDysfonct;
    }

    public void setCodeTypeDysfonct(String codeTypeDysfonct) {
        this.codeTypeDysfonct = codeTypeDysfonct;
    }

    public String getLibTypeDysfonct() {
        return libTypeDysfonct;
    }

    public void setLibTypeDysfonct(String libTypeDysfonct) {
        this.libTypeDysfonct = libTypeDysfonct;
    }

    public BigDecimal getQuantiteGazTorchee() {
        return quantiteGazTorchee;
    }

    public void setQuantiteGazTorchee(BigDecimal quantiteGazTorchee) {
        this.quantiteGazTorchee = quantiteGazTorchee;
    }
}