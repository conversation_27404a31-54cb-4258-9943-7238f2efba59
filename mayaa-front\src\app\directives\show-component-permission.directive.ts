import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { UserPermissionService } from '../services/auth/user-permission.service';

@Directive({
  selector: '[showComponentPermission]',
})
export class ShowComponentPermissionDirective {
  constructor(private templateRef: TemplateRef<any>, private viewContainer: ViewContainerRef, public userPermissionService: UserPermissionService) {}

  @Input() set showComponentPermission(permission: string) {
    if (this.userPermissionService.hasPermissions([permission])) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
