export interface ConsommationPosteData {
  poste: string;
  cm3GN: number;
  pourcentageGN: number;
}

export interface GazTorchesCauseData {
  cause: string;
  cm3GN: number;
  pourcentageGN: number;
}

export interface ConsommationJournaliere {
  jour: number;
  poste: string;
  cm3GN: number;
  analyse?: string;
}

export interface GazTorchesJournalier {
  jour: number;
  cause: string;
  cm3GN: number;
  analyse?: string;
}

export interface DetailMensuel {
  mois: string;
  annee: string;
  postes?: ConsommationPosteData[];
  causes?: GazTorchesCauseData[];
  donneesJournalieres?: ConsommationJournaliere[] | GazTorchesJournalier[];
}