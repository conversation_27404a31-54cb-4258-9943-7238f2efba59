import { HttpErrorResponse } from '@angular/common/http';
import { <PERSON>rror<PERSON>andler, Injectable } from '@angular/core';
import { InterceptorService } from '../../layout/interceptor.service';
import { LoaderService } from '../../layout/service/loader.service';
@Injectable()
export class ErrorHandlerService implements ErrorHandler {
  constructor(private interceptorService: InterceptorService, private loaderService: LoaderService) {}
  handleError(error: any): void {
    console.error('Attention erreur !!!:', error);

    if (error instanceof HttpErrorResponse && error.error) {
      this.interceptorService.updateErrors({
        error: error.error,
        code: error.status,
        http: true,
        dialog: (error.error.silentMode === 0 && error.status === 400) || error.status === 500 ? true : undefined,
      });
    } else {
      let UnauthorizedApp = false;
      if (error.name == 'UnauthorizedAppError') {
        UnauthorizedApp = true;
      }
      this.interceptorService.updateErrors({
        error: error,
        application: true,
        UnauthorizedApp: UnauthorizedApp,
      });
    }
    this.loaderService.hide();
  }
}
