import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { ArretsConsolideService } from '../../services/arrets-consolide.service';
import { CalendarService } from '../../../../services/calendar.service';
import {
  SyntheseArretsConsolide,
  RepartitionSiegeConsolide,
  RepartitionCauseConsolide,
  SituationTrainsConsolide,
  StatistiquesGlobalesArrets,
  ReponseArretsConsolide
} from '../../models/arrets-consolide.interface';

@Component({
  selector: 'app-arrets-consolide',
  templateUrl: './arrets-consolide.component.html',
  styleUrls: ['./arrets-consolide.component.scss']
})
export class ArretsConsolideComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Données
  donneesConsolidees: ReponseArretsConsolide | null = null;
  syntheseGlobale: SyntheseArretsConsolide[] = [];
  repartitionSiege: RepartitionSiegeConsolide[] = [];
  repartitionCause: RepartitionCauseConsolide[] = [];
  situationTrains: SituationTrainsConsolide[] = [];
  statistiquesGlobales: StatistiquesGlobalesArrets | null = null;
  loading = false;

  // Configuration des tableaux
  colsSynthese = [
    { field: 'unite', header: 'Unité', width: '100px' },
    { field: 'totalArrets', header: 'Total Arrêts', width: '120px' },
    { field: 'totalHeuresArret', header: 'Heures Arrêt', width: '120px' },
    { field: 'mapMoyenne', header: 'MAP (%)', width: '100px' },
    { field: 'acNetteMoyenne', header: 'AC Nette (%)', width: '120px' },
    { field: 'gtTotalInterne', header: 'GT Interne', width: '120px' },
    { field: 'gtTotalExterne', header: 'GT Externe', width: '120px' },
    { field: 'causePrincipale', header: 'Cause Principale', width: '200px' }
  ];

  colsSituation = [
    { field: 'typeCause', header: 'Type', width: '120px' },
    { field: 'totalArrets', header: 'Total Arrêts', width: '120px' },
    { field: 'causesPrincipalesGlobales', header: 'Causes Principales', width: '300px' },
    { field: 'analysesConsolidees', header: 'Analyses', width: '400px' }
  ];

  // Données pour les graphiques
  pieChartDataSiege: any;
  pieChartDataCause: any;
  pieChartOptions: any;

  constructor(
    private arretsConsolideService: ArretsConsolideService,
    private calendarService: CalendarService
  ) {
    this.initPieChartOptions();
  }

  ngOnInit(): void {
    // Écouter les changements de calendrier
    this.calendarService.selectedDate$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(date => {
      if (date) {
        const periode = this.formatDateToPeriode(date);
        this.chargerDonnees(periode);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private chargerDonnees(periode: string): void {
    this.loading = true;

    // Charger toutes les données consolidées
    combineLatest([
      this.arretsConsolideService.getSyntheseConsolidee(periode),
      this.arretsConsolideService.getRepartitionSiegeConsolidee(periode),
      this.arretsConsolideService.getRepartitionCauseConsolidee(periode),
      this.arretsConsolideService.getSituationTrainsConsolidee(periode),
      this.arretsConsolideService.getStatistiquesGlobales(periode)
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: ([synthese, repartitionSiege, repartitionCause, situationTrains, statistiques]) => {
        this.syntheseGlobale = synthese;
        this.repartitionSiege = repartitionSiege;
        this.repartitionCause = repartitionCause;
        this.situationTrains = situationTrains;
        this.statistiquesGlobales = statistiques;

        this.preparerDonneesPieCharts();
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des données consolidées:', error);
        this.loading = false;
      }
    });
  }

  private preparerDonneesPieCharts(): void {
    // Graphique en secteurs pour la répartition par siège
    this.pieChartDataSiege = this.creerDonneesPieChart(
      this.repartitionSiege,
      'siegeCause',
      'totalQuantite',
      'Répartition par Siège'
    );

    // Graphique en secteurs pour la répartition par cause
    this.pieChartDataCause = this.creerDonneesPieChart(
      this.repartitionCause,
      'cause',
      'totalQuantite',
      'Répartition par Cause'
    );
  }

  private creerDonneesPieChart(data: any[], labelField: string, valueField: string, titre: string): any {
    const colors = [
      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
      '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];

    return {
      labels: data.map(item => item[labelField]),
      datasets: [{
        label: titre,
        data: data.map(item => item[valueField]),
        backgroundColor: colors.slice(0, data.length),
        borderColor: colors.slice(0, data.length),
        borderWidth: 1
      }]
    };
  }

  private initPieChartOptions(): void {
    this.pieChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = ((context.parsed / total) * 100).toFixed(1);
              return `${context.label}: ${context.parsed} (${percentage}%)`;
            }
          }
        }
      }
    };
  }

  private formatDateToPeriode(date: Date): string {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${month}/${year}`;
  }

  // Méthodes utilitaires pour l'affichage
  formatNumber(value: number): string {
    return value?.toFixed(1) || '0.0';
  }

  formatCausesList(causes: string[]): string {
    return causes.join(', ');
  }

  getTypeBadgeClass(type: string): string {
    switch(type) {
      case 'INTERNES': return 'badge-interne';
      case 'EXTERNES': return 'badge-externe';
      case 'INTERNE': return 'badge-interne';
      case 'EXTERNE': return 'badge-externe';
      default: return 'badge-default';
    }
  }

  getTotalArrets(): number {
    return this.syntheseGlobale.reduce((total, item) => total + item.totalArrets, 0);
  }

  getTotalHeuresArret(): number {
    return this.syntheseGlobale.reduce((total, item) => total + item.totalHeuresArret, 0);
  }

  getMoyenneMAP(): number {
    if (this.syntheseGlobale.length === 0) return 0;
    const total = this.syntheseGlobale.reduce((sum, item) => sum + item.mapMoyenne, 0);
    return total / this.syntheseGlobale.length;
  }
}
