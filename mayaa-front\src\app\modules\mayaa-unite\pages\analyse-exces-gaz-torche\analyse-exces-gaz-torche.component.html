<div class="analyse-exces-gaz-torche-container">
  <div class="page-header">
    <h2>Analyse de l'excès de l'autoconsommation (Gaz torchés)</h2>
  </div>

  <!-- Statistiques globales -->
  <div class="stats-container" *ngIf="!loading && gazTorcheData.length > 0">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value">{{ getTotalAC() | number:'1.1-1' }}</div>
        <div class="stat-label">Total AC (10³ CM³ GN)</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ getTotalItems() }}</div>
        <div class="stat-label">Nombre d'éléments</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ getUniqueTrains().length }}</div>
        <div class="stat-label">Trains concernés</div>
      </div>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="content-container">

    <!-- Affichage par train -->
    <div class="trains-container" *ngIf="!loading">
      <div *ngFor="let train of getUniqueTrains()" class="train-section">
        <div class="train-header">
          <h3 class="train-title">{{ train }}</h3>
          <div class="train-stats">
            <span class="train-stat">
              <i class="pi pi-chart-bar"></i>
              {{ getTrainTotalAC(train) | number:'1.1-1' }} (10³ CM³ GN)
            </span>
            <span class="train-stat">
              <i class="pi pi-list"></i>
              {{ getTrainItemCount(train) }} éléments
            </span>
          </div>
        </div>

        <div class="table-container">
          <p-table
            [value]="getTrainData(train)"
            styleClass="p-datatable-sm p-datatable-striped train-table"
            responsiveLayout="scroll"
            [sortField]="'ac'"
            [sortOrder]="-1">

            <ng-template pTemplate="header">
              <tr>
                <th *ngFor="let col of gazTorcheColumns"
                    [pSortableColumn]="col.sortable ? col.field : undefined"
                    class="table-header">
                  {{ col.header }}
                  <p-sortIcon [field]="col.field" *ngIf="col.sortable"></p-sortIcon>
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-item>
              <tr>
                <td>{{ item.codeAc }}</td>
                <td>{{ item.problemeSpecifique }}</td>
                <td>{{ item.intitule }}</td>
                <td class="text-right font-weight-bold">{{ item.ac | number:'1.1-1' }}</td>
                <td>{{ item.classeCauses }}</td>
                <td>{{ item.causes }}</td>
                <td>{{ item.actions }}</td>
                <td>
                  <span [class]="'criticality-badge criticality-' + item.classes.toLowerCase().replace(' ', '-')">
                    {{ item.classes }}
                  </span>
                </td>
                <td>
                  <span [class]="'status-badge status-' + item.etat.toLowerCase().replace(' ', '-')">
                    {{ item.etat }}
                  </span>
                </td>
                <td>{{ item.numero }}</td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td [attr.colspan]="gazTorcheColumns.length" class="text-center">
                  Aucune donnée disponible pour ce train
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>

      <!-- Message si aucun train -->
      <div *ngIf="getUniqueTrains().length === 0" class="no-data-message">
        <p-message severity="info" text="Aucun train avec des données de gaz torché disponible"></p-message>
      </div>
    </div>

    <!-- Indicateur de chargement -->
    <div *ngIf="loading" class="loading-container">
      <p-progressSpinner></p-progressSpinner>
      <p>Chargement des données de gaz torché...</p>
    </div>

    <!-- Message si aucune donnée -->
    <div *ngIf="!loading && gazTorcheData.length === 0" class="no-data-message">
      <p-message severity="warn" text="Aucune donnée d'analyse d'excès de gaz torché disponible pour cette période"></p-message>
    </div>

  </div>

  <!-- Résumé par état et criticité -->
  <div class="summary-container" *ngIf="!loading && gazTorcheData.length > 0">
    <div class="summary-grid">

      <!-- Répartition par état -->
      <div class="summary-card">
        <h4>Répartition par État</h4>
        <div class="summary-items">
          <div *ngFor="let status of getStatusDistribution() | keyvalue" class="summary-item">
            <span [class]="'status-badge status-' + status.key.toLowerCase().replace(' ', '-')">
              {{ status.key }}
            </span>
            <span class="summary-count">{{ status.value }}</span>
          </div>
        </div>
      </div>

      <!-- Répartition par criticité -->
      <div class="summary-card">
        <h4>Répartition par Criticité</h4>
        <div class="summary-items">
          <div *ngFor="let criticality of getCriticalityDistribution() | keyvalue" class="summary-item">
            <span [class]="'criticality-badge criticality-' + criticality.key.toLowerCase().replace(' ', '-')">
              {{ criticality.key }}
            </span>
            <span class="summary-count">{{ criticality.value }}</span>
          </div>
        </div>
      </div>

    </div>
  </div>

</div>
