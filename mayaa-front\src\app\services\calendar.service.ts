// src/app/services/calendar.service.ts
import { Injectable, signal, computed } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CalendarService {
  // Signal stockant la date sélectionnée (initialisée à la date courante par défaut)
  selectedMonthYear = signal<Date>(new Date());
  // Signal pour l'unité (par défaut '5X3')
  selectedUnite = signal<string>('5X3');

  // BehaviorSubjects pour compatibilité avec les observables
  private selectedDateSubject = new BehaviorSubject<Date>(new Date());
  private selectedUniteSubject = new BehaviorSubject<string>('5X3');

  // Observables publics
  selectedDate$: Observable<Date> = this.selectedDateSubject.asObservable();
  selectedUnite$: Observable<string> = this.selectedUniteSubject.asObservable();

  constructor() {
    // Synchroniser les BehaviorSubjects avec les signals initiaux
    this.selectedDateSubject.next(this.selectedMonthYear());
    this.selectedUniteSubject.next(this.selectedUnite());
  }

  // Signal computed pour le format pmois (ddMMyyyy)
  pmoisFormat = computed(() => {
    const date = this.selectedMonthYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `01${month}${year}`;
  });

  // Signal computed pour le mois seul (MM)
  monthFormat = computed(() => {
    const date = this.selectedMonthYear();
    return (date.getMonth() + 1).toString().padStart(2, '0');
  });

  // Signal computed pour l'année seule (yyyy)
  yearFormat = computed(() => {
    const date = this.selectedMonthYear();
    return date.getFullYear().toString();
  });

  setSelectedMonthYear(date: Date): void {
    this.selectedMonthYear.set(date);
    this.selectedDateSubject.next(date);
  }

  setSelectedUnite(unite: string): void {
    this.selectedUnite.set(unite);
    this.selectedUniteSubject.next(unite);
  }

  /**
   * Convertit une date en format pmois (ddMMyyyy)
   * @param date Date à convertir
   * @returns Format pmois
   */
  dateToPmoisFormat(date: Date): string {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `01${month}${year}`;
  }

  /**
   * Convertit une date en format mois (MM)
   * @param date Date à convertir
   * @returns Format mois
   */
  dateToMonthFormat(date: Date): string {
    return (date.getMonth() + 1).toString().padStart(2, '0');
  }

  /**
   * Convertit une date en format année (yyyy)
   * @param date Date à convertir
   * @returns Format année
   */
  dateToYearFormat(date: Date): string {
    return date.getFullYear().toString();
  }
}
