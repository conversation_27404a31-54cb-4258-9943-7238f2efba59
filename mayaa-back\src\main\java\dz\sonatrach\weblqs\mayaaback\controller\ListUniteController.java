package dz.sonatrach.weblqs.mayaaback.controller;

import com.fasterxml.jackson.annotation.JsonView;
import dz.sonatrach.weblqs.mayaaback.model.ListUnite;
import dz.sonatrach.weblqs.mayaaback.repo.ListUniteRepository;
import dz.sonatrach.weblqs.mayaaback.views.View;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur pour l'accès aux données de la vue LIST_UNITE.
 * 
 * Endpoints principaux :
 *   GET /api/list-unites - Récupère toutes les unités
 *   GET /api/list-unites/{pmois} - Récupère les unités pour une période donnée
 *   GET /api/list-unites/{codeUnite}/{pmois} - Récupère une unité spécifique pour une période
 *   GET /api/list-unites/unite/{codeUnite} - Récupère toutes les données d'une unité
 *   GET /api/list-unites/unite/{codeUnite}/latest - Récupère la dernière donnée d'une unité
 * 
 * Format des dates : ddMMyyyy (ex: 01122024)
 */
@RestController
@RequestMapping("api/")
public class ListUniteController {

    @Autowired
    private ListUniteRepository listUniteRepository;

    /**
     * Récupère toutes les unités avec leurs objectifs et taux design
     * @return Liste de toutes les unités
     */
    @GetMapping("/list-unites")
    @JsonView(View.basic.class)
    public ResponseEntity<List<ListUnite>> getAllUnites() {
        List<ListUnite> unites = listUniteRepository.findAllByOrderByPmoisDescCodeUniteAsc();
        if (unites.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(unites);
    }

    /**
     * Récupère les unités pour une période donnée
     * @param pmois Période au format ddMMyyyy (ex: 01122024)
     * @return Liste des unités pour cette période
     */
    @GetMapping("/list-unites/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<ListUnite>> getUnitesByPeriode(@PathVariable String pmois) {
        try {
            LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            List<ListUnite> unites = listUniteRepository.findByPmois(date);
            if (unites.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(unites);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère une unité spécifique pour une période donnée
     * @param codeUnite Code de l'unité (ex: 5X2)
     * @param pmois Période au format ddMMyyyy (ex: 01122024)
     * @return L'unité trouvée ou 204 si non trouvée
     */
    @GetMapping("/list-unites/{codeUnite}/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<ListUnite> getUniteByCodeAndPeriode(
            @PathVariable String codeUnite, 
            @PathVariable String pmois) {
        try {
            LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            Optional<ListUnite> unite = listUniteRepository.findByCodeUniteAndPmois(codeUnite, date);
            return unite.map(ResponseEntity::ok)
                       .orElseGet(() -> ResponseEntity.noContent().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Récupère toutes les données pour une unité donnée, triées par période décroissante
     * @param codeUnite Code de l'unité (ex: 5X2)
     * @return Liste des données pour cette unité
     */
    @GetMapping("/list-unites/unite/{codeUnite}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<ListUnite>> getUniteByCode(@PathVariable String codeUnite) {
        List<ListUnite> unites = listUniteRepository.findByCodeUniteOrderByPmoisDesc(codeUnite);
        if (unites.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(unites);
    }

    /**
     * Récupère la dernière donnée disponible pour une unité donnée
     * @param codeUnite Code de l'unité (ex: 5X2)
     * @return La dernière donnée disponible pour cette unité
     */
    @GetMapping("/list-unites/unite/{codeUnite}/latest")
    @JsonView(View.basic.class)
    public ResponseEntity<ListUnite> getLatestUniteByCode(@PathVariable String codeUnite) {
        Optional<ListUnite> unite = listUniteRepository.findFirstByCodeUniteOrderByPmoisDesc(codeUnite);
        return unite.map(ResponseEntity::ok)
                   .orElseGet(() -> ResponseEntity.noContent().build());
    }
}
