package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.AutoConsMens;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import dz.sonatrach.weblqs.mayaaback.repo.AutoConsMensuelRepository;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@RestController
@RequestMapping("api/")
public class AutoConsMensController {

    @Autowired
    private AutoConsMensuelRepository autoConsMensRepository;

    @GetMapping("/auto-cons-mens/{pmois}/{unite}")
    public ResponseEntity<AutoConsMens> getByPmoisAndUnite(@PathVariable String pmois, @PathVariable String unite) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        Optional<AutoConsMens> result = autoConsMensRepository.findByPmoisAndUnite(date, unite);
        return result.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.noContent().build());
    }

    /**
     * Récupère la dernière consommation mensuelle pour une unité donnée
     * (triée par mois décroissant)
     *
     * @param unite L'unité pour laquelle récupérer la dernière consommation
     * @return ResponseEntity contenant la dernière AutoConsMens ou 204 si aucune donnée trouvée
     */
    @GetMapping("/auto-cons-mens/latest/{unite}")
    public ResponseEntity<AutoConsMens> getLatestByUnite(@PathVariable String unite) {
        AutoConsMens result = autoConsMensRepository.findFirstByUniteOrderByPmoisDesc(unite);
        if (result == null) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(result);
    }
}
