package dz.sonatrach.weblqs.mayaaback.service.impl;

import java.time.LocalDate;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;

import dz.sonatrach.weblqs.mayaaback.exception.BadRequestException;
import dz.sonatrach.weblqs.mayaaback.exception.NotFoundException;
import dz.sonatrach.weblqs.mayaaback.model.MUtilisateur;
import dz.sonatrach.weblqs.mayaaback.repo.UsersRepository;
import dz.sonatrach.weblqs.mayaaback.service.UtilisateurService;


@Service
public class UtilisateurServiceImp implements UtilisateurService {

	@Autowired
	UsersRepository usersRepo;


	@Override
	public MUtilisateur getUserByKId(String Kid) {
		if (Kid == null)
			throw new BadRequestException("L'identifiant Keycloak de l'Utilisateur ne peut pas être NULL !");
		Optional<MUtilisateur> user = usersRepo.findByKiduser(Kid);
		if (!user.isPresent())
			throw new NotFoundException();
		else
			return user.get();
	};

	

	@Override
	public MUtilisateur addKuserToDB() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (authentication != null && !(authentication instanceof AnonymousAuthenticationToken)) {
			Jwt jwt = ((JwtAuthenticationToken) authentication).getToken();
			String kiduser = jwt.getClaimAsString("sub");
			Optional<MUtilisateur> existingUser = usersRepo.findByKiduser(kiduser);
			if (!existingUser.isPresent()) {
				MUtilisateur utilisateur = new MUtilisateur();
				utilisateur.setkIdUser(kiduser);
				utilisateur.setUsername(jwt.getClaimAsString("preferred_username"));
				utilisateur.setPrenom(jwt.getClaimAsString("given_name"));
				utilisateur.setNom(jwt.getClaimAsString("family_name"));
				utilisateur.setFonction(jwt.getClaimAsString("intitule"));
				
				String departement = jwt.getClaimAsString("Departement");
				if (departement != null){
					int lastSlashIndex = departement.lastIndexOf("/");
					if (lastSlashIndex != -1) {
						departement = departement.substring(0, lastSlashIndex);
					}
				}		
				
				utilisateur.setStructure(departement);
				utilisateur.setEmail(jwt.getClaimAsString("email"));
				utilisateur.setMatricule(jwt.getClaimAsString("matricule"));
				utilisateur.setDateCreation(LocalDate.now());
				usersRepo.save(utilisateur);
				return utilisateur;
			} else
				return existingUser.get();
		}
		return null;
	}

	
}
