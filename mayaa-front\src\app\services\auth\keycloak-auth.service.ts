import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { interval } from 'rxjs';
import { takeWhile, switchMap } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class KeycloakAuthService {
  private static readonly TOKEN_CHECK_INTERVAL = 30000;
  private static readonly TOKEN_REFRESH_THRESHOLD = 10;
  private tokenCheckInterval: any;
  public isLoggedIn = false;

  constructor(private keycloak: KeycloakService) {
    this.isLoggedIn = this.keycloak.isLoggedIn();
    this.startTokenCheck();
  }

  private startTokenCheck(): void {
    this.tokenCheckInterval = interval(KeycloakAuthService.TOKEN_CHECK_INTERVAL)
      .pipe(
        takeWhile(() => this.isLoggedIn),
        switchMap(() => this.checkTokens())
      )
      .subscribe({
        next: (isValid) => {
          if (!isValid) {
            this.handleTokenExpiration();
          }
        },
        error: () => {
          this.handleTokenExpiration();
        }
      });
  }

  private async checkTokens(): Promise<boolean> {
    try {
      const keycloakInstance = this.keycloak.getKeycloakInstance();
      const tokenParsed = keycloakInstance.tokenParsed;

      if (!tokenParsed?.exp) {
        return false;
      }

      const tokenExpiresIn = tokenParsed.exp - Math.floor(Date.now() / 1000);
      
      if (tokenExpiresIn <= KeycloakAuthService.TOKEN_REFRESH_THRESHOLD) {
        try {
          await keycloakInstance.updateToken(KeycloakAuthService.TOKEN_REFRESH_THRESHOLD);
          return true;
        } catch (error) {
          console.error('Échec du rafraîchissement du token:', error);
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private handleTokenExpiration(): void {
    this.isLoggedIn = false;
    if (this.tokenCheckInterval) {
      this.tokenCheckInterval.unsubscribe();
    }
    this.logout();
  }

  public logout(): void {
    this.isLoggedIn = false;
    this.keycloak.logout();
  }
} 