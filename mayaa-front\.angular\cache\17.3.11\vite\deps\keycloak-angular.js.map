{"version": 3, "sources": ["browser-external:crypto", "browser-external:buffer", "../../../../../node_modules/js-sha256/src/sha256.js", "../../../../../node_modules/keycloak-js/dist/keycloak.mjs", "../../../../../node_modules/jwt-decode/build/esm/index.js", "../../../../../node_modules/keycloak-angular/fesm2022/keycloak-angular.mjs"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"crypto\" has been externalized for browser compatibility. Cannot access \"crypto.${key}\" in client code. See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"buffer\" has been externalized for browser compatibility. Cannot access \"buffer.${key}\" in client code. See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/**\n * [js-sha256]{@link https://github.com/emn178/js-sha256}\n *\n * @version 0.11.1\n * <AUTHOR> <PERSON><PERSON><PERSON> [<EMAIL>]\n * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2025\n * @license MIT\n */\n/*jslint bitwise: true */\n(function () {\n  'use strict';\n\n  var ERROR = 'input is invalid type';\n  var WINDOW = typeof window === 'object';\n  var root = WINDOW ? window : {};\n  if (root.JS_SHA256_NO_WINDOW) {\n    WINDOW = false;\n  }\n  var WEB_WORKER = !WINDOW && typeof self === 'object';\n  var NODE_JS = !root.JS_SHA256_NO_NODE_JS && typeof process === 'object' && process.versions && process.versions.node && process.type != 'renderer';\n  if (NODE_JS) {\n    root = global;\n  } else if (WEB_WORKER) {\n    root = self;\n  }\n  var COMMON_JS = !root.JS_SHA256_NO_COMMON_JS && typeof module === 'object' && module.exports;\n  var AMD = typeof define === 'function' && define.amd;\n  var ARRAY_BUFFER = !root.JS_SHA256_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';\n  var HEX_CHARS = '0123456789abcdef'.split('');\n  var EXTRA = [-**********, 8388608, 32768, 128];\n  var SHIFT = [24, 16, 8, 0];\n  var K = [\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n  ];\n  var OUTPUT_TYPES = ['hex', 'array', 'digest', 'arrayBuffer'];\n\n  var blocks = [];\n\n  if (root.JS_SHA256_NO_NODE_JS || !Array.isArray) {\n    Array.isArray = function (obj) {\n      return Object.prototype.toString.call(obj) === '[object Array]';\n    };\n  }\n\n  if (ARRAY_BUFFER && (root.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW || !ArrayBuffer.isView)) {\n    ArrayBuffer.isView = function (obj) {\n      return typeof obj === 'object' && obj.buffer && obj.buffer.constructor === ArrayBuffer;\n    };\n  }\n\n  var createOutputMethod = function (outputType, is224) {\n    return function (message) {\n      return new Sha256(is224, true).update(message)[outputType]();\n    };\n  };\n\n  var createMethod = function (is224) {\n    var method = createOutputMethod('hex', is224);\n    if (NODE_JS) {\n      method = nodeWrap(method, is224);\n    }\n    method.create = function () {\n      return new Sha256(is224);\n    };\n    method.update = function (message) {\n      return method.create().update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createOutputMethod(type, is224);\n    }\n    return method;\n  };\n\n  var nodeWrap = function (method, is224) {\n    var crypto = require('crypto')\n    var Buffer = require('buffer').Buffer;\n    var algorithm = is224 ? 'sha224' : 'sha256';\n    var bufferFrom;\n    if (Buffer.from && !root.JS_SHA256_NO_BUFFER_FROM) {\n      bufferFrom = Buffer.from;\n    } else {\n      bufferFrom = function (message) {\n        return new Buffer(message);\n      };\n    }\n    var nodeMethod = function (message) {\n      if (typeof message === 'string') {\n        return crypto.createHash(algorithm).update(message, 'utf8').digest('hex');\n      } else {\n        if (message === null || message === undefined) {\n          throw new Error(ERROR);\n        } else if (message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        }\n      }\n      if (Array.isArray(message) || ArrayBuffer.isView(message) ||\n        message.constructor === Buffer) {\n        return crypto.createHash(algorithm).update(bufferFrom(message)).digest('hex');\n      } else {\n        return method(message);\n      }\n    };\n    return nodeMethod;\n  };\n\n  var createHmacOutputMethod = function (outputType, is224) {\n    return function (key, message) {\n      return new HmacSha256(key, is224, true).update(message)[outputType]();\n    };\n  };\n\n  var createHmacMethod = function (is224) {\n    var method = createHmacOutputMethod('hex', is224);\n    method.create = function (key) {\n      return new HmacSha256(key, is224);\n    };\n    method.update = function (key, message) {\n      return method.create(key).update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createHmacOutputMethod(type, is224);\n    }\n    return method;\n  };\n\n  function Sha256(is224, sharedMemory) {\n    if (sharedMemory) {\n      blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      this.blocks = blocks;\n    } else {\n      this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n    }\n\n    if (is224) {\n      this.h0 = 0xc1059ed8;\n      this.h1 = 0x367cd507;\n      this.h2 = 0x3070dd17;\n      this.h3 = 0xf70e5939;\n      this.h4 = 0xffc00b31;\n      this.h5 = 0x68581511;\n      this.h6 = 0x64f98fa7;\n      this.h7 = 0xbefa4fa4;\n    } else { // 256\n      this.h0 = 0x6a09e667;\n      this.h1 = 0xbb67ae85;\n      this.h2 = 0x3c6ef372;\n      this.h3 = 0xa54ff53a;\n      this.h4 = 0x510e527f;\n      this.h5 = 0x9b05688c;\n      this.h6 = 0x1f83d9ab;\n      this.h7 = 0x5be0cd19;\n    }\n\n    this.block = this.start = this.bytes = this.hBytes = 0;\n    this.finalized = this.hashed = false;\n    this.first = true;\n    this.is224 = is224;\n  }\n\n  Sha256.prototype.update = function (message) {\n    if (this.finalized) {\n      return;\n    }\n    var notString, type = typeof message;\n    if (type !== 'string') {\n      if (type === 'object') {\n        if (message === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        } else if (!Array.isArray(message)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(message)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n      notString = true;\n    }\n    var code, index = 0, i, length = message.length, blocks = this.blocks;\n    while (index < length) {\n      if (this.hashed) {\n        this.hashed = false;\n        blocks[0] = this.block;\n        this.block = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n          blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n          blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n          blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      }\n\n      if (notString) {\n        for (i = this.start; index < length && i < 64; ++index) {\n          blocks[i >>> 2] |= message[index] << SHIFT[i++ & 3];\n        }\n      } else {\n        for (i = this.start; index < length && i < 64; ++index) {\n          code = message.charCodeAt(index);\n          if (code < 0x80) {\n            blocks[i >>> 2] |= code << SHIFT[i++ & 3];\n          } else if (code < 0x800) {\n            blocks[i >>> 2] |= (0xc0 | (code >>> 6)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          } else if (code < 0xd800 || code >= 0xe000) {\n            blocks[i >>> 2] |= (0xe0 | (code >>> 12)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 6) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          } else {\n            code = 0x10000 + (((code & 0x3ff) << 10) | (message.charCodeAt(++index) & 0x3ff));\n            blocks[i >>> 2] |= (0xf0 | (code >>> 18)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 12) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 6) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          }\n        }\n      }\n\n      this.lastByteIndex = i;\n      this.bytes += i - this.start;\n      if (i >= 64) {\n        this.block = blocks[16];\n        this.start = i - 64;\n        this.hash();\n        this.hashed = true;\n      } else {\n        this.start = i;\n      }\n    }\n    if (this.bytes > 4294967295) {\n      this.hBytes += this.bytes / 4294967296 << 0;\n      this.bytes = this.bytes % 4294967296;\n    }\n    return this;\n  };\n\n  Sha256.prototype.finalize = function () {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    var blocks = this.blocks, i = this.lastByteIndex;\n    blocks[16] = this.block;\n    blocks[i >>> 2] |= EXTRA[i & 3];\n    this.block = blocks[16];\n    if (i >= 56) {\n      if (!this.hashed) {\n        this.hash();\n      }\n      blocks[0] = this.block;\n      blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n    }\n    blocks[14] = this.hBytes << 3 | this.bytes >>> 29;\n    blocks[15] = this.bytes << 3;\n    this.hash();\n  };\n\n  Sha256.prototype.hash = function () {\n    var a = this.h0, b = this.h1, c = this.h2, d = this.h3, e = this.h4, f = this.h5, g = this.h6,\n      h = this.h7, blocks = this.blocks, j, s0, s1, maj, t1, t2, ch, ab, da, cd, bc;\n\n    for (j = 16; j < 64; ++j) {\n      // rightrotate\n      t1 = blocks[j - 15];\n      s0 = ((t1 >>> 7) | (t1 << 25)) ^ ((t1 >>> 18) | (t1 << 14)) ^ (t1 >>> 3);\n      t1 = blocks[j - 2];\n      s1 = ((t1 >>> 17) | (t1 << 15)) ^ ((t1 >>> 19) | (t1 << 13)) ^ (t1 >>> 10);\n      blocks[j] = blocks[j - 16] + s0 + blocks[j - 7] + s1 << 0;\n    }\n\n    bc = b & c;\n    for (j = 0; j < 64; j += 4) {\n      if (this.first) {\n        if (this.is224) {\n          ab = 300032;\n          t1 = blocks[0] - 1413257819;\n          h = t1 - 150054599 << 0;\n          d = t1 + 24177077 << 0;\n        } else {\n          ab = 704751109;\n          t1 = blocks[0] - 210244248;\n          h = t1 - 1521486534 << 0;\n          d = t1 + 143694565 << 0;\n        }\n        this.first = false;\n      } else {\n        s0 = ((a >>> 2) | (a << 30)) ^ ((a >>> 13) | (a << 19)) ^ ((a >>> 22) | (a << 10));\n        s1 = ((e >>> 6) | (e << 26)) ^ ((e >>> 11) | (e << 21)) ^ ((e >>> 25) | (e << 7));\n        ab = a & b;\n        maj = ab ^ (a & c) ^ bc;\n        ch = (e & f) ^ (~e & g);\n        t1 = h + s1 + ch + K[j] + blocks[j];\n        t2 = s0 + maj;\n        h = d + t1 << 0;\n        d = t1 + t2 << 0;\n      }\n      s0 = ((d >>> 2) | (d << 30)) ^ ((d >>> 13) | (d << 19)) ^ ((d >>> 22) | (d << 10));\n      s1 = ((h >>> 6) | (h << 26)) ^ ((h >>> 11) | (h << 21)) ^ ((h >>> 25) | (h << 7));\n      da = d & a;\n      maj = da ^ (d & b) ^ ab;\n      ch = (h & e) ^ (~h & f);\n      t1 = g + s1 + ch + K[j + 1] + blocks[j + 1];\n      t2 = s0 + maj;\n      g = c + t1 << 0;\n      c = t1 + t2 << 0;\n      s0 = ((c >>> 2) | (c << 30)) ^ ((c >>> 13) | (c << 19)) ^ ((c >>> 22) | (c << 10));\n      s1 = ((g >>> 6) | (g << 26)) ^ ((g >>> 11) | (g << 21)) ^ ((g >>> 25) | (g << 7));\n      cd = c & d;\n      maj = cd ^ (c & a) ^ da;\n      ch = (g & h) ^ (~g & e);\n      t1 = f + s1 + ch + K[j + 2] + blocks[j + 2];\n      t2 = s0 + maj;\n      f = b + t1 << 0;\n      b = t1 + t2 << 0;\n      s0 = ((b >>> 2) | (b << 30)) ^ ((b >>> 13) | (b << 19)) ^ ((b >>> 22) | (b << 10));\n      s1 = ((f >>> 6) | (f << 26)) ^ ((f >>> 11) | (f << 21)) ^ ((f >>> 25) | (f << 7));\n      bc = b & c;\n      maj = bc ^ (b & d) ^ cd;\n      ch = (f & g) ^ (~f & h);\n      t1 = e + s1 + ch + K[j + 3] + blocks[j + 3];\n      t2 = s0 + maj;\n      e = a + t1 << 0;\n      a = t1 + t2 << 0;\n      this.chromeBugWorkAround = true;\n    }\n\n    this.h0 = this.h0 + a << 0;\n    this.h1 = this.h1 + b << 0;\n    this.h2 = this.h2 + c << 0;\n    this.h3 = this.h3 + d << 0;\n    this.h4 = this.h4 + e << 0;\n    this.h5 = this.h5 + f << 0;\n    this.h6 = this.h6 + g << 0;\n    this.h7 = this.h7 + h << 0;\n  };\n\n  Sha256.prototype.hex = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4, h5 = this.h5,\n      h6 = this.h6, h7 = this.h7;\n\n    var hex = HEX_CHARS[(h0 >>> 28) & 0x0F] + HEX_CHARS[(h0 >>> 24) & 0x0F] +\n      HEX_CHARS[(h0 >>> 20) & 0x0F] + HEX_CHARS[(h0 >>> 16) & 0x0F] +\n      HEX_CHARS[(h0 >>> 12) & 0x0F] + HEX_CHARS[(h0 >>> 8) & 0x0F] +\n      HEX_CHARS[(h0 >>> 4) & 0x0F] + HEX_CHARS[h0 & 0x0F] +\n      HEX_CHARS[(h1 >>> 28) & 0x0F] + HEX_CHARS[(h1 >>> 24) & 0x0F] +\n      HEX_CHARS[(h1 >>> 20) & 0x0F] + HEX_CHARS[(h1 >>> 16) & 0x0F] +\n      HEX_CHARS[(h1 >>> 12) & 0x0F] + HEX_CHARS[(h1 >>> 8) & 0x0F] +\n      HEX_CHARS[(h1 >>> 4) & 0x0F] + HEX_CHARS[h1 & 0x0F] +\n      HEX_CHARS[(h2 >>> 28) & 0x0F] + HEX_CHARS[(h2 >>> 24) & 0x0F] +\n      HEX_CHARS[(h2 >>> 20) & 0x0F] + HEX_CHARS[(h2 >>> 16) & 0x0F] +\n      HEX_CHARS[(h2 >>> 12) & 0x0F] + HEX_CHARS[(h2 >>> 8) & 0x0F] +\n      HEX_CHARS[(h2 >>> 4) & 0x0F] + HEX_CHARS[h2 & 0x0F] +\n      HEX_CHARS[(h3 >>> 28) & 0x0F] + HEX_CHARS[(h3 >>> 24) & 0x0F] +\n      HEX_CHARS[(h3 >>> 20) & 0x0F] + HEX_CHARS[(h3 >>> 16) & 0x0F] +\n      HEX_CHARS[(h3 >>> 12) & 0x0F] + HEX_CHARS[(h3 >>> 8) & 0x0F] +\n      HEX_CHARS[(h3 >>> 4) & 0x0F] + HEX_CHARS[h3 & 0x0F] +\n      HEX_CHARS[(h4 >>> 28) & 0x0F] + HEX_CHARS[(h4 >>> 24) & 0x0F] +\n      HEX_CHARS[(h4 >>> 20) & 0x0F] + HEX_CHARS[(h4 >>> 16) & 0x0F] +\n      HEX_CHARS[(h4 >>> 12) & 0x0F] + HEX_CHARS[(h4 >>> 8) & 0x0F] +\n      HEX_CHARS[(h4 >>> 4) & 0x0F] + HEX_CHARS[h4 & 0x0F] +\n      HEX_CHARS[(h5 >>> 28) & 0x0F] + HEX_CHARS[(h5 >>> 24) & 0x0F] +\n      HEX_CHARS[(h5 >>> 20) & 0x0F] + HEX_CHARS[(h5 >>> 16) & 0x0F] +\n      HEX_CHARS[(h5 >>> 12) & 0x0F] + HEX_CHARS[(h5 >>> 8) & 0x0F] +\n      HEX_CHARS[(h5 >>> 4) & 0x0F] + HEX_CHARS[h5 & 0x0F] +\n      HEX_CHARS[(h6 >>> 28) & 0x0F] + HEX_CHARS[(h6 >>> 24) & 0x0F] +\n      HEX_CHARS[(h6 >>> 20) & 0x0F] + HEX_CHARS[(h6 >>> 16) & 0x0F] +\n      HEX_CHARS[(h6 >>> 12) & 0x0F] + HEX_CHARS[(h6 >>> 8) & 0x0F] +\n      HEX_CHARS[(h6 >>> 4) & 0x0F] + HEX_CHARS[h6 & 0x0F];\n    if (!this.is224) {\n      hex += HEX_CHARS[(h7 >>> 28) & 0x0F] + HEX_CHARS[(h7 >>> 24) & 0x0F] +\n        HEX_CHARS[(h7 >>> 20) & 0x0F] + HEX_CHARS[(h7 >>> 16) & 0x0F] +\n        HEX_CHARS[(h7 >>> 12) & 0x0F] + HEX_CHARS[(h7 >>> 8) & 0x0F] +\n        HEX_CHARS[(h7 >>> 4) & 0x0F] + HEX_CHARS[h7 & 0x0F];\n    }\n    return hex;\n  };\n\n  Sha256.prototype.toString = Sha256.prototype.hex;\n\n  Sha256.prototype.digest = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4, h5 = this.h5,\n      h6 = this.h6, h7 = this.h7;\n\n    var arr = [\n      (h0 >>> 24) & 0xFF, (h0 >>> 16) & 0xFF, (h0 >>> 8) & 0xFF, h0 & 0xFF,\n      (h1 >>> 24) & 0xFF, (h1 >>> 16) & 0xFF, (h1 >>> 8) & 0xFF, h1 & 0xFF,\n      (h2 >>> 24) & 0xFF, (h2 >>> 16) & 0xFF, (h2 >>> 8) & 0xFF, h2 & 0xFF,\n      (h3 >>> 24) & 0xFF, (h3 >>> 16) & 0xFF, (h3 >>> 8) & 0xFF, h3 & 0xFF,\n      (h4 >>> 24) & 0xFF, (h4 >>> 16) & 0xFF, (h4 >>> 8) & 0xFF, h4 & 0xFF,\n      (h5 >>> 24) & 0xFF, (h5 >>> 16) & 0xFF, (h5 >>> 8) & 0xFF, h5 & 0xFF,\n      (h6 >>> 24) & 0xFF, (h6 >>> 16) & 0xFF, (h6 >>> 8) & 0xFF, h6 & 0xFF\n    ];\n    if (!this.is224) {\n      arr.push((h7 >>> 24) & 0xFF, (h7 >>> 16) & 0xFF, (h7 >>> 8) & 0xFF, h7 & 0xFF);\n    }\n    return arr;\n  };\n\n  Sha256.prototype.array = Sha256.prototype.digest;\n\n  Sha256.prototype.arrayBuffer = function () {\n    this.finalize();\n\n    var buffer = new ArrayBuffer(this.is224 ? 28 : 32);\n    var dataView = new DataView(buffer);\n    dataView.setUint32(0, this.h0);\n    dataView.setUint32(4, this.h1);\n    dataView.setUint32(8, this.h2);\n    dataView.setUint32(12, this.h3);\n    dataView.setUint32(16, this.h4);\n    dataView.setUint32(20, this.h5);\n    dataView.setUint32(24, this.h6);\n    if (!this.is224) {\n      dataView.setUint32(28, this.h7);\n    }\n    return buffer;\n  };\n\n  function HmacSha256(key, is224, sharedMemory) {\n    var i, type = typeof key;\n    if (type === 'string') {\n      var bytes = [], length = key.length, index = 0, code;\n      for (i = 0; i < length; ++i) {\n        code = key.charCodeAt(i);\n        if (code < 0x80) {\n          bytes[index++] = code;\n        } else if (code < 0x800) {\n          bytes[index++] = (0xc0 | (code >>> 6));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        } else if (code < 0xd800 || code >= 0xe000) {\n          bytes[index++] = (0xe0 | (code >>> 12));\n          bytes[index++] = (0x80 | ((code >>> 6) & 0x3f));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        } else {\n          code = 0x10000 + (((code & 0x3ff) << 10) | (key.charCodeAt(++i) & 0x3ff));\n          bytes[index++] = (0xf0 | (code >>> 18));\n          bytes[index++] = (0x80 | ((code >>> 12) & 0x3f));\n          bytes[index++] = (0x80 | ((code >>> 6) & 0x3f));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        }\n      }\n      key = bytes;\n    } else {\n      if (type === 'object') {\n        if (key === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && key.constructor === ArrayBuffer) {\n          key = new Uint8Array(key);\n        } else if (!Array.isArray(key)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(key)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n    }\n\n    if (key.length > 64) {\n      key = (new Sha256(is224, true)).update(key).array();\n    }\n\n    var oKeyPad = [], iKeyPad = [];\n    for (i = 0; i < 64; ++i) {\n      var b = key[i] || 0;\n      oKeyPad[i] = 0x5c ^ b;\n      iKeyPad[i] = 0x36 ^ b;\n    }\n\n    Sha256.call(this, is224, sharedMemory);\n\n    this.update(iKeyPad);\n    this.oKeyPad = oKeyPad;\n    this.inner = true;\n    this.sharedMemory = sharedMemory;\n  }\n  HmacSha256.prototype = new Sha256();\n\n  HmacSha256.prototype.finalize = function () {\n    Sha256.prototype.finalize.call(this);\n    if (this.inner) {\n      this.inner = false;\n      var innerHash = this.array();\n      Sha256.call(this, this.is224, this.sharedMemory);\n      this.update(this.oKeyPad);\n      this.update(innerHash);\n      Sha256.prototype.finalize.call(this);\n    }\n  };\n\n  var exports = createMethod();\n  exports.sha256 = exports;\n  exports.sha224 = createMethod(true);\n  exports.sha256.hmac = createHmacMethod();\n  exports.sha224.hmac = createHmacMethod(true);\n\n  if (COMMON_JS) {\n    module.exports = exports;\n  } else {\n    root.sha256 = exports.sha256;\n    root.sha224 = exports.sha224;\n    if (AMD) {\n      define(function () {\n        return exports;\n      });\n    }\n  }\n})();\n", "import sha256 from 'js-sha256';\nimport { jwtDecode } from 'jwt-decode';\n\n/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nif (typeof Promise === 'undefined') {\n    throw Error('Keycloak requires an environment that supports Promises. Make sure that you include the appropriate polyfill.');\n}\n\nfunction Keycloak (config) {\n    if (!(this instanceof Keycloak)) {\n        throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\")\n    }\n\n    var kc = this;\n    var adapter;\n    var refreshQueue = [];\n    var callbackStorage;\n\n    var loginIframe = {\n        enable: true,\n        callbackList: [],\n        interval: 5\n    };\n\n    var scripts = document.getElementsByTagName('script');\n    for (var i = 0; i < scripts.length; i++) {\n        if ((scripts[i].src.indexOf('keycloak.js') !== -1 || scripts[i].src.indexOf('keycloak.min.js') !== -1) && scripts[i].src.indexOf('version=') !== -1) {\n            kc.iframeVersion = scripts[i].src.substring(scripts[i].src.indexOf('version=') + 8).split('&')[0];\n        }\n    }\n\n    var useNonce = true;\n    var logInfo = createLogger(console.info);\n    var logWarn = createLogger(console.warn);\n\n    kc.init = function (initOptions) {\n        if (kc.didInitialize) {\n            throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n        }\n\n        kc.didInitialize = true;\n\n        kc.authenticated = false;\n\n        callbackStorage = createCallbackStorage();\n        var adapters = ['default', 'cordova', 'cordova-native'];\n\n        if (initOptions && adapters.indexOf(initOptions.adapter) > -1) {\n            adapter = loadAdapter(initOptions.adapter);\n        } else if (initOptions && typeof initOptions.adapter === \"object\") {\n            adapter = initOptions.adapter;\n        } else {\n            if (window.Cordova || window.cordova) {\n                adapter = loadAdapter('cordova');\n            } else {\n                adapter = loadAdapter();\n            }\n        }\n\n        if (initOptions) {\n            if (typeof initOptions.useNonce !== 'undefined') {\n                useNonce = initOptions.useNonce;\n            }\n\n            if (typeof initOptions.checkLoginIframe !== 'undefined') {\n                loginIframe.enable = initOptions.checkLoginIframe;\n            }\n\n            if (initOptions.checkLoginIframeInterval) {\n                loginIframe.interval = initOptions.checkLoginIframeInterval;\n            }\n\n            if (initOptions.onLoad === 'login-required') {\n                kc.loginRequired = true;\n            }\n\n            if (initOptions.responseMode) {\n                if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n                    kc.responseMode = initOptions.responseMode;\n                } else {\n                    throw 'Invalid value for responseMode';\n                }\n            }\n\n            if (initOptions.flow) {\n                switch (initOptions.flow) {\n                    case 'standard':\n                        kc.responseType = 'code';\n                        break;\n                    case 'implicit':\n                        kc.responseType = 'id_token token';\n                        break;\n                    case 'hybrid':\n                        kc.responseType = 'code id_token token';\n                        break;\n                    default:\n                        throw 'Invalid value for flow';\n                }\n                kc.flow = initOptions.flow;\n            }\n\n            if (initOptions.timeSkew != null) {\n                kc.timeSkew = initOptions.timeSkew;\n            }\n\n            if(initOptions.redirectUri) {\n                kc.redirectUri = initOptions.redirectUri;\n            }\n\n            if (initOptions.silentCheckSsoRedirectUri) {\n                kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n            }\n\n            if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n                kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n            } else {\n                kc.silentCheckSsoFallback = true;\n            }\n\n            if (typeof initOptions.pkceMethod !== \"undefined\") {\n                if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n                    throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n                }\n\n                kc.pkceMethod = initOptions.pkceMethod;\n            } else {\n                kc.pkceMethod = \"S256\";\n            }\n\n            if (typeof initOptions.enableLogging === 'boolean') {\n                kc.enableLogging = initOptions.enableLogging;\n            } else {\n                kc.enableLogging = false;\n            }\n\n            if (initOptions.logoutMethod === 'POST') {\n                kc.logoutMethod = 'POST';\n            } else {\n                kc.logoutMethod = 'GET';\n            }\n\n            if (typeof initOptions.scope === 'string') {\n                kc.scope = initOptions.scope;\n            }\n\n            if (typeof initOptions.acrValues === 'string') {\n                kc.acrValues = initOptions.acrValues;\n            }\n\n            if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n                kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n            } else {\n                kc.messageReceiveTimeout = 10000;\n            }\n        }\n\n        if (!kc.responseMode) {\n            kc.responseMode = 'fragment';\n        }\n        if (!kc.responseType) {\n            kc.responseType = 'code';\n            kc.flow = 'standard';\n        }\n\n        var promise = createPromise();\n\n        var initPromise = createPromise();\n        initPromise.promise.then(function() {\n            kc.onReady && kc.onReady(kc.authenticated);\n            promise.setSuccess(kc.authenticated);\n        }).catch(function(error) {\n            promise.setError(error);\n        });\n\n        var configPromise = loadConfig();\n\n        function onLoad() {\n            var doLogin = function(prompt) {\n                if (!prompt) {\n                    options.prompt = 'none';\n                }\n\n                if (initOptions && initOptions.locale) {\n                    options.locale = initOptions.locale;\n                }\n                kc.login(options).then(function () {\n                    initPromise.setSuccess();\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            };\n\n            var checkSsoSilently = function() {\n                var ifrm = document.createElement(\"iframe\");\n                var src = kc.createLoginUrl({prompt: 'none', redirectUri: kc.silentCheckSsoRedirectUri});\n                ifrm.setAttribute(\"src\", src);\n                ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n                ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n                ifrm.style.display = \"none\";\n                document.body.appendChild(ifrm);\n\n                var messageCallback = function(event) {\n                    if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n                        return;\n                    }\n\n                    var oauth = parseCallback(event.data);\n                    processCallback(oauth, initPromise);\n\n                    document.body.removeChild(ifrm);\n                    window.removeEventListener(\"message\", messageCallback);\n                };\n\n                window.addEventListener(\"message\", messageCallback);\n            };\n\n            var options = {};\n            switch (initOptions.onLoad) {\n                case 'check-sso':\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (!unchanged) {\n                                    kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                    }\n                    break;\n                case 'login-required':\n                    doLogin(true);\n                    break;\n                default:\n                    throw 'Invalid value for onLoad';\n            }\n        }\n\n        function processInit() {\n            var callback = parseCallback(window.location.href);\n\n            if (callback) {\n                window.history.replaceState(window.history.state, null, callback.newUrl);\n            }\n\n            if (callback && callback.valid) {\n                return setupCheckLoginIframe().then(function() {\n                    processCallback(callback, initPromise);\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            } else if (initOptions) {\n                if (initOptions.token && initOptions.refreshToken) {\n                    setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (unchanged) {\n                                    kc.onAuthSuccess && kc.onAuthSuccess();\n                                    initPromise.setSuccess();\n                                    scheduleCheckIframe();\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.updateToken(-1).then(function() {\n                            kc.onAuthSuccess && kc.onAuthSuccess();\n                            initPromise.setSuccess();\n                        }).catch(function(error) {\n                            kc.onAuthError && kc.onAuthError();\n                            if (initOptions.onLoad) {\n                                onLoad();\n                            } else {\n                                initPromise.setError(error);\n                            }\n                        });\n                    }\n                } else if (initOptions.onLoad) {\n                    onLoad();\n                } else {\n                    initPromise.setSuccess();\n                }\n            } else {\n                initPromise.setSuccess();\n            }\n        }\n\n        function domReady() {\n            var promise = createPromise();\n\n            var checkReadyState = function () {\n                if (document.readyState === 'interactive' || document.readyState === 'complete') {\n                    document.removeEventListener('readystatechange', checkReadyState);\n                    promise.setSuccess();\n                }\n            };\n            document.addEventListener('readystatechange', checkReadyState);\n\n            checkReadyState(); // just in case the event was already fired and we missed it (in case the init is done later than at the load time, i.e. it's done from code)\n\n            return promise.promise;\n        }\n\n        configPromise.then(function () {\n            domReady()\n                .then(check3pCookiesSupported)\n                .then(processInit)\n                .catch(function (error) {\n                    promise.setError(error);\n                });\n        });\n        configPromise.catch(function (error) {\n            promise.setError(error);\n        });\n\n        return promise.promise;\n    };\n\n    kc.login = function (options) {\n        return adapter.login(options);\n    };\n\n    function generateRandomData(len) {\n        // use web crypto APIs if possible\n        var array = null;\n        var crypto = window.crypto || window.msCrypto;\n        if (crypto && crypto.getRandomValues && window.Uint8Array) {\n            array = new Uint8Array(len);\n            crypto.getRandomValues(array);\n            return array;\n        }\n\n        // fallback to Math random\n        array = new Array(len);\n        for (var j = 0; j < array.length; j++) {\n            array[j] = Math.floor(256 * Math.random());\n        }\n        return array;\n    }\n\n    function generateCodeVerifier(len) {\n        return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n    }\n\n    function generateRandomString(len, alphabet){\n        var randomData = generateRandomData(len);\n        var chars = new Array(len);\n        for (var i = 0; i < len; i++) {\n            chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n        }\n        return String.fromCharCode.apply(null, chars);\n    }\n\n    function generatePkceChallenge(pkceMethod, codeVerifier) {\n        if (pkceMethod !== \"S256\") {\n            throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n        }\n\n        // hash codeVerifier, then encode as url-safe base64 without padding\n        const hashBytes = new Uint8Array(sha256.arrayBuffer(codeVerifier));\n        const encodedHash = bytesToBase64(hashBytes)\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/\\=/g, '');\n\n        return encodedHash;\n    }\n\n    function buildClaimsParameter(requestedAcr){\n        var claims = {\n            id_token: {\n                acr: requestedAcr\n            }\n        };\n        return JSON.stringify(claims);\n    }\n\n    kc.createLoginUrl = function(options) {\n        var state = createUUID();\n        var nonce = createUUID();\n\n        var redirectUri = adapter.redirectUri(options);\n\n        var callbackState = {\n            state: state,\n            nonce: nonce,\n            redirectUri: encodeURIComponent(redirectUri)\n        };\n\n        if (options && options.prompt) {\n            callbackState.prompt = options.prompt;\n        }\n\n        var baseUrl;\n        if (options && options.action == 'register') {\n            baseUrl = kc.endpoints.register();\n        } else {\n            baseUrl = kc.endpoints.authorize();\n        }\n\n        var scope = options && options.scope || kc.scope;\n        if (!scope) {\n            // if scope is not set, default to \"openid\"\n            scope = \"openid\";\n        } else if (scope.indexOf(\"openid\") === -1) {\n            // if openid scope is missing, prefix the given scopes with it\n            scope = \"openid \" + scope;\n        }\n\n        var url = baseUrl\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&redirect_uri=' + encodeURIComponent(redirectUri)\n            + '&state=' + encodeURIComponent(state)\n            + '&response_mode=' + encodeURIComponent(kc.responseMode)\n            + '&response_type=' + encodeURIComponent(kc.responseType)\n            + '&scope=' + encodeURIComponent(scope);\n        if (useNonce) {\n            url = url + '&nonce=' + encodeURIComponent(nonce);\n        }\n\n        if (options && options.prompt) {\n            url += '&prompt=' + encodeURIComponent(options.prompt);\n        }\n\n        if (options && options.maxAge) {\n            url += '&max_age=' + encodeURIComponent(options.maxAge);\n        }\n\n        if (options && options.loginHint) {\n            url += '&login_hint=' + encodeURIComponent(options.loginHint);\n        }\n\n        if (options && options.idpHint) {\n            url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n        }\n\n        if (options && options.action && options.action != 'register') {\n            url += '&kc_action=' + encodeURIComponent(options.action);\n        }\n\n        if (options && options.locale) {\n            url += '&ui_locales=' + encodeURIComponent(options.locale);\n        }\n\n        if (options && options.acr) {\n            var claimsParameter = buildClaimsParameter(options.acr);\n            url += '&claims=' + encodeURIComponent(claimsParameter);\n        }\n\n        if ((options && options.acrValues) || kc.acrValues) {\n            url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n        }\n\n        if (kc.pkceMethod) {\n            var codeVerifier = generateCodeVerifier(96);\n            callbackState.pkceCodeVerifier = codeVerifier;\n            var pkceChallenge = generatePkceChallenge(kc.pkceMethod, codeVerifier);\n            url += '&code_challenge=' + pkceChallenge;\n            url += '&code_challenge_method=' + kc.pkceMethod;\n        }\n\n        callbackStorage.add(callbackState);\n\n        return url;\n    };\n\n    kc.logout = function(options) {\n        return adapter.logout(options);\n    };\n\n    kc.createLogoutUrl = function(options) {\n\n        const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n        if (logoutMethod === 'POST') {\n            return kc.endpoints.logout();\n        }\n\n        var url = kc.endpoints.logout()\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n\n        if (kc.idToken) {\n            url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n        }\n\n        return url;\n    };\n\n    kc.register = function (options) {\n        return adapter.register(options);\n    };\n\n    kc.createRegisterUrl = function(options) {\n        if (!options) {\n            options = {};\n        }\n        options.action = 'register';\n        return kc.createLoginUrl(options);\n    };\n\n    kc.createAccountUrl = function(options) {\n        var realm = getRealmUrl();\n        var url = undefined;\n        if (typeof realm !== 'undefined') {\n            url = realm\n            + '/account'\n            + '?referrer=' + encodeURIComponent(kc.clientId)\n            + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n        }\n        return url;\n    };\n\n    kc.accountManagement = function() {\n        return adapter.accountManagement();\n    };\n\n    kc.hasRealmRole = function (role) {\n        var access = kc.realmAccess;\n        return !!access && access.roles.indexOf(role) >= 0;\n    };\n\n    kc.hasResourceRole = function(role, resource) {\n        if (!kc.resourceAccess) {\n            return false;\n        }\n\n        var access = kc.resourceAccess[resource || kc.clientId];\n        return !!access && access.roles.indexOf(role) >= 0;\n    };\n\n    kc.loadUserProfile = function() {\n        var url = getRealmUrl() + '/account';\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.profile = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.profile);\n                } else {\n                    promise.setError();\n                }\n            }\n        };\n\n        req.send();\n\n        return promise.promise;\n    };\n\n    kc.loadUserInfo = function() {\n        var url = kc.endpoints.userinfo();\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.userInfo = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.userInfo);\n                } else {\n                    promise.setError();\n                }\n            }\n        };\n\n        req.send();\n\n        return promise.promise;\n    };\n\n    kc.isTokenExpired = function(minValidity) {\n        if (!kc.tokenParsed || (!kc.refreshToken && kc.flow != 'implicit' )) {\n            throw 'Not authenticated';\n        }\n\n        if (kc.timeSkew == null) {\n            logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n            return true;\n        }\n\n        var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n        if (minValidity) {\n            if (isNaN(minValidity)) {\n                throw 'Invalid minValidity';\n            }\n            expiresIn -= minValidity;\n        }\n        return expiresIn < 0;\n    };\n\n    kc.updateToken = function(minValidity) {\n        var promise = createPromise();\n\n        if (!kc.refreshToken) {\n            promise.setError();\n            return promise.promise;\n        }\n\n        minValidity = minValidity || 5;\n\n        var exec = function() {\n            var refreshToken = false;\n            if (minValidity == -1) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n            } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: token expired');\n            }\n\n            if (!refreshToken) {\n                promise.setSuccess(false);\n            } else {\n                var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n                var url = kc.endpoints.token();\n\n                refreshQueue.push(promise);\n\n                if (refreshQueue.length == 1) {\n                    var req = new XMLHttpRequest();\n                    req.open('POST', url, true);\n                    req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n                    req.withCredentials = true;\n\n                    params += '&client_id=' + encodeURIComponent(kc.clientId);\n\n                    var timeLocal = new Date().getTime();\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200) {\n                                logInfo('[KEYCLOAK] Token refreshed');\n\n                                timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n                                var tokenResponse = JSON.parse(req.responseText);\n\n                                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n\n                                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setSuccess(true);\n                                }\n                            } else {\n                                logWarn('[KEYCLOAK] Failed to refresh token');\n\n                                if (req.status == 400) {\n                                    kc.clearToken();\n                                }\n\n                                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setError(true);\n                                }\n                            }\n                        }\n                    };\n\n                    req.send(params);\n                }\n            }\n        };\n\n        if (loginIframe.enable) {\n            var iframePromise = checkLoginIframe();\n            iframePromise.then(function() {\n                exec();\n            }).catch(function(error) {\n                promise.setError(error);\n            });\n        } else {\n            exec();\n        }\n\n        return promise.promise;\n    };\n\n    kc.clearToken = function() {\n        if (kc.token) {\n            setToken(null, null, null);\n            kc.onAuthLogout && kc.onAuthLogout();\n            if (kc.loginRequired) {\n                kc.login();\n            }\n        }\n    };\n\n    function getRealmUrl() {\n        if (typeof kc.authServerUrl !== 'undefined') {\n            if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n                return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n            } else {\n                return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n            }\n        } else {\n            return undefined;\n        }\n    }\n\n    function getOrigin() {\n        if (!window.location.origin) {\n            return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');\n        } else {\n            return window.location.origin;\n        }\n    }\n\n    function processCallback(oauth, promise) {\n        var code = oauth.code;\n        var error = oauth.error;\n        var prompt = oauth.prompt;\n\n        var timeLocal = new Date().getTime();\n\n        if (oauth['kc_action_status']) {\n            kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status']);\n        }\n\n        if (error) {\n            if (prompt != 'none') {\n                var errorData = { error: error, error_description: oauth.error_description };\n                kc.onAuthError && kc.onAuthError(errorData);\n                promise && promise.setError(errorData);\n            } else {\n                promise && promise.setSuccess();\n            }\n            return;\n        } else if ((kc.flow != 'standard') && (oauth.access_token || oauth.id_token)) {\n            authSuccess(oauth.access_token, null, oauth.id_token, true);\n        }\n\n        if ((kc.flow != 'implicit') && code) {\n            var params = 'code=' + code + '&grant_type=authorization_code';\n            var url = kc.endpoints.token();\n\n            var req = new XMLHttpRequest();\n            req.open('POST', url, true);\n            req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n\n            params += '&client_id=' + encodeURIComponent(kc.clientId);\n            params += '&redirect_uri=' + oauth.redirectUri;\n\n            if (oauth.pkceCodeVerifier) {\n                params += '&code_verifier=' + oauth.pkceCodeVerifier;\n            }\n\n            req.withCredentials = true;\n\n            req.onreadystatechange = function() {\n                if (req.readyState == 4) {\n                    if (req.status == 200) {\n\n                        var tokenResponse = JSON.parse(req.responseText);\n                        authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n                        scheduleCheckIframe();\n                    } else {\n                        kc.onAuthError && kc.onAuthError();\n                        promise && promise.setError();\n                    }\n                }\n            };\n\n            req.send(params);\n        }\n\n        function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n            timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n            setToken(accessToken, refreshToken, idToken, timeLocal);\n\n            if (useNonce && (kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce)) {\n                logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n                kc.clearToken();\n                promise && promise.setError();\n            } else {\n                if (fulfillPromise) {\n                    kc.onAuthSuccess && kc.onAuthSuccess();\n                    promise && promise.setSuccess();\n                }\n            }\n        }\n\n    }\n\n    function loadConfig(url) {\n        var promise = createPromise();\n        var configUrl;\n\n        if (!config) {\n            configUrl = 'keycloak.json';\n        } else if (typeof config === 'string') {\n            configUrl = config;\n        }\n\n        function setupOidcEndoints(oidcConfiguration) {\n            if (! oidcConfiguration) {\n                kc.endpoints = {\n                    authorize: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/auth';\n                    },\n                    token: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/token';\n                    },\n                    logout: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/logout';\n                    },\n                    checkSessionIframe: function() {\n                        var src = getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n                        if (kc.iframeVersion) {\n                            src = src + '?version=' + kc.iframeVersion;\n                        }\n                        return src;\n                    },\n                    thirdPartyCookiesIframe: function() {\n                        var src = getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n                        if (kc.iframeVersion) {\n                            src = src + '?version=' + kc.iframeVersion;\n                        }\n                        return src;\n                    },\n                    register: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/registrations';\n                    },\n                    userinfo: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/userinfo';\n                    }\n                };\n            } else {\n                kc.endpoints = {\n                    authorize: function() {\n                        return oidcConfiguration.authorization_endpoint;\n                    },\n                    token: function() {\n                        return oidcConfiguration.token_endpoint;\n                    },\n                    logout: function() {\n                        if (!oidcConfiguration.end_session_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.end_session_endpoint;\n                    },\n                    checkSessionIframe: function() {\n                        if (!oidcConfiguration.check_session_iframe) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.check_session_iframe;\n                    },\n                    register: function() {\n                        throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n                    },\n                    userinfo: function() {\n                        if (!oidcConfiguration.userinfo_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.userinfo_endpoint;\n                    }\n                };\n            }\n        }\n\n        if (configUrl) {\n            var req = new XMLHttpRequest();\n            req.open('GET', configUrl, true);\n            req.setRequestHeader('Accept', 'application/json');\n\n            req.onreadystatechange = function () {\n                if (req.readyState == 4) {\n                    if (req.status == 200 || fileLoaded(req)) {\n                        var config = JSON.parse(req.responseText);\n\n                        kc.authServerUrl = config['auth-server-url'];\n                        kc.realm = config['realm'];\n                        kc.clientId = config['resource'];\n                        setupOidcEndoints(null);\n                        promise.setSuccess();\n                    } else {\n                        promise.setError();\n                    }\n                }\n            };\n\n            req.send();\n        } else {\n            if (!config.clientId) {\n                throw 'clientId missing';\n            }\n\n            kc.clientId = config.clientId;\n\n            var oidcProvider = config['oidcProvider'];\n            if (!oidcProvider) {\n                if (!config['url']) {\n                    var scripts = document.getElementsByTagName('script');\n                    for (var i = 0; i < scripts.length; i++) {\n                        if (scripts[i].src.match(/.*keycloak\\.js/)) {\n                            config.url = scripts[i].src.substr(0, scripts[i].src.indexOf('/js/keycloak.js'));\n                            break;\n                        }\n                    }\n                }\n                if (!config.realm) {\n                    throw 'realm missing';\n                }\n\n                kc.authServerUrl = config.url;\n                kc.realm = config.realm;\n                setupOidcEndoints(null);\n                promise.setSuccess();\n            } else {\n                if (typeof oidcProvider === 'string') {\n                    var oidcProviderConfigUrl;\n                    if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n                        oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n                    } else {\n                        oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n                    }\n                    var req = new XMLHttpRequest();\n                    req.open('GET', oidcProviderConfigUrl, true);\n                    req.setRequestHeader('Accept', 'application/json');\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200 || fileLoaded(req)) {\n                                var oidcProviderConfig = JSON.parse(req.responseText);\n                                setupOidcEndoints(oidcProviderConfig);\n                                promise.setSuccess();\n                            } else {\n                                promise.setError();\n                            }\n                        }\n                    };\n\n                    req.send();\n                } else {\n                    setupOidcEndoints(oidcProvider);\n                    promise.setSuccess();\n                }\n            }\n        }\n\n        return promise.promise;\n    }\n\n    function fileLoaded(xhr) {\n        return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n    }\n\n    function setToken(token, refreshToken, idToken, timeLocal) {\n        if (kc.tokenTimeoutHandle) {\n            clearTimeout(kc.tokenTimeoutHandle);\n            kc.tokenTimeoutHandle = null;\n        }\n\n        if (refreshToken) {\n            kc.refreshToken = refreshToken;\n            kc.refreshTokenParsed = jwtDecode(refreshToken);\n        } else {\n            delete kc.refreshToken;\n            delete kc.refreshTokenParsed;\n        }\n\n        if (idToken) {\n            kc.idToken = idToken;\n            kc.idTokenParsed = jwtDecode(idToken);\n        } else {\n            delete kc.idToken;\n            delete kc.idTokenParsed;\n        }\n\n        if (token) {\n            kc.token = token;\n            kc.tokenParsed = jwtDecode(token);\n            kc.sessionId = kc.tokenParsed.sid;\n            kc.authenticated = true;\n            kc.subject = kc.tokenParsed.sub;\n            kc.realmAccess = kc.tokenParsed.realm_access;\n            kc.resourceAccess = kc.tokenParsed.resource_access;\n\n            if (timeLocal) {\n                kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n            }\n\n            if (kc.timeSkew != null) {\n                logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n\n                if (kc.onTokenExpired) {\n                    var expiresIn = (kc.tokenParsed['exp'] - (new Date().getTime() / 1000) + kc.timeSkew) * 1000;\n                    logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n                    if (expiresIn <= 0) {\n                        kc.onTokenExpired();\n                    } else {\n                        kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n                    }\n                }\n            }\n        } else {\n            delete kc.token;\n            delete kc.tokenParsed;\n            delete kc.subject;\n            delete kc.realmAccess;\n            delete kc.resourceAccess;\n\n            kc.authenticated = false;\n        }\n    }\n\n    function createUUID() {\n        var hexDigits = '0123456789abcdef';\n        var s = generateRandomString(36, hexDigits).split(\"\");\n        s[14] = '4';\n        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);\n        s[8] = s[13] = s[18] = s[23] = '-';\n        var uuid = s.join('');\n        return uuid;\n    }\n\n    function parseCallback(url) {\n        var oauth = parseCallbackUrl(url);\n        if (!oauth) {\n            return;\n        }\n\n        var oauthState = callbackStorage.get(oauth.state);\n\n        if (oauthState) {\n            oauth.valid = true;\n            oauth.redirectUri = oauthState.redirectUri;\n            oauth.storedNonce = oauthState.nonce;\n            oauth.prompt = oauthState.prompt;\n            oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n        }\n\n        return oauth;\n    }\n\n    function parseCallbackUrl(url) {\n        var supportedParams;\n        switch (kc.flow) {\n            case 'standard':\n                supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'iss'];\n                break;\n            case 'implicit':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'iss'];\n                break;\n            case 'hybrid':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'iss'];\n                break;\n        }\n\n        supportedParams.push('error');\n        supportedParams.push('error_description');\n        supportedParams.push('error_uri');\n\n        var queryIndex = url.indexOf('?');\n        var fragmentIndex = url.indexOf('#');\n\n        var newUrl;\n        var parsed;\n\n        if (kc.responseMode === 'query' && queryIndex !== -1) {\n            newUrl = url.substring(0, queryIndex);\n            parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '?' + parsed.paramsString;\n            }\n            if (fragmentIndex !== -1) {\n                newUrl += url.substring(fragmentIndex);\n            }\n        } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n            newUrl = url.substring(0, fragmentIndex);\n            parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '#' + parsed.paramsString;\n            }\n        }\n\n        if (parsed && parsed.oauthParams) {\n            if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n                if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            } else if (kc.flow === 'implicit') {\n                if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            }\n        }\n    }\n\n    function parseCallbackParams(paramsString, supportedParams) {\n        var p = paramsString.split('&');\n        var result = {\n            paramsString: '',\n            oauthParams: {}\n        };\n        for (var i = 0; i < p.length; i++) {\n            var split = p[i].indexOf(\"=\");\n            var key = p[i].slice(0, split);\n            if (supportedParams.indexOf(key) !== -1) {\n                result.oauthParams[key] = p[i].slice(split + 1);\n            } else {\n                if (result.paramsString !== '') {\n                    result.paramsString += '&';\n                }\n                result.paramsString += p[i];\n            }\n        }\n        return result;\n    }\n\n    function createPromise() {\n        // Need to create a native Promise which also preserves the\n        // interface of the custom promise type previously used by the API\n        var p = {\n            setSuccess: function(result) {\n                p.resolve(result);\n            },\n\n            setError: function(result) {\n                p.reject(result);\n            }\n        };\n        p.promise = new Promise(function(resolve, reject) {\n            p.resolve = resolve;\n            p.reject = reject;\n        });\n\n        return p;\n    }\n\n    // Function to extend existing native Promise with timeout\n    function applyTimeoutToPromise(promise, timeout, errorMessage) {\n        var timeoutHandle = null;\n        var timeoutPromise = new Promise(function (resolve, reject) {\n            timeoutHandle = setTimeout(function () {\n                reject({ \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\" });\n            }, timeout);\n        });\n\n        return Promise.race([promise, timeoutPromise]).finally(function () {\n            clearTimeout(timeoutHandle);\n        });\n    }\n\n    function setupCheckLoginIframe() {\n        var promise = createPromise();\n\n        if (!loginIframe.enable) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        if (loginIframe.iframe) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        var iframe = document.createElement('iframe');\n        loginIframe.iframe = iframe;\n\n        iframe.onload = function() {\n            var authUrl = kc.endpoints.authorize();\n            if (authUrl.charAt(0) === '/') {\n                loginIframe.iframeOrigin = getOrigin();\n            } else {\n                loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n            }\n            promise.setSuccess();\n        };\n\n        var src = kc.endpoints.checkSessionIframe();\n        iframe.setAttribute('src', src );\n        iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n        iframe.setAttribute('title', 'keycloak-session-iframe' );\n        iframe.style.display = 'none';\n        document.body.appendChild(iframe);\n\n        var messageCallback = function(event) {\n            if ((event.origin !== loginIframe.iframeOrigin) || (loginIframe.iframe.contentWindow !== event.source)) {\n                return;\n            }\n\n            if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n                return;\n            }\n\n\n            if (event.data != 'unchanged') {\n                kc.clearToken();\n            }\n\n            var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n\n            for (var i = callbacks.length - 1; i >= 0; --i) {\n                var promise = callbacks[i];\n                if (event.data == 'error') {\n                    promise.setError();\n                } else {\n                    promise.setSuccess(event.data == 'unchanged');\n                }\n            }\n        };\n\n        window.addEventListener('message', messageCallback, false);\n\n        return promise.promise;\n    }\n\n    function scheduleCheckIframe() {\n        if (loginIframe.enable) {\n            if (kc.token) {\n                setTimeout(function() {\n                    checkLoginIframe().then(function(unchanged) {\n                        if (unchanged) {\n                            scheduleCheckIframe();\n                        }\n                    });\n                }, loginIframe.interval * 1000);\n            }\n        }\n    }\n\n    function checkLoginIframe() {\n        var promise = createPromise();\n\n        if (loginIframe.iframe && loginIframe.iframeOrigin ) {\n            var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n            loginIframe.callbackList.push(promise);\n            var origin = loginIframe.iframeOrigin;\n            if (loginIframe.callbackList.length == 1) {\n                loginIframe.iframe.contentWindow.postMessage(msg, origin);\n            }\n        } else {\n            promise.setSuccess();\n        }\n\n        return promise.promise;\n    }\n\n    function check3pCookiesSupported() {\n        var promise = createPromise();\n\n        if (loginIframe.enable || kc.silentCheckSsoRedirectUri) {\n            var iframe = document.createElement('iframe');\n            iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n            iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n            iframe.setAttribute('title', 'keycloak-3p-check-iframe' );\n            iframe.style.display = 'none';\n            document.body.appendChild(iframe);\n\n            var messageCallback = function(event) {\n                if (iframe.contentWindow !== event.source) {\n                    return;\n                }\n\n                if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n                    return;\n                } else if (event.data === \"unsupported\") {\n                    logWarn(\n                        \"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" +\n                        \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" +\n                        \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" +\n                        \"For more information see: https://www.keycloak.org/docs/latest/securing_apps/#_modern_browsers\"\n                    );\n\n                    loginIframe.enable = false;\n                    if (kc.silentCheckSsoFallback) {\n                        kc.silentCheckSsoRedirectUri = false;\n                    }\n                }\n\n                document.body.removeChild(iframe);\n                window.removeEventListener(\"message\", messageCallback);\n                promise.setSuccess();\n            };\n\n            window.addEventListener('message', messageCallback, false);\n        } else {\n            promise.setSuccess();\n        }\n\n        return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n    }\n\n    function loadAdapter(type) {\n        if (!type || type == 'default') {\n            return {\n                login: function(options) {\n                    window.location.assign(kc.createLoginUrl(options));\n                    return createPromise().promise;\n                },\n\n                logout: async function(options) {\n\n                    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n                    if (logoutMethod === \"GET\") {\n                        window.location.replace(kc.createLogoutUrl(options));\n                        return;\n                    }\n\n                    const logoutUrl = kc.createLogoutUrl(options);\n                    const response = await fetch(logoutUrl, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/x-www-form-urlencoded\"\n                        },\n                        body: new URLSearchParams({\n                            id_token_hint: kc.idToken,\n                            client_id: kc.clientId,\n                            post_logout_redirect_uri: adapter.redirectUri(options, false)\n                        })\n                    });\n\n                    if (response.redirected) {\n                        window.location.href = response.url;\n                        return;\n                    }\n\n                    if (response.ok) {\n                        window.location.reload();\n                        return;\n                    }\n\n                    throw new Error(\"Logout failed, request returned an error code.\");\n                },\n\n                register: function(options) {\n                    window.location.assign(kc.createRegisterUrl(options));\n                    return createPromise().promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.location.href = accountUrl;\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                    return createPromise().promise;\n                },\n\n                redirectUri: function(options, encodeHash) {\n\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return location.href;\n                    }\n                }\n            };\n        }\n\n        if (type == 'cordova') {\n            loginIframe.enable = false;\n            var cordovaOpenWindowWrapper = function(loginUrl, target, options) {\n                if (window.cordova && window.cordova.InAppBrowser) {\n                    // Use inappbrowser for IOS and Android if available\n                    return window.cordova.InAppBrowser.open(loginUrl, target, options);\n                } else {\n                    return window.open(loginUrl, target, options);\n                }\n            };\n\n            var shallowCloneCordovaOptions = function (userOptions) {\n                if (userOptions && userOptions.cordovaOptions) {\n                    return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n                        options[optionName] = userOptions.cordovaOptions[optionName];\n                        return options;\n                    }, {});\n                } else {\n                    return {};\n                }\n            };\n\n            var formatCordovaOptions = function (cordovaOptions) {\n                return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n                    options.push(optionName+\"=\"+cordovaOptions[optionName]);\n                    return options;\n                }, []).join(\",\");\n            };\n\n            var createCordovaOptions = function (userOptions) {\n                var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n                cordovaOptions.location = 'no';\n                if (userOptions && userOptions.prompt == 'none') {\n                    cordovaOptions.hidden = 'yes';\n                }\n                return formatCordovaOptions(cordovaOptions);\n            };\n\n            var getCordovaRedirectUri = function() {\n                return kc.redirectUri || 'http://localhost';\n            };\n            \n            return {\n                login: function(options) {\n                    var promise = createPromise();\n\n                    var cordovaOptions = createCordovaOptions(options);\n                    var loginUrl = kc.createLoginUrl(options);\n                    var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n                    var completed = false;\n\n                    var closed = false;\n                    var closeBrowser = function() {\n                        closed = true;\n                        ref.close();\n                    };\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            var callback = parseCallback(event.url);\n                            processCallback(callback, promise);\n                            closeBrowser();\n                            completed = true;\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (!completed) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                var callback = parseCallback(event.url);\n                                processCallback(callback, promise);\n                                closeBrowser();\n                                completed = true;\n                            } else {\n                                promise.setError();\n                                closeBrowser();\n                            }\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (!closed) {\n                            promise.setError({\n                                reason: \"closed_by_user\"\n                            });\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n\n                    var logoutUrl = kc.createLogoutUrl(options);\n                    var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n\n                    var error;\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        } else {\n                            error = true;\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (error) {\n                            promise.setError();\n                        } else {\n                            kc.clearToken();\n                            promise.setSuccess();\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                register : function(options) {\n                    var promise = createPromise();\n                    var registerUrl = kc.createRegisterUrl();\n                    var cordovaOptions = createCordovaOptions(options);\n                    var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                            var oauth = parseCallback(event.url);\n                            processCallback(oauth, promise);\n                        }\n                    });\n                    return promise.promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n                        ref.addEventListener('loadstart', function(event) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                ref.close();\n                            }\n                        });\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    return getCordovaRedirectUri();\n                }\n            }\n        }\n\n        if (type == 'cordova-native') {\n            loginIframe.enable = false;\n\n            return {\n                login: function(options) {\n                    var promise = createPromise();\n                    var loginUrl = kc.createLoginUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(loginUrl);\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n                    var logoutUrl = kc.createLogoutUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        kc.clearToken();\n                        promise.setSuccess();\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(logoutUrl);\n                    return promise.promise;\n                },\n\n                register : function(options) {\n                    var promise = createPromise();\n                    var registerUrl = kc.createRegisterUrl(options);\n                    universalLinks.subscribe('keycloak' , function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n                    window.cordova.plugins.browsertab.openUrl(registerUrl);\n                    return promise.promise;\n\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.cordova.plugins.browsertab.openUrl(accountUrl);\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return \"http://localhost\";\n                    }\n                }\n            }\n        }\n\n        throw 'invalid adapter type: ' + type;\n    }\n\n    var LocalStorage = function() {\n        if (!(this instanceof LocalStorage)) {\n            return new LocalStorage();\n        }\n\n        localStorage.setItem('kc-test', 'test');\n        localStorage.removeItem('kc-test');\n\n        var cs = this;\n\n        function clearExpired() {\n            var time = new Date().getTime();\n            for (var i = 0; i < localStorage.length; i++)  {\n                var key = localStorage.key(i);\n                if (key && key.indexOf('kc-callback-') == 0) {\n                    var value = localStorage.getItem(key);\n                    if (value) {\n                        try {\n                            var expires = JSON.parse(value).expires;\n                            if (!expires || expires < time) {\n                                localStorage.removeItem(key);\n                            }\n                        } catch (err) {\n                            localStorage.removeItem(key);\n                        }\n                    }\n                }\n            }\n        }\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var key = 'kc-callback-' + state;\n            var value = localStorage.getItem(key);\n            if (value) {\n                localStorage.removeItem(key);\n                value = JSON.parse(value);\n            }\n\n            clearExpired();\n            return value;\n        };\n\n        cs.add = function(state) {\n            clearExpired();\n\n            var key = 'kc-callback-' + state.state;\n            state.expires = new Date().getTime() + (60 * 60 * 1000);\n            localStorage.setItem(key, JSON.stringify(state));\n        };\n    };\n\n    var CookieStorage = function() {\n        if (!(this instanceof CookieStorage)) {\n            return new CookieStorage();\n        }\n\n        var cs = this;\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var value = getCookie('kc-callback-' + state);\n            setCookie('kc-callback-' + state, '', cookieExpiration(-100));\n            if (value) {\n                return JSON.parse(value);\n            }\n        };\n\n        cs.add = function(state) {\n            setCookie('kc-callback-' + state.state, JSON.stringify(state), cookieExpiration(60));\n        };\n\n        cs.removeItem = function(key) {\n            setCookie(key, '', cookieExpiration(-100));\n        };\n\n        var cookieExpiration = function (minutes) {\n            var exp = new Date();\n            exp.setTime(exp.getTime() + (minutes*60*1000));\n            return exp;\n        };\n\n        var getCookie = function (key) {\n            var name = key + '=';\n            var ca = document.cookie.split(';');\n            for (var i = 0; i < ca.length; i++) {\n                var c = ca[i];\n                while (c.charAt(0) == ' ') {\n                    c = c.substring(1);\n                }\n                if (c.indexOf(name) == 0) {\n                    return c.substring(name.length, c.length);\n                }\n            }\n            return '';\n        };\n\n        var setCookie = function (key, value, expirationDate) {\n            var cookie = key + '=' + value + '; '\n                + 'expires=' + expirationDate.toUTCString() + '; ';\n            document.cookie = cookie;\n        };\n    };\n\n    function createCallbackStorage() {\n        try {\n            return new LocalStorage();\n        } catch (err) {\n        }\n\n        return new CookieStorage();\n    }\n\n    function createLogger(fn) {\n        return function() {\n            if (kc.enableLogging) {\n                fn.apply(console, Array.prototype.slice.call(arguments));\n            }\n        };\n    }\n}\n\n// See: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\nfunction bytesToBase64(bytes) {\n    const binString = String.fromCodePoint(...bytes);\n    return btoa(binString);\n}\n\nexport { Keycloak as default };\n", "export class InvalidTokenError extends Error {\n}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n        return \"%\" + code;\n    }));\n}\nfunction base64UrlDecode(str) {\n    let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"base64 string is not of the correct length\");\n    }\n    try {\n        return b64DecodeUnicode(output);\n    }\n    catch (err) {\n        return atob(output);\n    }\n}\nexport function jwtDecode(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n    }\n    options || (options = {});\n    const pos = options.header === true ? 0 : 1;\n    const part = token.split(\".\")[pos];\n    if (typeof part !== \"string\") {\n        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n    }\n    let decoded;\n    try {\n        decoded = base64UrlDecode(part);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n    }\n    try {\n        return JSON.parse(decoded);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n    }\n}\n", "import * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, from, of, combineLatest } from 'rxjs';\nimport { map, mergeMap } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\nimport { CommonModule } from '@angular/common';\nvar KeycloakEventType;\n(function (KeycloakEventType) {\n  KeycloakEventType[KeycloakEventType[\"OnAuthError\"] = 0] = \"OnAuthError\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthLogout\"] = 1] = \"OnAuthLogout\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthRefreshError\"] = 2] = \"OnAuthRefreshError\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthRefreshSuccess\"] = 3] = \"OnAuthRefreshSuccess\";\n  KeycloakEventType[KeycloakEventType[\"OnAuthSuccess\"] = 4] = \"OnAuthSuccess\";\n  KeycloakEventType[KeycloakEventType[\"OnReady\"] = 5] = \"OnReady\";\n  KeycloakEventType[KeycloakEventType[\"OnTokenExpired\"] = 6] = \"OnTokenExpired\";\n  KeycloakEventType[KeycloakEventType[\"OnActionUpdate\"] = 7] = \"OnActionUpdate\";\n})(KeycloakEventType || (KeycloakEventType = {}));\nclass KeycloakAuthGuard {\n  constructor(router, keycloakAngular) {\n    this.router = router;\n    this.keycloakAngular = keycloakAngular;\n  }\n  async canActivate(route, state) {\n    try {\n      this.authenticated = await this.keycloakAngular.isLoggedIn();\n      this.roles = await this.keycloakAngular.getUserRoles(true);\n      return await this.isAccessAllowed(route, state);\n    } catch (error) {\n      throw new Error('An error happened during access validation. Details:' + error);\n    }\n  }\n}\nclass KeycloakService {\n  constructor() {\n    this._keycloakEvents$ = new Subject();\n  }\n  bindsKeycloakEvents() {\n    this._instance.onAuthError = errorData => {\n      this._keycloakEvents$.next({\n        args: errorData,\n        type: KeycloakEventType.OnAuthError\n      });\n    };\n    this._instance.onAuthLogout = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthLogout\n      });\n    };\n    this._instance.onAuthRefreshSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthRefreshSuccess\n      });\n    };\n    this._instance.onAuthRefreshError = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthRefreshError\n      });\n    };\n    this._instance.onAuthSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnAuthSuccess\n      });\n    };\n    this._instance.onTokenExpired = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventType.OnTokenExpired\n      });\n    };\n    this._instance.onActionUpdate = state => {\n      this._keycloakEvents$.next({\n        args: state,\n        type: KeycloakEventType.OnActionUpdate\n      });\n    };\n    this._instance.onReady = authenticated => {\n      this._keycloakEvents$.next({\n        args: authenticated,\n        type: KeycloakEventType.OnReady\n      });\n    };\n  }\n  loadExcludedUrls(bearerExcludedUrls) {\n    const excludedUrls = [];\n    for (const item of bearerExcludedUrls) {\n      let excludedUrl;\n      if (typeof item === 'string') {\n        excludedUrl = {\n          urlPattern: new RegExp(item, 'i'),\n          httpMethods: []\n        };\n      } else {\n        excludedUrl = {\n          urlPattern: new RegExp(item.url, 'i'),\n          httpMethods: item.httpMethods\n        };\n      }\n      excludedUrls.push(excludedUrl);\n    }\n    return excludedUrls;\n  }\n  initServiceValues({\n    enableBearerInterceptor = true,\n    loadUserProfileAtStartUp = false,\n    bearerExcludedUrls = [],\n    authorizationHeaderName = 'Authorization',\n    bearerPrefix = 'Bearer',\n    initOptions,\n    updateMinValidity = 20,\n    shouldAddToken = () => true,\n    shouldUpdateToken = () => true\n  }) {\n    this._enableBearerInterceptor = enableBearerInterceptor;\n    this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n    this._authorizationHeaderName = authorizationHeaderName;\n    this._bearerPrefix = bearerPrefix.trim().concat(' ');\n    this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n    this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n    this._updateMinValidity = updateMinValidity;\n    this.shouldAddToken = shouldAddToken;\n    this.shouldUpdateToken = shouldUpdateToken;\n  }\n  async init(options = {}) {\n    this.initServiceValues(options);\n    const {\n      config,\n      initOptions\n    } = options;\n    this._instance = new Keycloak(config);\n    this.bindsKeycloakEvents();\n    const authenticated = await this._instance.init(initOptions);\n    if (authenticated && this._loadUserProfileAtStartUp) {\n      await this.loadUserProfile();\n    }\n    return authenticated;\n  }\n  async login(options = {}) {\n    await this._instance.login(options);\n    if (this._loadUserProfileAtStartUp) {\n      await this.loadUserProfile();\n    }\n  }\n  async logout(redirectUri) {\n    const options = {\n      redirectUri\n    };\n    await this._instance.logout(options);\n    this._userProfile = undefined;\n  }\n  async register(options = {\n    action: 'register'\n  }) {\n    await this._instance.register(options);\n  }\n  isUserInRole(role, resource) {\n    let hasRole;\n    hasRole = this._instance.hasResourceRole(role, resource);\n    if (!hasRole) {\n      hasRole = this._instance.hasRealmRole(role);\n    }\n    return hasRole;\n  }\n  getUserRoles(realmRoles = true, resource) {\n    let roles = [];\n    if (this._instance.resourceAccess) {\n      Object.keys(this._instance.resourceAccess).forEach(key => {\n        if (resource && resource !== key) {\n          return;\n        }\n        const resourceAccess = this._instance.resourceAccess[key];\n        const clientRoles = resourceAccess['roles'] || [];\n        roles = roles.concat(clientRoles);\n      });\n    }\n    if (realmRoles && this._instance.realmAccess) {\n      const realmRoles = this._instance.realmAccess['roles'] || [];\n      roles.push(...realmRoles);\n    }\n    return roles;\n  }\n  isLoggedIn() {\n    if (!this._instance) {\n      return false;\n    }\n    return this._instance.authenticated;\n  }\n  isTokenExpired(minValidity = 0) {\n    return this._instance.isTokenExpired(minValidity);\n  }\n  async updateToken(minValidity = this._updateMinValidity) {\n    if (this._silentRefresh) {\n      if (this.isTokenExpired()) {\n        throw new Error('Failed to refresh the token, or the session is expired');\n      }\n      return true;\n    }\n    if (!this._instance) {\n      throw new Error('Keycloak Angular library is not initialized.');\n    }\n    try {\n      return await this._instance.updateToken(minValidity);\n    } catch (error) {\n      return false;\n    }\n  }\n  async loadUserProfile(forceReload = false) {\n    if (this._userProfile && !forceReload) {\n      return this._userProfile;\n    }\n    if (!this._instance.authenticated) {\n      throw new Error('The user profile was not loaded as the user is not logged in.');\n    }\n    return this._userProfile = await this._instance.loadUserProfile();\n  }\n  async getToken() {\n    return this._instance.token;\n  }\n  getUsername() {\n    if (!this._userProfile) {\n      throw new Error('User not logged in or user profile was not loaded.');\n    }\n    return this._userProfile.username;\n  }\n  clearToken() {\n    this._instance.clearToken();\n  }\n  addTokenToHeader(headers = new HttpHeaders()) {\n    return from(this.getToken()).pipe(map(token => token ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token) : headers));\n  }\n  getKeycloakInstance() {\n    return this._instance;\n  }\n  get excludedUrls() {\n    return this._excludedUrls;\n  }\n  get enableBearerInterceptor() {\n    return this._enableBearerInterceptor;\n  }\n  get keycloakEvents$() {\n    return this._keycloakEvents$;\n  }\n  static {\n    this.ɵfac = function KeycloakService_Factory(t) {\n      return new (t || KeycloakService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakService,\n      factory: KeycloakService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass KeycloakBearerInterceptor {\n  constructor(keycloak) {\n    this.keycloak = keycloak;\n  }\n  async conditionallyUpdateToken(req) {\n    if (this.keycloak.shouldUpdateToken(req)) {\n      return await this.keycloak.updateToken();\n    }\n    return true;\n  }\n  isUrlExcluded({\n    method,\n    url\n  }, {\n    urlPattern,\n    httpMethods\n  }) {\n    const httpTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n    const urlTest = urlPattern.test(url);\n    return httpTest && urlTest;\n  }\n  intercept(req, next) {\n    const {\n      enableBearerInterceptor,\n      excludedUrls\n    } = this.keycloak;\n    if (!enableBearerInterceptor) {\n      return next.handle(req);\n    }\n    const shallPass = !this.keycloak.shouldAddToken(req) || excludedUrls.findIndex(item => this.isUrlExcluded(req, item)) > -1;\n    if (shallPass) {\n      return next.handle(req);\n    }\n    return combineLatest([from(this.conditionallyUpdateToken(req)), of(this.keycloak.isLoggedIn())]).pipe(mergeMap(([_, isLoggedIn]) => isLoggedIn ? this.handleRequestWithTokenHeader(req, next) : next.handle(req)));\n  }\n  handleRequestWithTokenHeader(req, next) {\n    return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap(headersWithBearer => {\n      const kcReq = req.clone({\n        headers: headersWithBearer\n      });\n      return next.handle(kcReq);\n    }));\n  }\n  static {\n    this.ɵfac = function KeycloakBearerInterceptor_Factory(t) {\n      return new (t || KeycloakBearerInterceptor)(i0.ɵɵinject(KeycloakService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakBearerInterceptor,\n      factory: KeycloakBearerInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakBearerInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: KeycloakService\n  }], null);\n})();\nclass CoreModule {\n  static {\n    this.ɵfac = function CoreModule_Factory(t) {\n      return new (t || CoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CoreModule,\n      imports: [CommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CoreModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }]\n    }]\n  }], null, null);\n})();\nclass KeycloakAngularModule {\n  static {\n    this.ɵfac = function KeycloakAngularModule_Factory(t) {\n      return new (t || KeycloakAngularModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: KeycloakAngularModule,\n      imports: [CoreModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakAngularModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule]\n    }]\n  }], null, null);\n})();\nexport { CoreModule, KeycloakAngularModule, KeycloakAuthGuard, KeycloakBearerInterceptor, KeycloakEventType, KeycloakService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,qIAAqI;AAAA,QACjP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,qIAAqI;AAAA,QACjP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AASA,KAAC,WAAY;AACX;AAEA,UAAI,QAAQ;AACZ,UAAI,SAAS,OAAO,WAAW;AAC/B,UAAI,OAAO,SAAS,SAAS,CAAC;AAC9B,UAAI,KAAK,qBAAqB;AAC5B,iBAAS;AAAA,MACX;AACA,UAAI,aAAa,CAAC,UAAU,OAAO,SAAS;AAC5C,UAAI,UAAU,CAAC,KAAK,wBAAwB,OAAO,YAAY,YAAY,QAAQ,YAAY,QAAQ,SAAS,QAAQ,QAAQ,QAAQ;AACxI,UAAI,SAAS;AACX,eAAO;AAAA,MACT,WAAW,YAAY;AACrB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,CAAC,KAAK,0BAA0B,OAAO,WAAW,YAAY,OAAO;AACrF,UAAI,MAAM,OAAO,WAAW,cAAc,OAAO;AACjD,UAAI,eAAe,CAAC,KAAK,6BAA6B,OAAO,gBAAgB;AAC7E,UAAI,YAAY,mBAAmB,MAAM,EAAE;AAC3C,UAAI,QAAQ,CAAC,aAAa,SAAS,OAAO,GAAG;AAC7C,UAAI,QAAQ,CAAC,IAAI,IAAI,GAAG,CAAC;AACzB,UAAI,IAAI;AAAA,QACN;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,MACtF;AACA,UAAI,eAAe,CAAC,OAAO,SAAS,UAAU,aAAa;AAE3D,UAAI,SAAS,CAAC;AAEd,UAAI,KAAK,wBAAwB,CAAC,MAAM,SAAS;AAC/C,cAAM,UAAU,SAAU,KAAK;AAC7B,iBAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,QACjD;AAAA,MACF;AAEA,UAAI,iBAAiB,KAAK,qCAAqC,CAAC,YAAY,SAAS;AACnF,oBAAY,SAAS,SAAU,KAAK;AAClC,iBAAO,OAAO,QAAQ,YAAY,IAAI,UAAU,IAAI,OAAO,gBAAgB;AAAA,QAC7E;AAAA,MACF;AAEA,UAAI,qBAAqB,SAAU,YAAY,OAAO;AACpD,eAAO,SAAU,SAAS;AACxB,iBAAO,IAAI,OAAO,OAAO,IAAI,EAAE,OAAO,OAAO,EAAE,UAAU,EAAE;AAAA,QAC7D;AAAA,MACF;AAEA,UAAI,eAAe,SAAU,OAAO;AAClC,YAAI,SAAS,mBAAmB,OAAO,KAAK;AAC5C,YAAI,SAAS;AACX,mBAAS,SAAS,QAAQ,KAAK;AAAA,QACjC;AACA,eAAO,SAAS,WAAY;AAC1B,iBAAO,IAAI,OAAO,KAAK;AAAA,QACzB;AACA,eAAO,SAAS,SAAU,SAAS;AACjC,iBAAO,OAAO,OAAO,EAAE,OAAO,OAAO;AAAA,QACvC;AACA,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,cAAI,OAAO,aAAa,CAAC;AACzB,iBAAO,IAAI,IAAI,mBAAmB,MAAM,KAAK;AAAA,QAC/C;AACA,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,SAAU,QAAQ,OAAO;AACtC,YAAI,SAAS;AACb,YAAI,SAAS,iBAAkB;AAC/B,YAAI,YAAY,QAAQ,WAAW;AACnC,YAAI;AACJ,YAAI,OAAO,QAAQ,CAAC,KAAK,0BAA0B;AACjD,uBAAa,OAAO;AAAA,QACtB,OAAO;AACL,uBAAa,SAAU,SAAS;AAC9B,mBAAO,IAAI,OAAO,OAAO;AAAA,UAC3B;AAAA,QACF;AACA,YAAI,aAAa,SAAU,SAAS;AAClC,cAAI,OAAO,YAAY,UAAU;AAC/B,mBAAO,OAAO,WAAW,SAAS,EAAE,OAAO,SAAS,MAAM,EAAE,OAAO,KAAK;AAAA,UAC1E,OAAO;AACL,gBAAI,YAAY,QAAQ,YAAY,QAAW;AAC7C,oBAAM,IAAI,MAAM,KAAK;AAAA,YACvB,WAAW,QAAQ,gBAAgB,aAAa;AAC9C,wBAAU,IAAI,WAAW,OAAO;AAAA,YAClC;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,OAAO,KAAK,YAAY,OAAO,OAAO,KACtD,QAAQ,gBAAgB,QAAQ;AAChC,mBAAO,OAAO,WAAW,SAAS,EAAE,OAAO,WAAW,OAAO,CAAC,EAAE,OAAO,KAAK;AAAA,UAC9E,OAAO;AACL,mBAAO,OAAO,OAAO;AAAA,UACvB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,UAAI,yBAAyB,SAAU,YAAY,OAAO;AACxD,eAAO,SAAU,KAAK,SAAS;AAC7B,iBAAO,IAAI,WAAW,KAAK,OAAO,IAAI,EAAE,OAAO,OAAO,EAAE,UAAU,EAAE;AAAA,QACtE;AAAA,MACF;AAEA,UAAI,mBAAmB,SAAU,OAAO;AACtC,YAAI,SAAS,uBAAuB,OAAO,KAAK;AAChD,eAAO,SAAS,SAAU,KAAK;AAC7B,iBAAO,IAAI,WAAW,KAAK,KAAK;AAAA,QAClC;AACA,eAAO,SAAS,SAAU,KAAK,SAAS;AACtC,iBAAO,OAAO,OAAO,GAAG,EAAE,OAAO,OAAO;AAAA,QAC1C;AACA,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,cAAI,OAAO,aAAa,CAAC;AACzB,iBAAO,IAAI,IAAI,uBAAuB,MAAM,KAAK;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAEA,eAAS,OAAO,OAAO,cAAc;AACnC,YAAI,cAAc;AAChB,iBAAO,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IACvD,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAC5C,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAC9C,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI;AACtD,eAAK,SAAS;AAAA,QAChB,OAAO;AACL,eAAK,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAClE;AAEA,YAAI,OAAO;AACT,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAAA,QACZ,OAAO;AACL,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAAA,QACZ;AAEA,aAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS;AACrD,aAAK,YAAY,KAAK,SAAS;AAC/B,aAAK,QAAQ;AACb,aAAK,QAAQ;AAAA,MACf;AAEA,aAAO,UAAU,SAAS,SAAU,SAAS;AAC3C,YAAI,KAAK,WAAW;AAClB;AAAA,QACF;AACA,YAAI,WAAW,OAAO,OAAO;AAC7B,YAAI,SAAS,UAAU;AACrB,cAAI,SAAS,UAAU;AACrB,gBAAI,YAAY,MAAM;AACpB,oBAAM,IAAI,MAAM,KAAK;AAAA,YACvB,WAAW,gBAAgB,QAAQ,gBAAgB,aAAa;AAC9D,wBAAU,IAAI,WAAW,OAAO;AAAA,YAClC,WAAW,CAAC,MAAM,QAAQ,OAAO,GAAG;AAClC,kBAAI,CAAC,gBAAgB,CAAC,YAAY,OAAO,OAAO,GAAG;AACjD,sBAAM,IAAI,MAAM,KAAK;AAAA,cACvB;AAAA,YACF;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,MAAM,KAAK;AAAA,UACvB;AACA,sBAAY;AAAA,QACd;AACA,YAAI,MAAM,QAAQ,GAAG,GAAG,SAAS,QAAQ,QAAQA,UAAS,KAAK;AAC/D,eAAO,QAAQ,QAAQ;AACrB,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS;AACd,YAAAA,QAAO,CAAC,IAAI,KAAK;AACjB,iBAAK,QAAQA,QAAO,EAAE,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IACxDA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAC5CA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAC9CA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAI;AAAA,UACxD;AAEA,cAAI,WAAW;AACb,iBAAK,IAAI,KAAK,OAAO,QAAQ,UAAU,IAAI,IAAI,EAAE,OAAO;AACtD,cAAAA,QAAO,MAAM,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,MAAM,CAAC;AAAA,YACpD;AAAA,UACF,OAAO;AACL,iBAAK,IAAI,KAAK,OAAO,QAAQ,UAAU,IAAI,IAAI,EAAE,OAAO;AACtD,qBAAO,QAAQ,WAAW,KAAK;AAC/B,kBAAI,OAAO,KAAM;AACf,gBAAAA,QAAO,MAAM,CAAC,KAAK,QAAQ,MAAM,MAAM,CAAC;AAAA,cAC1C,WAAW,OAAO,MAAO;AACvB,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,SAAS,MAAO,MAAM,MAAM,CAAC;AACzD,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,OAAO,OAAU,MAAM,MAAM,CAAC;AAAA,cAC5D,WAAW,OAAO,SAAU,QAAQ,OAAQ;AAC1C,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,SAAS,OAAQ,MAAM,MAAM,CAAC;AAC1D,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAS,SAAS,IAAK,OAAU,MAAM,MAAM,CAAC;AAClE,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,OAAO,OAAU,MAAM,MAAM,CAAC;AAAA,cAC5D,OAAO;AACL,uBAAO,UAAa,OAAO,SAAU,KAAO,QAAQ,WAAW,EAAE,KAAK,IAAI;AAC1E,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,SAAS,OAAQ,MAAM,MAAM,CAAC;AAC1D,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAS,SAAS,KAAM,OAAU,MAAM,MAAM,CAAC;AACnE,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAS,SAAS,IAAK,OAAU,MAAM,MAAM,CAAC;AAClE,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,OAAO,OAAU,MAAM,MAAM,CAAC;AAAA,cAC5D;AAAA,YACF;AAAA,UACF;AAEA,eAAK,gBAAgB;AACrB,eAAK,SAAS,IAAI,KAAK;AACvB,cAAI,KAAK,IAAI;AACX,iBAAK,QAAQA,QAAO,EAAE;AACtB,iBAAK,QAAQ,IAAI;AACjB,iBAAK,KAAK;AACV,iBAAK,SAAS;AAAA,UAChB,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AACA,YAAI,KAAK,QAAQ,YAAY;AAC3B,eAAK,UAAU,KAAK,QAAQ,cAAc;AAC1C,eAAK,QAAQ,KAAK,QAAQ;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,WAAW,WAAY;AACtC,YAAI,KAAK,WAAW;AAClB;AAAA,QACF;AACA,aAAK,YAAY;AACjB,YAAIA,UAAS,KAAK,QAAQ,IAAI,KAAK;AACnC,QAAAA,QAAO,EAAE,IAAI,KAAK;AAClB,QAAAA,QAAO,MAAM,CAAC,KAAK,MAAM,IAAI,CAAC;AAC9B,aAAK,QAAQA,QAAO,EAAE;AACtB,YAAI,KAAK,IAAI;AACX,cAAI,CAAC,KAAK,QAAQ;AAChB,iBAAK,KAAK;AAAA,UACZ;AACA,UAAAA,QAAO,CAAC,IAAI,KAAK;AACjB,UAAAA,QAAO,EAAE,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAC3CA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAC5CA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAC9CA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAI;AAAA,QACxD;AACA,QAAAA,QAAO,EAAE,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU;AAC/C,QAAAA,QAAO,EAAE,IAAI,KAAK,SAAS;AAC3B,aAAK,KAAK;AAAA,MACZ;AAEA,aAAO,UAAU,OAAO,WAAY;AAClC,YAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IACzF,IAAI,KAAK,IAAIA,UAAS,KAAK,QAAQ,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAE7E,aAAK,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAExB,eAAKA,QAAO,IAAI,EAAE;AAClB,gBAAO,OAAO,IAAM,MAAM,OAAS,OAAO,KAAO,MAAM,MAAQ,OAAO;AACtE,eAAKA,QAAO,IAAI,CAAC;AACjB,gBAAO,OAAO,KAAO,MAAM,OAAS,OAAO,KAAO,MAAM,MAAQ,OAAO;AACvE,UAAAA,QAAO,CAAC,IAAIA,QAAO,IAAI,EAAE,IAAI,KAAKA,QAAO,IAAI,CAAC,IAAI,MAAM;AAAA,QAC1D;AAEA,aAAK,IAAI;AACT,aAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC1B,cAAI,KAAK,OAAO;AACd,gBAAI,KAAK,OAAO;AACd,mBAAK;AACL,mBAAKA,QAAO,CAAC,IAAI;AACjB,kBAAI,KAAK,aAAa;AACtB,kBAAI,KAAK,YAAY;AAAA,YACvB,OAAO;AACL,mBAAK;AACL,mBAAKA,QAAO,CAAC,IAAI;AACjB,kBAAI,KAAK,cAAc;AACvB,kBAAI,KAAK,aAAa;AAAA,YACxB;AACA,iBAAK,QAAQ;AAAA,UACf,OAAO;AACL,kBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,kBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,iBAAK,IAAI;AACT,kBAAM,KAAM,IAAI,IAAK;AACrB,iBAAM,IAAI,IAAM,CAAC,IAAI;AACrB,iBAAK,IAAI,KAAK,KAAK,EAAE,CAAC,IAAIA,QAAO,CAAC;AAClC,iBAAK,KAAK;AACV,gBAAI,IAAI,MAAM;AACd,gBAAI,KAAK,MAAM;AAAA,UACjB;AACA,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,eAAK,IAAI;AACT,gBAAM,KAAM,IAAI,IAAK;AACrB,eAAM,IAAI,IAAM,CAAC,IAAI;AACrB,eAAK,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,IAAIA,QAAO,IAAI,CAAC;AAC1C,eAAK,KAAK;AACV,cAAI,IAAI,MAAM;AACd,cAAI,KAAK,MAAM;AACf,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,eAAK,IAAI;AACT,gBAAM,KAAM,IAAI,IAAK;AACrB,eAAM,IAAI,IAAM,CAAC,IAAI;AACrB,eAAK,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,IAAIA,QAAO,IAAI,CAAC;AAC1C,eAAK,KAAK;AACV,cAAI,IAAI,MAAM;AACd,cAAI,KAAK,MAAM;AACf,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,eAAK,IAAI;AACT,gBAAM,KAAM,IAAI,IAAK;AACrB,eAAM,IAAI,IAAM,CAAC,IAAI;AACrB,eAAK,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,IAAIA,QAAO,IAAI,CAAC;AAC1C,eAAK,KAAK;AACV,cAAI,IAAI,MAAM;AACd,cAAI,KAAK,MAAM;AACf,eAAK,sBAAsB;AAAA,QAC7B;AAEA,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AAAA,MAC3B;AAEA,aAAO,UAAU,MAAM,WAAY;AACjC,aAAK,SAAS;AAEd,YAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAClF,KAAK,KAAK,IAAI,KAAK,KAAK;AAE1B,YAAI,MAAM,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IACpE,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI;AACpD,YAAI,CAAC,KAAK,OAAO;AACf,iBAAO,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IACjE,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI;AAAA,QACtD;AACA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,WAAW,OAAO,UAAU;AAE7C,aAAO,UAAU,SAAS,WAAY;AACpC,aAAK,SAAS;AAEd,YAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAClF,KAAK,KAAK,IAAI,KAAK,KAAK;AAE1B,YAAI,MAAM;AAAA,UACP,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,QAClE;AACA,YAAI,CAAC,KAAK,OAAO;AACf,cAAI,KAAM,OAAO,KAAM,KAAO,OAAO,KAAM,KAAO,OAAO,IAAK,KAAM,KAAK,GAAI;AAAA,QAC/E;AACA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,QAAQ,OAAO,UAAU;AAE1C,aAAO,UAAU,cAAc,WAAY;AACzC,aAAK,SAAS;AAEd,YAAI,SAAS,IAAI,YAAY,KAAK,QAAQ,KAAK,EAAE;AACjD,YAAI,WAAW,IAAI,SAAS,MAAM;AAClC,iBAAS,UAAU,GAAG,KAAK,EAAE;AAC7B,iBAAS,UAAU,GAAG,KAAK,EAAE;AAC7B,iBAAS,UAAU,GAAG,KAAK,EAAE;AAC7B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,YAAI,CAAC,KAAK,OAAO;AACf,mBAAS,UAAU,IAAI,KAAK,EAAE;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,KAAK,OAAO,cAAc;AAC5C,YAAI,GAAG,OAAO,OAAO;AACrB,YAAI,SAAS,UAAU;AACrB,cAAI,QAAQ,CAAC,GAAG,SAAS,IAAI,QAAQ,QAAQ,GAAG;AAChD,eAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,mBAAO,IAAI,WAAW,CAAC;AACvB,gBAAI,OAAO,KAAM;AACf,oBAAM,OAAO,IAAI;AAAA,YACnB,WAAW,OAAO,MAAO;AACvB,oBAAM,OAAO,IAAK,MAAQ,SAAS;AACnC,oBAAM,OAAO,IAAK,MAAQ,OAAO;AAAA,YACnC,WAAW,OAAO,SAAU,QAAQ,OAAQ;AAC1C,oBAAM,OAAO,IAAK,MAAQ,SAAS;AACnC,oBAAM,OAAO,IAAK,MAAS,SAAS,IAAK;AACzC,oBAAM,OAAO,IAAK,MAAQ,OAAO;AAAA,YACnC,OAAO;AACL,qBAAO,UAAa,OAAO,SAAU,KAAO,IAAI,WAAW,EAAE,CAAC,IAAI;AAClE,oBAAM,OAAO,IAAK,MAAQ,SAAS;AACnC,oBAAM,OAAO,IAAK,MAAS,SAAS,KAAM;AAC1C,oBAAM,OAAO,IAAK,MAAS,SAAS,IAAK;AACzC,oBAAM,OAAO,IAAK,MAAQ,OAAO;AAAA,YACnC;AAAA,UACF;AACA,gBAAM;AAAA,QACR,OAAO;AACL,cAAI,SAAS,UAAU;AACrB,gBAAI,QAAQ,MAAM;AAChB,oBAAM,IAAI,MAAM,KAAK;AAAA,YACvB,WAAW,gBAAgB,IAAI,gBAAgB,aAAa;AAC1D,oBAAM,IAAI,WAAW,GAAG;AAAA,YAC1B,WAAW,CAAC,MAAM,QAAQ,GAAG,GAAG;AAC9B,kBAAI,CAAC,gBAAgB,CAAC,YAAY,OAAO,GAAG,GAAG;AAC7C,sBAAM,IAAI,MAAM,KAAK;AAAA,cACvB;AAAA,YACF;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,MAAM,KAAK;AAAA,UACvB;AAAA,QACF;AAEA,YAAI,IAAI,SAAS,IAAI;AACnB,gBAAO,IAAI,OAAO,OAAO,IAAI,EAAG,OAAO,GAAG,EAAE,MAAM;AAAA,QACpD;AAEA,YAAI,UAAU,CAAC,GAAG,UAAU,CAAC;AAC7B,aAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACvB,cAAI,IAAI,IAAI,CAAC,KAAK;AAClB,kBAAQ,CAAC,IAAI,KAAO;AACpB,kBAAQ,CAAC,IAAI,KAAO;AAAA,QACtB;AAEA,eAAO,KAAK,MAAM,OAAO,YAAY;AAErC,aAAK,OAAO,OAAO;AACnB,aAAK,UAAU;AACf,aAAK,QAAQ;AACb,aAAK,eAAe;AAAA,MACtB;AACA,iBAAW,YAAY,IAAI,OAAO;AAElC,iBAAW,UAAU,WAAW,WAAY;AAC1C,eAAO,UAAU,SAAS,KAAK,IAAI;AACnC,YAAI,KAAK,OAAO;AACd,eAAK,QAAQ;AACb,cAAI,YAAY,KAAK,MAAM;AAC3B,iBAAO,KAAK,MAAM,KAAK,OAAO,KAAK,YAAY;AAC/C,eAAK,OAAO,KAAK,OAAO;AACxB,eAAK,OAAO,SAAS;AACrB,iBAAO,UAAU,SAAS,KAAK,IAAI;AAAA,QACrC;AAAA,MACF;AAEA,UAAIC,WAAU,aAAa;AAC3B,MAAAA,SAAQ,SAASA;AACjB,MAAAA,SAAQ,SAAS,aAAa,IAAI;AAClC,MAAAA,SAAQ,OAAO,OAAO,iBAAiB;AACvC,MAAAA,SAAQ,OAAO,OAAO,iBAAiB,IAAI;AAE3C,UAAI,WAAW;AACb,eAAO,UAAUA;AAAA,MACnB,OAAO;AACL,aAAK,SAASA,SAAQ;AACtB,aAAK,SAASA,SAAQ;AACtB,YAAI,KAAK;AACP,iBAAO,WAAY;AACjB,mBAAOA;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,GAAG;AAAA;AAAA;;;AC7gBH,uBAAmB;;;ACAZ,IAAM,oBAAN,cAAgC,MAAM;AAC7C;AACA,kBAAkB,UAAU,OAAO;AACnC,SAAS,iBAAiB,KAAK;AAC3B,SAAO,mBAAmB,KAAK,GAAG,EAAE,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAC1D,QAAI,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AACpD,QAAI,KAAK,SAAS,GAAG;AACjB,aAAO,MAAM;AAAA,IACjB;AACA,WAAO,MAAM;AAAA,EACjB,CAAC,CAAC;AACN;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACrD,UAAQ,OAAO,SAAS,GAAG;AAAA,IACvB,KAAK;AACD;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ;AACI,YAAM,IAAI,MAAM,4CAA4C;AAAA,EACpE;AACA,MAAI;AACA,WAAO,iBAAiB,MAAM;AAAA,EAClC,SACO,KAAK;AACR,WAAO,KAAK,MAAM;AAAA,EACtB;AACJ;AACO,SAAS,UAAU,OAAO,SAAS;AACtC,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,IAAI,kBAAkB,2CAA2C;AAAA,EAC3E;AACA,cAAY,UAAU,CAAC;AACvB,QAAM,MAAM,QAAQ,WAAW,OAAO,IAAI;AAC1C,QAAM,OAAO,MAAM,MAAM,GAAG,EAAE,GAAG;AACjC,MAAI,OAAO,SAAS,UAAU;AAC1B,UAAM,IAAI,kBAAkB,0CAA0C,MAAM,CAAC,EAAE;AAAA,EACnF;AACA,MAAI;AACJ,MAAI;AACA,cAAU,gBAAgB,IAAI;AAAA,EAClC,SACO,GAAG;AACN,UAAM,IAAI,kBAAkB,qDAAqD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC7G;AACA,MAAI;AACA,WAAO,KAAK,MAAM,OAAO;AAAA,EAC7B,SACO,GAAG;AACN,UAAM,IAAI,kBAAkB,mDAAmD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC3G;AACJ;;;ADpCA,IAAI,OAAO,YAAY,aAAa;AAChC,QAAM,MAAM,+GAA+G;AAC/H;AAEA,SAAS,SAAU,QAAQ;AACvB,MAAI,EAAE,gBAAgB,WAAW;AAC7B,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC5E;AAEA,MAAI,KAAK;AACT,MAAI;AACJ,MAAI,eAAe,CAAC;AACpB,MAAI;AAEJ,MAAI,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,cAAc,CAAC;AAAA,IACf,UAAU;AAAA,EACd;AAEA,MAAI,UAAU,SAAS,qBAAqB,QAAQ;AACpD,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,SAAK,QAAQ,CAAC,EAAE,IAAI,QAAQ,aAAa,MAAM,MAAM,QAAQ,CAAC,EAAE,IAAI,QAAQ,iBAAiB,MAAM,OAAO,QAAQ,CAAC,EAAE,IAAI,QAAQ,UAAU,MAAM,IAAI;AACjJ,SAAG,gBAAgB,QAAQ,CAAC,EAAE,IAAI,UAAU,QAAQ,CAAC,EAAE,IAAI,QAAQ,UAAU,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,IACpG;AAAA,EACJ;AAEA,MAAI,WAAW;AACf,MAAI,UAAU,aAAa,QAAQ,IAAI;AACvC,MAAI,UAAU,aAAa,QAAQ,IAAI;AAEvC,KAAG,OAAO,SAAU,aAAa;AAC7B,QAAI,GAAG,eAAe;AAClB,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACzE;AAEA,OAAG,gBAAgB;AAEnB,OAAG,gBAAgB;AAEnB,sBAAkB,sBAAsB;AACxC,QAAI,WAAW,CAAC,WAAW,WAAW,gBAAgB;AAEtD,QAAI,eAAe,SAAS,QAAQ,YAAY,OAAO,IAAI,IAAI;AAC3D,gBAAU,YAAY,YAAY,OAAO;AAAA,IAC7C,WAAW,eAAe,OAAO,YAAY,YAAY,UAAU;AAC/D,gBAAU,YAAY;AAAA,IAC1B,OAAO;AACH,UAAI,OAAO,WAAW,OAAO,SAAS;AAClC,kBAAU,YAAY,SAAS;AAAA,MACnC,OAAO;AACH,kBAAU,YAAY;AAAA,MAC1B;AAAA,IACJ;AAEA,QAAI,aAAa;AACb,UAAI,OAAO,YAAY,aAAa,aAAa;AAC7C,mBAAW,YAAY;AAAA,MAC3B;AAEA,UAAI,OAAO,YAAY,qBAAqB,aAAa;AACrD,oBAAY,SAAS,YAAY;AAAA,MACrC;AAEA,UAAI,YAAY,0BAA0B;AACtC,oBAAY,WAAW,YAAY;AAAA,MACvC;AAEA,UAAI,YAAY,WAAW,kBAAkB;AACzC,WAAG,gBAAgB;AAAA,MACvB;AAEA,UAAI,YAAY,cAAc;AAC1B,YAAI,YAAY,iBAAiB,WAAW,YAAY,iBAAiB,YAAY;AACjF,aAAG,eAAe,YAAY;AAAA,QAClC,OAAO;AACH,gBAAM;AAAA,QACV;AAAA,MACJ;AAEA,UAAI,YAAY,MAAM;AAClB,gBAAQ,YAAY,MAAM;AAAA,UACtB,KAAK;AACD,eAAG,eAAe;AAClB;AAAA,UACJ,KAAK;AACD,eAAG,eAAe;AAClB;AAAA,UACJ,KAAK;AACD,eAAG,eAAe;AAClB;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,WAAG,OAAO,YAAY;AAAA,MAC1B;AAEA,UAAI,YAAY,YAAY,MAAM;AAC9B,WAAG,WAAW,YAAY;AAAA,MAC9B;AAEA,UAAG,YAAY,aAAa;AACxB,WAAG,cAAc,YAAY;AAAA,MACjC;AAEA,UAAI,YAAY,2BAA2B;AACvC,WAAG,4BAA4B,YAAY;AAAA,MAC/C;AAEA,UAAI,OAAO,YAAY,2BAA2B,WAAW;AACzD,WAAG,yBAAyB,YAAY;AAAA,MAC5C,OAAO;AACH,WAAG,yBAAyB;AAAA,MAChC;AAEA,UAAI,OAAO,YAAY,eAAe,aAAa;AAC/C,YAAI,YAAY,eAAe,UAAU,YAAY,eAAe,OAAO;AACvE,gBAAM,IAAI,UAAU,mEAAmE,YAAY,UAAU,GAAG;AAAA,QACpH;AAEA,WAAG,aAAa,YAAY;AAAA,MAChC,OAAO;AACH,WAAG,aAAa;AAAA,MACpB;AAEA,UAAI,OAAO,YAAY,kBAAkB,WAAW;AAChD,WAAG,gBAAgB,YAAY;AAAA,MACnC,OAAO;AACH,WAAG,gBAAgB;AAAA,MACvB;AAEA,UAAI,YAAY,iBAAiB,QAAQ;AACrC,WAAG,eAAe;AAAA,MACtB,OAAO;AACH,WAAG,eAAe;AAAA,MACtB;AAEA,UAAI,OAAO,YAAY,UAAU,UAAU;AACvC,WAAG,QAAQ,YAAY;AAAA,MAC3B;AAEA,UAAI,OAAO,YAAY,cAAc,UAAU;AAC3C,WAAG,YAAY,YAAY;AAAA,MAC/B;AAEA,UAAI,OAAO,YAAY,0BAA0B,YAAY,YAAY,wBAAwB,GAAG;AAChG,WAAG,wBAAwB,YAAY;AAAA,MAC3C,OAAO;AACH,WAAG,wBAAwB;AAAA,MAC/B;AAAA,IACJ;AAEA,QAAI,CAAC,GAAG,cAAc;AAClB,SAAG,eAAe;AAAA,IACtB;AACA,QAAI,CAAC,GAAG,cAAc;AAClB,SAAG,eAAe;AAClB,SAAG,OAAO;AAAA,IACd;AAEA,QAAI,UAAU,cAAc;AAE5B,QAAI,cAAc,cAAc;AAChC,gBAAY,QAAQ,KAAK,WAAW;AAChC,SAAG,WAAW,GAAG,QAAQ,GAAG,aAAa;AACzC,cAAQ,WAAW,GAAG,aAAa;AAAA,IACvC,CAAC,EAAE,MAAM,SAAS,OAAO;AACrB,cAAQ,SAAS,KAAK;AAAA,IAC1B,CAAC;AAED,QAAI,gBAAgB,WAAW;AAE/B,aAAS,SAAS;AACd,UAAI,UAAU,SAAS,QAAQ;AAC3B,YAAI,CAAC,QAAQ;AACT,kBAAQ,SAAS;AAAA,QACrB;AAEA,YAAI,eAAe,YAAY,QAAQ;AACnC,kBAAQ,SAAS,YAAY;AAAA,QACjC;AACA,WAAG,MAAM,OAAO,EAAE,KAAK,WAAY;AAC/B,sBAAY,WAAW;AAAA,QAC3B,CAAC,EAAE,MAAM,SAAU,OAAO;AACtB,sBAAY,SAAS,KAAK;AAAA,QAC9B,CAAC;AAAA,MACL;AAEA,UAAI,mBAAmB,WAAW;AAC9B,YAAI,OAAO,SAAS,cAAc,QAAQ;AAC1C,YAAI,MAAM,GAAG,eAAe,EAAC,QAAQ,QAAQ,aAAa,GAAG,0BAAyB,CAAC;AACvF,aAAK,aAAa,OAAO,GAAG;AAC5B,aAAK,aAAa,WAAW,yEAAyE;AACtG,aAAK,aAAa,SAAS,2BAA2B;AACtD,aAAK,MAAM,UAAU;AACrB,iBAAS,KAAK,YAAY,IAAI;AAE9B,YAAI,kBAAkB,SAAS,OAAO;AAClC,cAAI,MAAM,WAAW,OAAO,SAAS,UAAU,KAAK,kBAAkB,MAAM,QAAQ;AAChF;AAAA,UACJ;AAEA,cAAI,QAAQ,cAAc,MAAM,IAAI;AACpC,0BAAgB,OAAO,WAAW;AAElC,mBAAS,KAAK,YAAY,IAAI;AAC9B,iBAAO,oBAAoB,WAAW,eAAe;AAAA,QACzD;AAEA,eAAO,iBAAiB,WAAW,eAAe;AAAA,MACtD;AAEA,UAAI,UAAU,CAAC;AACf,cAAQ,YAAY,QAAQ;AAAA,QACxB,KAAK;AACD,cAAI,YAAY,QAAQ;AACpB,kCAAsB,EAAE,KAAK,WAAW;AACpC,+BAAiB,EAAE,KAAK,SAAU,WAAW;AACzC,oBAAI,CAAC,WAAW;AACZ,qBAAG,4BAA4B,iBAAiB,IAAI,QAAQ,KAAK;AAAA,gBACrE,OAAO;AACH,8BAAY,WAAW;AAAA,gBAC3B;AAAA,cACJ,CAAC,EAAE,MAAM,SAAU,OAAO;AACtB,4BAAY,SAAS,KAAK;AAAA,cAC9B,CAAC;AAAA,YACL,CAAC;AAAA,UACL,OAAO;AACH,eAAG,4BAA4B,iBAAiB,IAAI,QAAQ,KAAK;AAAA,UACrE;AACA;AAAA,QACJ,KAAK;AACD,kBAAQ,IAAI;AACZ;AAAA,QACJ;AACI,gBAAM;AAAA,MACd;AAAA,IACJ;AAEA,aAAS,cAAc;AACnB,UAAI,WAAW,cAAc,OAAO,SAAS,IAAI;AAEjD,UAAI,UAAU;AACV,eAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,MAAM,SAAS,MAAM;AAAA,MAC3E;AAEA,UAAI,YAAY,SAAS,OAAO;AAC5B,eAAO,sBAAsB,EAAE,KAAK,WAAW;AAC3C,0BAAgB,UAAU,WAAW;AAAA,QACzC,CAAC,EAAE,MAAM,SAAU,OAAO;AACtB,sBAAY,SAAS,KAAK;AAAA,QAC9B,CAAC;AAAA,MACL,WAAW,aAAa;AACpB,YAAI,YAAY,SAAS,YAAY,cAAc;AAC/C,mBAAS,YAAY,OAAO,YAAY,cAAc,YAAY,OAAO;AAEzE,cAAI,YAAY,QAAQ;AACpB,kCAAsB,EAAE,KAAK,WAAW;AACpC,+BAAiB,EAAE,KAAK,SAAU,WAAW;AACzC,oBAAI,WAAW;AACX,qBAAG,iBAAiB,GAAG,cAAc;AACrC,8BAAY,WAAW;AACvB,sCAAoB;AAAA,gBACxB,OAAO;AACH,8BAAY,WAAW;AAAA,gBAC3B;AAAA,cACJ,CAAC,EAAE,MAAM,SAAU,OAAO;AACtB,4BAAY,SAAS,KAAK;AAAA,cAC9B,CAAC;AAAA,YACL,CAAC;AAAA,UACL,OAAO;AACH,eAAG,YAAY,EAAE,EAAE,KAAK,WAAW;AAC/B,iBAAG,iBAAiB,GAAG,cAAc;AACrC,0BAAY,WAAW;AAAA,YAC3B,CAAC,EAAE,MAAM,SAAS,OAAO;AACrB,iBAAG,eAAe,GAAG,YAAY;AACjC,kBAAI,YAAY,QAAQ;AACpB,uBAAO;AAAA,cACX,OAAO;AACH,4BAAY,SAAS,KAAK;AAAA,cAC9B;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,WAAW,YAAY,QAAQ;AAC3B,iBAAO;AAAA,QACX,OAAO;AACH,sBAAY,WAAW;AAAA,QAC3B;AAAA,MACJ,OAAO;AACH,oBAAY,WAAW;AAAA,MAC3B;AAAA,IACJ;AAEA,aAAS,WAAW;AAChB,UAAIC,WAAU,cAAc;AAE5B,UAAI,kBAAkB,WAAY;AAC9B,YAAI,SAAS,eAAe,iBAAiB,SAAS,eAAe,YAAY;AAC7E,mBAAS,oBAAoB,oBAAoB,eAAe;AAChE,UAAAA,SAAQ,WAAW;AAAA,QACvB;AAAA,MACJ;AACA,eAAS,iBAAiB,oBAAoB,eAAe;AAE7D,sBAAgB;AAEhB,aAAOA,SAAQ;AAAA,IACnB;AAEA,kBAAc,KAAK,WAAY;AAC3B,eAAS,EACJ,KAAK,uBAAuB,EAC5B,KAAK,WAAW,EAChB,MAAM,SAAU,OAAO;AACpB,gBAAQ,SAAS,KAAK;AAAA,MAC1B,CAAC;AAAA,IACT,CAAC;AACD,kBAAc,MAAM,SAAU,OAAO;AACjC,cAAQ,SAAS,KAAK;AAAA,IAC1B,CAAC;AAED,WAAO,QAAQ;AAAA,EACnB;AAEA,KAAG,QAAQ,SAAU,SAAS;AAC1B,WAAO,QAAQ,MAAM,OAAO;AAAA,EAChC;AAEA,WAAS,mBAAmB,KAAK;AAE7B,QAAI,QAAQ;AACZ,QAAI,SAAS,OAAO,UAAU,OAAO;AACrC,QAAI,UAAU,OAAO,mBAAmB,OAAO,YAAY;AACvD,cAAQ,IAAI,WAAW,GAAG;AAC1B,aAAO,gBAAgB,KAAK;AAC5B,aAAO;AAAA,IACX;AAGA,YAAQ,IAAI,MAAM,GAAG;AACrB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,CAAC,IAAI,KAAK,MAAM,MAAM,KAAK,OAAO,CAAC;AAAA,IAC7C;AACA,WAAO;AAAA,EACX;AAEA,WAAS,qBAAqB,KAAK;AAC/B,WAAO,qBAAqB,KAAK,gEAAgE;AAAA,EACrG;AAEA,WAAS,qBAAqB,KAAK,UAAS;AACxC,QAAI,aAAa,mBAAmB,GAAG;AACvC,QAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,aAASC,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,YAAMA,EAAC,IAAI,SAAS,WAAW,WAAWA,EAAC,IAAI,SAAS,MAAM;AAAA,IAClE;AACA,WAAO,OAAO,aAAa,MAAM,MAAM,KAAK;AAAA,EAChD;AAEA,WAAS,sBAAsB,YAAY,cAAc;AACrD,QAAI,eAAe,QAAQ;AACvB,YAAM,IAAI,UAAU,4DAA4D,UAAU,IAAI;AAAA,IAClG;AAGA,UAAM,YAAY,IAAI,WAAW,iBAAAC,QAAO,YAAY,YAAY,CAAC;AACjE,UAAM,cAAc,cAAc,SAAS,EACtC,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,EAAE;AAEtB,WAAO;AAAA,EACX;AAEA,WAAS,qBAAqB,cAAa;AACvC,QAAI,SAAS;AAAA,MACT,UAAU;AAAA,QACN,KAAK;AAAA,MACT;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,MAAM;AAAA,EAChC;AAEA,KAAG,iBAAiB,SAAS,SAAS;AAClC,QAAI,QAAQ,WAAW;AACvB,QAAI,QAAQ,WAAW;AAEvB,QAAI,cAAc,QAAQ,YAAY,OAAO;AAE7C,QAAI,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,aAAa,mBAAmB,WAAW;AAAA,IAC/C;AAEA,QAAI,WAAW,QAAQ,QAAQ;AAC3B,oBAAc,SAAS,QAAQ;AAAA,IACnC;AAEA,QAAI;AACJ,QAAI,WAAW,QAAQ,UAAU,YAAY;AACzC,gBAAU,GAAG,UAAU,SAAS;AAAA,IACpC,OAAO;AACH,gBAAU,GAAG,UAAU,UAAU;AAAA,IACrC;AAEA,QAAI,QAAQ,WAAW,QAAQ,SAAS,GAAG;AAC3C,QAAI,CAAC,OAAO;AAER,cAAQ;AAAA,IACZ,WAAW,MAAM,QAAQ,QAAQ,MAAM,IAAI;AAEvC,cAAQ,YAAY;AAAA,IACxB;AAEA,QAAI,MAAM,UACJ,gBAAgB,mBAAmB,GAAG,QAAQ,IAC9C,mBAAmB,mBAAmB,WAAW,IACjD,YAAY,mBAAmB,KAAK,IACpC,oBAAoB,mBAAmB,GAAG,YAAY,IACtD,oBAAoB,mBAAmB,GAAG,YAAY,IACtD,YAAY,mBAAmB,KAAK;AAC1C,QAAI,UAAU;AACV,YAAM,MAAM,YAAY,mBAAmB,KAAK;AAAA,IACpD;AAEA,QAAI,WAAW,QAAQ,QAAQ;AAC3B,aAAO,aAAa,mBAAmB,QAAQ,MAAM;AAAA,IACzD;AAEA,QAAI,WAAW,QAAQ,QAAQ;AAC3B,aAAO,cAAc,mBAAmB,QAAQ,MAAM;AAAA,IAC1D;AAEA,QAAI,WAAW,QAAQ,WAAW;AAC9B,aAAO,iBAAiB,mBAAmB,QAAQ,SAAS;AAAA,IAChE;AAEA,QAAI,WAAW,QAAQ,SAAS;AAC5B,aAAO,kBAAkB,mBAAmB,QAAQ,OAAO;AAAA,IAC/D;AAEA,QAAI,WAAW,QAAQ,UAAU,QAAQ,UAAU,YAAY;AAC3D,aAAO,gBAAgB,mBAAmB,QAAQ,MAAM;AAAA,IAC5D;AAEA,QAAI,WAAW,QAAQ,QAAQ;AAC3B,aAAO,iBAAiB,mBAAmB,QAAQ,MAAM;AAAA,IAC7D;AAEA,QAAI,WAAW,QAAQ,KAAK;AACxB,UAAI,kBAAkB,qBAAqB,QAAQ,GAAG;AACtD,aAAO,aAAa,mBAAmB,eAAe;AAAA,IAC1D;AAEA,QAAK,WAAW,QAAQ,aAAc,GAAG,WAAW;AAChD,aAAO,iBAAiB,mBAAmB,QAAQ,aAAa,GAAG,SAAS;AAAA,IAChF;AAEA,QAAI,GAAG,YAAY;AACf,UAAI,eAAe,qBAAqB,EAAE;AAC1C,oBAAc,mBAAmB;AACjC,UAAI,gBAAgB,sBAAsB,GAAG,YAAY,YAAY;AACrE,aAAO,qBAAqB;AAC5B,aAAO,4BAA4B,GAAG;AAAA,IAC1C;AAEA,oBAAgB,IAAI,aAAa;AAEjC,WAAO;AAAA,EACX;AAEA,KAAG,SAAS,SAAS,SAAS;AAC1B,WAAO,QAAQ,OAAO,OAAO;AAAA,EACjC;AAEA,KAAG,kBAAkB,SAAS,SAAS;AAEnC,UAAM,eAAe,SAAS,gBAAgB,GAAG;AACjD,QAAI,iBAAiB,QAAQ;AACzB,aAAO,GAAG,UAAU,OAAO;AAAA,IAC/B;AAEA,QAAI,MAAM,GAAG,UAAU,OAAO,IACxB,gBAAgB,mBAAmB,GAAG,QAAQ,IAC9C,+BAA+B,mBAAmB,QAAQ,YAAY,SAAS,KAAK,CAAC;AAE3F,QAAI,GAAG,SAAS;AACZ,aAAO,oBAAoB,mBAAmB,GAAG,OAAO;AAAA,IAC5D;AAEA,WAAO;AAAA,EACX;AAEA,KAAG,WAAW,SAAU,SAAS;AAC7B,WAAO,QAAQ,SAAS,OAAO;AAAA,EACnC;AAEA,KAAG,oBAAoB,SAAS,SAAS;AACrC,QAAI,CAAC,SAAS;AACV,gBAAU,CAAC;AAAA,IACf;AACA,YAAQ,SAAS;AACjB,WAAO,GAAG,eAAe,OAAO;AAAA,EACpC;AAEA,KAAG,mBAAmB,SAAS,SAAS;AACpC,QAAI,QAAQ,YAAY;AACxB,QAAI,MAAM;AACV,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,QACJ,uBACe,mBAAmB,GAAG,QAAQ,IAC7C,mBAAmB,mBAAmB,QAAQ,YAAY,OAAO,CAAC;AAAA,IACxE;AACA,WAAO;AAAA,EACX;AAEA,KAAG,oBAAoB,WAAW;AAC9B,WAAO,QAAQ,kBAAkB;AAAA,EACrC;AAEA,KAAG,eAAe,SAAU,MAAM;AAC9B,QAAI,SAAS,GAAG;AAChB,WAAO,CAAC,CAAC,UAAU,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EACrD;AAEA,KAAG,kBAAkB,SAAS,MAAM,UAAU;AAC1C,QAAI,CAAC,GAAG,gBAAgB;AACpB,aAAO;AAAA,IACX;AAEA,QAAI,SAAS,GAAG,eAAe,YAAY,GAAG,QAAQ;AACtD,WAAO,CAAC,CAAC,UAAU,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EACrD;AAEA,KAAG,kBAAkB,WAAW;AAC5B,QAAI,MAAM,YAAY,IAAI;AAC1B,QAAI,MAAM,IAAI,eAAe;AAC7B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,iBAAiB,UAAU,kBAAkB;AACjD,QAAI,iBAAiB,iBAAiB,YAAY,GAAG,KAAK;AAE1D,QAAI,UAAU,cAAc;AAE5B,QAAI,qBAAqB,WAAY;AACjC,UAAI,IAAI,cAAc,GAAG;AACrB,YAAI,IAAI,UAAU,KAAK;AACnB,aAAG,UAAU,KAAK,MAAM,IAAI,YAAY;AACxC,kBAAQ,WAAW,GAAG,OAAO;AAAA,QACjC,OAAO;AACH,kBAAQ,SAAS;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,KAAK;AAET,WAAO,QAAQ;AAAA,EACnB;AAEA,KAAG,eAAe,WAAW;AACzB,QAAI,MAAM,GAAG,UAAU,SAAS;AAChC,QAAI,MAAM,IAAI,eAAe;AAC7B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,iBAAiB,UAAU,kBAAkB;AACjD,QAAI,iBAAiB,iBAAiB,YAAY,GAAG,KAAK;AAE1D,QAAI,UAAU,cAAc;AAE5B,QAAI,qBAAqB,WAAY;AACjC,UAAI,IAAI,cAAc,GAAG;AACrB,YAAI,IAAI,UAAU,KAAK;AACnB,aAAG,WAAW,KAAK,MAAM,IAAI,YAAY;AACzC,kBAAQ,WAAW,GAAG,QAAQ;AAAA,QAClC,OAAO;AACH,kBAAQ,SAAS;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,KAAK;AAET,WAAO,QAAQ;AAAA,EACnB;AAEA,KAAG,iBAAiB,SAAS,aAAa;AACtC,QAAI,CAAC,GAAG,eAAgB,CAAC,GAAG,gBAAgB,GAAG,QAAQ,YAAc;AACjE,YAAM;AAAA,IACV;AAEA,QAAI,GAAG,YAAY,MAAM;AACrB,cAAQ,2EAA2E;AACnF,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,GAAG,YAAY,KAAK,IAAI,KAAK,MAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,GAAI,IAAI,GAAG;AACpF,QAAI,aAAa;AACb,UAAI,MAAM,WAAW,GAAG;AACpB,cAAM;AAAA,MACV;AACA,mBAAa;AAAA,IACjB;AACA,WAAO,YAAY;AAAA,EACvB;AAEA,KAAG,cAAc,SAAS,aAAa;AACnC,QAAI,UAAU,cAAc;AAE5B,QAAI,CAAC,GAAG,cAAc;AAClB,cAAQ,SAAS;AACjB,aAAO,QAAQ;AAAA,IACnB;AAEA,kBAAc,eAAe;AAE7B,QAAI,OAAO,WAAW;AAClB,UAAI,eAAe;AACnB,UAAI,eAAe,IAAI;AACnB,uBAAe;AACf,gBAAQ,6CAA6C;AAAA,MACzD,WAAW,CAAC,GAAG,eAAe,GAAG,eAAe,WAAW,GAAG;AAC1D,uBAAe;AACf,gBAAQ,4CAA4C;AAAA,MACxD;AAEA,UAAI,CAAC,cAAc;AACf,gBAAQ,WAAW,KAAK;AAAA,MAC5B,OAAO;AACH,YAAI,SAAS,4CAAiD,GAAG;AACjE,YAAI,MAAM,GAAG,UAAU,MAAM;AAE7B,qBAAa,KAAK,OAAO;AAEzB,YAAI,aAAa,UAAU,GAAG;AAC1B,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,QAAQ,KAAK,IAAI;AAC1B,cAAI,iBAAiB,gBAAgB,mCAAmC;AACxE,cAAI,kBAAkB;AAEtB,oBAAU,gBAAgB,mBAAmB,GAAG,QAAQ;AAExD,cAAI,aAAY,oBAAI,KAAK,GAAE,QAAQ;AAEnC,cAAI,qBAAqB,WAAY;AACjC,gBAAI,IAAI,cAAc,GAAG;AACrB,kBAAI,IAAI,UAAU,KAAK;AACnB,wBAAQ,4BAA4B;AAEpC,6BAAa,aAAY,oBAAI,KAAK,GAAE,QAAQ,KAAK;AAEjD,oBAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY;AAE/C,yBAAS,cAAc,cAAc,GAAG,cAAc,eAAe,GAAG,cAAc,UAAU,GAAG,SAAS;AAE5G,mBAAG,wBAAwB,GAAG,qBAAqB;AACnD,yBAAS,IAAI,aAAa,IAAI,GAAG,KAAK,MAAM,IAAI,aAAa,IAAI,GAAG;AAChE,oBAAE,WAAW,IAAI;AAAA,gBACrB;AAAA,cACJ,OAAO;AACH,wBAAQ,oCAAoC;AAE5C,oBAAI,IAAI,UAAU,KAAK;AACnB,qBAAG,WAAW;AAAA,gBAClB;AAEA,mBAAG,sBAAsB,GAAG,mBAAmB;AAC/C,yBAAS,IAAI,aAAa,IAAI,GAAG,KAAK,MAAM,IAAI,aAAa,IAAI,GAAG;AAChE,oBAAE,SAAS,IAAI;AAAA,gBACnB;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAEA,cAAI,KAAK,MAAM;AAAA,QACnB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,YAAY,QAAQ;AACpB,UAAI,gBAAgB,iBAAiB;AACrC,oBAAc,KAAK,WAAW;AAC1B,aAAK;AAAA,MACT,CAAC,EAAE,MAAM,SAAS,OAAO;AACrB,gBAAQ,SAAS,KAAK;AAAA,MAC1B,CAAC;AAAA,IACL,OAAO;AACH,WAAK;AAAA,IACT;AAEA,WAAO,QAAQ;AAAA,EACnB;AAEA,KAAG,aAAa,WAAW;AACvB,QAAI,GAAG,OAAO;AACV,eAAS,MAAM,MAAM,IAAI;AACzB,SAAG,gBAAgB,GAAG,aAAa;AACnC,UAAI,GAAG,eAAe;AAClB,WAAG,MAAM;AAAA,MACb;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,cAAc;AACnB,QAAI,OAAO,GAAG,kBAAkB,aAAa;AACzC,UAAI,GAAG,cAAc,OAAO,GAAG,cAAc,SAAS,CAAC,KAAK,KAAK;AAC7D,eAAO,GAAG,gBAAgB,YAAY,mBAAmB,GAAG,KAAK;AAAA,MACrE,OAAO;AACH,eAAO,GAAG,gBAAgB,aAAa,mBAAmB,GAAG,KAAK;AAAA,MACtE;AAAA,IACJ,OAAO;AACH,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,WAAS,YAAY;AACjB,QAAI,CAAC,OAAO,SAAS,QAAQ;AACzB,aAAO,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,YAAY,OAAO,SAAS,OAAO,MAAM,OAAO,SAAS,OAAM;AAAA,IAC5H,OAAO;AACH,aAAO,OAAO,SAAS;AAAA,IAC3B;AAAA,EACJ;AAEA,WAAS,gBAAgB,OAAO,SAAS;AACrC,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AAEnB,QAAI,aAAY,oBAAI,KAAK,GAAE,QAAQ;AAEnC,QAAI,MAAM,kBAAkB,GAAG;AAC3B,SAAG,kBAAkB,GAAG,eAAe,MAAM,kBAAkB,CAAC;AAAA,IACpE;AAEA,QAAI,OAAO;AACP,UAAI,UAAU,QAAQ;AAClB,YAAI,YAAY,EAAE,OAAc,mBAAmB,MAAM,kBAAkB;AAC3E,WAAG,eAAe,GAAG,YAAY,SAAS;AAC1C,mBAAW,QAAQ,SAAS,SAAS;AAAA,MACzC,OAAO;AACH,mBAAW,QAAQ,WAAW;AAAA,MAClC;AACA;AAAA,IACJ,WAAY,GAAG,QAAQ,eAAgB,MAAM,gBAAgB,MAAM,WAAW;AAC1E,kBAAY,MAAM,cAAc,MAAM,MAAM,UAAU,IAAI;AAAA,IAC9D;AAEA,QAAK,GAAG,QAAQ,cAAe,MAAM;AACjC,UAAI,SAAS,UAAU,OAAO;AAC9B,UAAI,MAAM,GAAG,UAAU,MAAM;AAE7B,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,KAAK,QAAQ,KAAK,IAAI;AAC1B,UAAI,iBAAiB,gBAAgB,mCAAmC;AAExE,gBAAU,gBAAgB,mBAAmB,GAAG,QAAQ;AACxD,gBAAU,mBAAmB,MAAM;AAEnC,UAAI,MAAM,kBAAkB;AACxB,kBAAU,oBAAoB,MAAM;AAAA,MACxC;AAEA,UAAI,kBAAkB;AAEtB,UAAI,qBAAqB,WAAW;AAChC,YAAI,IAAI,cAAc,GAAG;AACrB,cAAI,IAAI,UAAU,KAAK;AAEnB,gBAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY;AAC/C,wBAAY,cAAc,cAAc,GAAG,cAAc,eAAe,GAAG,cAAc,UAAU,GAAG,GAAG,SAAS,UAAU;AAC5H,gCAAoB;AAAA,UACxB,OAAO;AACH,eAAG,eAAe,GAAG,YAAY;AACjC,uBAAW,QAAQ,SAAS;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,KAAK,MAAM;AAAA,IACnB;AAEA,aAAS,YAAY,aAAa,cAAc,SAAS,gBAAgB;AACrE,mBAAa,aAAY,oBAAI,KAAK,GAAE,QAAQ,KAAK;AAEjD,eAAS,aAAa,cAAc,SAAS,SAAS;AAEtD,UAAI,aAAa,GAAG,iBAAiB,GAAG,cAAc,SAAS,MAAM,cAAc;AAC/E,gBAAQ,0CAA0C;AAClD,WAAG,WAAW;AACd,mBAAW,QAAQ,SAAS;AAAA,MAChC,OAAO;AACH,YAAI,gBAAgB;AAChB,aAAG,iBAAiB,GAAG,cAAc;AACrC,qBAAW,QAAQ,WAAW;AAAA,QAClC;AAAA,MACJ;AAAA,IACJ;AAAA,EAEJ;AAEA,WAAS,WAAW,KAAK;AACrB,QAAI,UAAU,cAAc;AAC5B,QAAI;AAEJ,QAAI,CAAC,QAAQ;AACT,kBAAY;AAAA,IAChB,WAAW,OAAO,WAAW,UAAU;AACnC,kBAAY;AAAA,IAChB;AAEA,aAAS,kBAAkB,mBAAmB;AAC1C,UAAI,CAAE,mBAAmB;AACrB,WAAG,YAAY;AAAA,UACX,WAAW,WAAW;AAClB,mBAAO,YAAY,IAAI;AAAA,UAC3B;AAAA,UACA,OAAO,WAAW;AACd,mBAAO,YAAY,IAAI;AAAA,UAC3B;AAAA,UACA,QAAQ,WAAW;AACf,mBAAO,YAAY,IAAI;AAAA,UAC3B;AAAA,UACA,oBAAoB,WAAW;AAC3B,gBAAI,MAAM,YAAY,IAAI;AAC1B,gBAAI,GAAG,eAAe;AAClB,oBAAM,MAAM,cAAc,GAAG;AAAA,YACjC;AACA,mBAAO;AAAA,UACX;AAAA,UACA,yBAAyB,WAAW;AAChC,gBAAI,MAAM,YAAY,IAAI;AAC1B,gBAAI,GAAG,eAAe;AAClB,oBAAM,MAAM,cAAc,GAAG;AAAA,YACjC;AACA,mBAAO;AAAA,UACX;AAAA,UACA,UAAU,WAAW;AACjB,mBAAO,YAAY,IAAI;AAAA,UAC3B;AAAA,UACA,UAAU,WAAW;AACjB,mBAAO,YAAY,IAAI;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,WAAG,YAAY;AAAA,UACX,WAAW,WAAW;AAClB,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,UACA,OAAO,WAAW;AACd,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,UACA,QAAQ,WAAW;AACf,gBAAI,CAAC,kBAAkB,sBAAsB;AACzC,oBAAM;AAAA,YACV;AACA,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,UACA,oBAAoB,WAAW;AAC3B,gBAAI,CAAC,kBAAkB,sBAAsB;AACzC,oBAAM;AAAA,YACV;AACA,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,UACA,UAAU,WAAW;AACjB,kBAAM;AAAA,UACV;AAAA,UACA,UAAU,WAAW;AACjB,gBAAI,CAAC,kBAAkB,mBAAmB;AACtC,oBAAM;AAAA,YACV;AACA,mBAAO,kBAAkB;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,WAAW;AACX,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,KAAK,OAAO,WAAW,IAAI;AAC/B,UAAI,iBAAiB,UAAU,kBAAkB;AAEjD,UAAI,qBAAqB,WAAY;AACjC,YAAI,IAAI,cAAc,GAAG;AACrB,cAAI,IAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACtC,gBAAIC,UAAS,KAAK,MAAM,IAAI,YAAY;AAExC,eAAG,gBAAgBA,QAAO,iBAAiB;AAC3C,eAAG,QAAQA,QAAO,OAAO;AACzB,eAAG,WAAWA,QAAO,UAAU;AAC/B,8BAAkB,IAAI;AACtB,oBAAQ,WAAW;AAAA,UACvB,OAAO;AACH,oBAAQ,SAAS;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,KAAK;AAAA,IACb,OAAO;AACH,UAAI,CAAC,OAAO,UAAU;AAClB,cAAM;AAAA,MACV;AAEA,SAAG,WAAW,OAAO;AAErB,UAAI,eAAe,OAAO,cAAc;AACxC,UAAI,CAAC,cAAc;AACf,YAAI,CAAC,OAAO,KAAK,GAAG;AAChB,cAAIC,WAAU,SAAS,qBAAqB,QAAQ;AACpD,mBAASH,KAAI,GAAGA,KAAIG,SAAQ,QAAQH,MAAK;AACrC,gBAAIG,SAAQH,EAAC,EAAE,IAAI,MAAM,gBAAgB,GAAG;AACxC,qBAAO,MAAMG,SAAQH,EAAC,EAAE,IAAI,OAAO,GAAGG,SAAQH,EAAC,EAAE,IAAI,QAAQ,iBAAiB,CAAC;AAC/E;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,CAAC,OAAO,OAAO;AACf,gBAAM;AAAA,QACV;AAEA,WAAG,gBAAgB,OAAO;AAC1B,WAAG,QAAQ,OAAO;AAClB,0BAAkB,IAAI;AACtB,gBAAQ,WAAW;AAAA,MACvB,OAAO;AACH,YAAI,OAAO,iBAAiB,UAAU;AAClC,cAAI;AACJ,cAAI,aAAa,OAAO,aAAa,SAAS,CAAC,KAAK,KAAK;AACrD,oCAAwB,eAAe;AAAA,UAC3C,OAAO;AACH,oCAAwB,eAAe;AAAA,UAC3C;AACA,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,OAAO,uBAAuB,IAAI;AAC3C,cAAI,iBAAiB,UAAU,kBAAkB;AAEjD,cAAI,qBAAqB,WAAY;AACjC,gBAAI,IAAI,cAAc,GAAG;AACrB,kBAAI,IAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACtC,oBAAI,qBAAqB,KAAK,MAAM,IAAI,YAAY;AACpD,kCAAkB,kBAAkB;AACpC,wBAAQ,WAAW;AAAA,cACvB,OAAO;AACH,wBAAQ,SAAS;AAAA,cACrB;AAAA,YACJ;AAAA,UACJ;AAEA,cAAI,KAAK;AAAA,QACb,OAAO;AACH,4BAAkB,YAAY;AAC9B,kBAAQ,WAAW;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,QAAQ;AAAA,EACnB;AAEA,WAAS,WAAW,KAAK;AACrB,WAAO,IAAI,UAAU,KAAK,IAAI,gBAAgB,IAAI,YAAY,WAAW,OAAO;AAAA,EACpF;AAEA,WAAS,SAAS,OAAO,cAAc,SAAS,WAAW;AACvD,QAAI,GAAG,oBAAoB;AACvB,mBAAa,GAAG,kBAAkB;AAClC,SAAG,qBAAqB;AAAA,IAC5B;AAEA,QAAI,cAAc;AACd,SAAG,eAAe;AAClB,SAAG,qBAAqB,UAAU,YAAY;AAAA,IAClD,OAAO;AACH,aAAO,GAAG;AACV,aAAO,GAAG;AAAA,IACd;AAEA,QAAI,SAAS;AACT,SAAG,UAAU;AACb,SAAG,gBAAgB,UAAU,OAAO;AAAA,IACxC,OAAO;AACH,aAAO,GAAG;AACV,aAAO,GAAG;AAAA,IACd;AAEA,QAAI,OAAO;AACP,SAAG,QAAQ;AACX,SAAG,cAAc,UAAU,KAAK;AAChC,SAAG,YAAY,GAAG,YAAY;AAC9B,SAAG,gBAAgB;AACnB,SAAG,UAAU,GAAG,YAAY;AAC5B,SAAG,cAAc,GAAG,YAAY;AAChC,SAAG,iBAAiB,GAAG,YAAY;AAEnC,UAAI,WAAW;AACX,WAAG,WAAW,KAAK,MAAM,YAAY,GAAI,IAAI,GAAG,YAAY;AAAA,MAChE;AAEA,UAAI,GAAG,YAAY,MAAM;AACrB,gBAAQ,wEAAwE,GAAG,WAAW,UAAU;AAExG,YAAI,GAAG,gBAAgB;AACnB,cAAI,aAAa,GAAG,YAAY,KAAK,KAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,MAAQ,GAAG,YAAY;AACxF,kBAAQ,iCAAiC,KAAK,MAAM,YAAY,GAAI,IAAI,IAAI;AAC5E,cAAI,aAAa,GAAG;AAChB,eAAG,eAAe;AAAA,UACtB,OAAO;AACH,eAAG,qBAAqB,WAAW,GAAG,gBAAgB,SAAS;AAAA,UACnE;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,OAAO;AACH,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AAEV,SAAG,gBAAgB;AAAA,IACvB;AAAA,EACJ;AAEA,WAAS,aAAa;AAClB,QAAI,YAAY;AAChB,QAAI,IAAI,qBAAqB,IAAI,SAAS,EAAE,MAAM,EAAE;AACpD,MAAE,EAAE,IAAI;AACR,MAAE,EAAE,IAAI,UAAU,OAAQ,EAAE,EAAE,IAAI,IAAO,GAAK,CAAC;AAC/C,MAAE,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI;AAC/B,QAAI,OAAO,EAAE,KAAK,EAAE;AACpB,WAAO;AAAA,EACX;AAEA,WAAS,cAAc,KAAK;AACxB,QAAI,QAAQ,iBAAiB,GAAG;AAChC,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AAEA,QAAI,aAAa,gBAAgB,IAAI,MAAM,KAAK;AAEhD,QAAI,YAAY;AACZ,YAAM,QAAQ;AACd,YAAM,cAAc,WAAW;AAC/B,YAAM,cAAc,WAAW;AAC/B,YAAM,SAAS,WAAW;AAC1B,YAAM,mBAAmB,WAAW;AAAA,IACxC;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,iBAAiB,KAAK;AAC3B,QAAI;AACJ,YAAQ,GAAG,MAAM;AAAA,MACb,KAAK;AACD,0BAAkB,CAAC,QAAQ,SAAS,iBAAiB,oBAAoB,KAAK;AAC9E;AAAA,MACJ,KAAK;AACD,0BAAkB,CAAC,gBAAgB,cAAc,YAAY,SAAS,iBAAiB,cAAc,oBAAoB,KAAK;AAC9H;AAAA,MACJ,KAAK;AACD,0BAAkB,CAAC,gBAAgB,cAAc,YAAY,QAAQ,SAAS,iBAAiB,cAAc,oBAAoB,KAAK;AACtI;AAAA,IACR;AAEA,oBAAgB,KAAK,OAAO;AAC5B,oBAAgB,KAAK,mBAAmB;AACxC,oBAAgB,KAAK,WAAW;AAEhC,QAAI,aAAa,IAAI,QAAQ,GAAG;AAChC,QAAI,gBAAgB,IAAI,QAAQ,GAAG;AAEnC,QAAI;AACJ,QAAI;AAEJ,QAAI,GAAG,iBAAiB,WAAW,eAAe,IAAI;AAClD,eAAS,IAAI,UAAU,GAAG,UAAU;AACpC,eAAS,oBAAoB,IAAI,UAAU,aAAa,GAAG,kBAAkB,KAAK,gBAAgB,IAAI,MAAM,GAAG,eAAe;AAC9H,UAAI,OAAO,iBAAiB,IAAI;AAC5B,kBAAU,MAAM,OAAO;AAAA,MAC3B;AACA,UAAI,kBAAkB,IAAI;AACtB,kBAAU,IAAI,UAAU,aAAa;AAAA,MACzC;AAAA,IACJ,WAAW,GAAG,iBAAiB,cAAc,kBAAkB,IAAI;AAC/D,eAAS,IAAI,UAAU,GAAG,aAAa;AACvC,eAAS,oBAAoB,IAAI,UAAU,gBAAgB,CAAC,GAAG,eAAe;AAC9E,UAAI,OAAO,iBAAiB,IAAI;AAC5B,kBAAU,MAAM,OAAO;AAAA,MAC3B;AAAA,IACJ;AAEA,QAAI,UAAU,OAAO,aAAa;AAC9B,UAAI,GAAG,SAAS,cAAc,GAAG,SAAS,UAAU;AAChD,aAAK,OAAO,YAAY,QAAQ,OAAO,YAAY,UAAU,OAAO,YAAY,OAAO;AACnF,iBAAO,YAAY,SAAS;AAC5B,iBAAO,OAAO;AAAA,QAClB;AAAA,MACJ,WAAW,GAAG,SAAS,YAAY;AAC/B,aAAK,OAAO,YAAY,gBAAgB,OAAO,YAAY,UAAU,OAAO,YAAY,OAAO;AAC3F,iBAAO,YAAY,SAAS;AAC5B,iBAAO,OAAO;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,oBAAoB,cAAc,iBAAiB;AACxD,QAAI,IAAI,aAAa,MAAM,GAAG;AAC9B,QAAI,SAAS;AAAA,MACT,cAAc;AAAA,MACd,aAAa,CAAC;AAAA,IAClB;AACA,aAASA,KAAI,GAAGA,KAAI,EAAE,QAAQA,MAAK;AAC/B,UAAI,QAAQ,EAAEA,EAAC,EAAE,QAAQ,GAAG;AAC5B,UAAI,MAAM,EAAEA,EAAC,EAAE,MAAM,GAAG,KAAK;AAC7B,UAAI,gBAAgB,QAAQ,GAAG,MAAM,IAAI;AACrC,eAAO,YAAY,GAAG,IAAI,EAAEA,EAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,MAClD,OAAO;AACH,YAAI,OAAO,iBAAiB,IAAI;AAC5B,iBAAO,gBAAgB;AAAA,QAC3B;AACA,eAAO,gBAAgB,EAAEA,EAAC;AAAA,MAC9B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAEA,WAAS,gBAAgB;AAGrB,QAAI,IAAI;AAAA,MACJ,YAAY,SAAS,QAAQ;AACzB,UAAE,QAAQ,MAAM;AAAA,MACpB;AAAA,MAEA,UAAU,SAAS,QAAQ;AACvB,UAAE,OAAO,MAAM;AAAA,MACnB;AAAA,IACJ;AACA,MAAE,UAAU,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC9C,QAAE,UAAU;AACZ,QAAE,SAAS;AAAA,IACf,CAAC;AAED,WAAO;AAAA,EACX;AAGA,WAAS,sBAAsB,SAAS,SAAS,cAAc;AAC3D,QAAI,gBAAgB;AACpB,QAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACxD,sBAAgB,WAAW,WAAY;AACnC,eAAO,EAAE,SAAS,gBAAgB,8CAA8C,UAAU,KAAK,CAAC;AAAA,MACpG,GAAG,OAAO;AAAA,IACd,CAAC;AAED,WAAO,QAAQ,KAAK,CAAC,SAAS,cAAc,CAAC,EAAE,QAAQ,WAAY;AAC/D,mBAAa,aAAa;AAAA,IAC9B,CAAC;AAAA,EACL;AAEA,WAAS,wBAAwB;AAC7B,QAAI,UAAU,cAAc;AAE5B,QAAI,CAAC,YAAY,QAAQ;AACrB,cAAQ,WAAW;AACnB,aAAO,QAAQ;AAAA,IACnB;AAEA,QAAI,YAAY,QAAQ;AACpB,cAAQ,WAAW;AACnB,aAAO,QAAQ;AAAA,IACnB;AAEA,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,gBAAY,SAAS;AAErB,WAAO,SAAS,WAAW;AACvB,UAAI,UAAU,GAAG,UAAU,UAAU;AACrC,UAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC3B,oBAAY,eAAe,UAAU;AAAA,MACzC,OAAO;AACH,oBAAY,eAAe,QAAQ,UAAU,GAAG,QAAQ,QAAQ,KAAK,CAAC,CAAC;AAAA,MAC3E;AACA,cAAQ,WAAW;AAAA,IACvB;AAEA,QAAI,MAAM,GAAG,UAAU,mBAAmB;AAC1C,WAAO,aAAa,OAAO,GAAI;AAC/B,WAAO,aAAa,WAAW,yEAAyE;AACxG,WAAO,aAAa,SAAS,yBAA0B;AACvD,WAAO,MAAM,UAAU;AACvB,aAAS,KAAK,YAAY,MAAM;AAEhC,QAAI,kBAAkB,SAAS,OAAO;AAClC,UAAK,MAAM,WAAW,YAAY,gBAAkB,YAAY,OAAO,kBAAkB,MAAM,QAAS;AACpG;AAAA,MACJ;AAEA,UAAI,EAAE,MAAM,QAAQ,eAAe,MAAM,QAAQ,aAAa,MAAM,QAAQ,UAAU;AAClF;AAAA,MACJ;AAGA,UAAI,MAAM,QAAQ,aAAa;AAC3B,WAAG,WAAW;AAAA,MAClB;AAEA,UAAI,YAAY,YAAY,aAAa,OAAO,GAAG,YAAY,aAAa,MAAM;AAElF,eAASA,KAAI,UAAU,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC5C,YAAID,WAAU,UAAUC,EAAC;AACzB,YAAI,MAAM,QAAQ,SAAS;AACvB,UAAAD,SAAQ,SAAS;AAAA,QACrB,OAAO;AACH,UAAAA,SAAQ,WAAW,MAAM,QAAQ,WAAW;AAAA,QAChD;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,iBAAiB,WAAW,iBAAiB,KAAK;AAEzD,WAAO,QAAQ;AAAA,EACnB;AAEA,WAAS,sBAAsB;AAC3B,QAAI,YAAY,QAAQ;AACpB,UAAI,GAAG,OAAO;AACV,mBAAW,WAAW;AAClB,2BAAiB,EAAE,KAAK,SAAS,WAAW;AACxC,gBAAI,WAAW;AACX,kCAAoB;AAAA,YACxB;AAAA,UACJ,CAAC;AAAA,QACL,GAAG,YAAY,WAAW,GAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,mBAAmB;AACxB,QAAI,UAAU,cAAc;AAE5B,QAAI,YAAY,UAAU,YAAY,cAAe;AACjD,UAAI,MAAM,GAAG,WAAW,OAAO,GAAG,YAAY,GAAG,YAAY;AAC7D,kBAAY,aAAa,KAAK,OAAO;AACrC,UAAI,SAAS,YAAY;AACzB,UAAI,YAAY,aAAa,UAAU,GAAG;AACtC,oBAAY,OAAO,cAAc,YAAY,KAAK,MAAM;AAAA,MAC5D;AAAA,IACJ,OAAO;AACH,cAAQ,WAAW;AAAA,IACvB;AAEA,WAAO,QAAQ;AAAA,EACnB;AAEA,WAAS,0BAA0B;AAC/B,QAAI,UAAU,cAAc;AAE5B,QAAI,YAAY,UAAU,GAAG,2BAA2B;AACpD,UAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,aAAO,aAAa,OAAO,GAAG,UAAU,wBAAwB,CAAC;AACjE,aAAO,aAAa,WAAW,yEAAyE;AACxG,aAAO,aAAa,SAAS,0BAA2B;AACxD,aAAO,MAAM,UAAU;AACvB,eAAS,KAAK,YAAY,MAAM;AAEhC,UAAI,kBAAkB,SAAS,OAAO;AAClC,YAAI,OAAO,kBAAkB,MAAM,QAAQ;AACvC;AAAA,QACJ;AAEA,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,eAAe;AAC5D;AAAA,QACJ,WAAW,MAAM,SAAS,eAAe;AACrC;AAAA,YACI;AAAA,UAIJ;AAEA,sBAAY,SAAS;AACrB,cAAI,GAAG,wBAAwB;AAC3B,eAAG,4BAA4B;AAAA,UACnC;AAAA,QACJ;AAEA,iBAAS,KAAK,YAAY,MAAM;AAChC,eAAO,oBAAoB,WAAW,eAAe;AACrD,gBAAQ,WAAW;AAAA,MACvB;AAEA,aAAO,iBAAiB,WAAW,iBAAiB,KAAK;AAAA,IAC7D,OAAO;AACH,cAAQ,WAAW;AAAA,IACvB;AAEA,WAAO,sBAAsB,QAAQ,SAAS,GAAG,uBAAuB,0DAA0D;AAAA,EACtI;AAEA,WAAS,YAAY,MAAM;AACvB,QAAI,CAAC,QAAQ,QAAQ,WAAW;AAC5B,aAAO;AAAA,QACH,OAAO,SAAS,SAAS;AACrB,iBAAO,SAAS,OAAO,GAAG,eAAe,OAAO,CAAC;AACjD,iBAAO,cAAc,EAAE;AAAA,QAC3B;AAAA,QAEA,QAAQ,SAAe,SAAS;AAAA;AAE5B,kBAAM,eAAe,SAAS,gBAAgB,GAAG;AACjD,gBAAI,iBAAiB,OAAO;AACxB,qBAAO,SAAS,QAAQ,GAAG,gBAAgB,OAAO,CAAC;AACnD;AAAA,YACJ;AAEA,kBAAM,YAAY,GAAG,gBAAgB,OAAO;AAC5C,kBAAM,WAAW,MAAM,MAAM,WAAW;AAAA,cACpC,QAAQ;AAAA,cACR,SAAS;AAAA,gBACL,gBAAgB;AAAA,cACpB;AAAA,cACA,MAAM,IAAI,gBAAgB;AAAA,gBACtB,eAAe,GAAG;AAAA,gBAClB,WAAW,GAAG;AAAA,gBACd,0BAA0B,QAAQ,YAAY,SAAS,KAAK;AAAA,cAChE,CAAC;AAAA,YACL,CAAC;AAED,gBAAI,SAAS,YAAY;AACrB,qBAAO,SAAS,OAAO,SAAS;AAChC;AAAA,YACJ;AAEA,gBAAI,SAAS,IAAI;AACb,qBAAO,SAAS,OAAO;AACvB;AAAA,YACJ;AAEA,kBAAM,IAAI,MAAM,gDAAgD;AAAA,UACpE;AAAA;AAAA,QAEA,UAAU,SAAS,SAAS;AACxB,iBAAO,SAAS,OAAO,GAAG,kBAAkB,OAAO,CAAC;AACpD,iBAAO,cAAc,EAAE;AAAA,QAC3B;AAAA,QAEA,mBAAoB,WAAW;AAC3B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACnC,mBAAO,SAAS,OAAO;AAAA,UAC3B,OAAO;AACH,kBAAM;AAAA,UACV;AACA,iBAAO,cAAc,EAAE;AAAA,QAC3B;AAAA,QAEA,aAAa,SAAS,SAAS,YAAY;AAEvC,cAAI,WAAW,QAAQ,aAAa;AAChC,mBAAO,QAAQ;AAAA,UACnB,WAAW,GAAG,aAAa;AACvB,mBAAO,GAAG;AAAA,UACd,OAAO;AACH,mBAAO,SAAS;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,QAAQ,WAAW;AACnB,kBAAY,SAAS;AACrB,UAAI,2BAA2B,SAAS,UAAU,QAAQ,SAAS;AAC/D,YAAI,OAAO,WAAW,OAAO,QAAQ,cAAc;AAE/C,iBAAO,OAAO,QAAQ,aAAa,KAAK,UAAU,QAAQ,OAAO;AAAA,QACrE,OAAO;AACH,iBAAO,OAAO,KAAK,UAAU,QAAQ,OAAO;AAAA,QAChD;AAAA,MACJ;AAEA,UAAI,6BAA6B,SAAU,aAAa;AACpD,YAAI,eAAe,YAAY,gBAAgB;AAC3C,iBAAO,OAAO,KAAK,YAAY,cAAc,EAAE,OAAO,SAAU,SAAS,YAAY;AACjF,oBAAQ,UAAU,IAAI,YAAY,eAAe,UAAU;AAC3D,mBAAO;AAAA,UACX,GAAG,CAAC,CAAC;AAAA,QACT,OAAO;AACH,iBAAO,CAAC;AAAA,QACZ;AAAA,MACJ;AAEA,UAAI,uBAAuB,SAAU,gBAAgB;AACjD,eAAO,OAAO,KAAK,cAAc,EAAE,OAAO,SAAU,SAAS,YAAY;AACrE,kBAAQ,KAAK,aAAW,MAAI,eAAe,UAAU,CAAC;AACtD,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,MACnB;AAEA,UAAI,uBAAuB,SAAU,aAAa;AAC9C,YAAI,iBAAiB,2BAA2B,WAAW;AAC3D,uBAAe,WAAW;AAC1B,YAAI,eAAe,YAAY,UAAU,QAAQ;AAC7C,yBAAe,SAAS;AAAA,QAC5B;AACA,eAAO,qBAAqB,cAAc;AAAA,MAC9C;AAEA,UAAI,wBAAwB,WAAW;AACnC,eAAO,GAAG,eAAe;AAAA,MAC7B;AAEA,aAAO;AAAA,QACH,OAAO,SAAS,SAAS;AACrB,cAAI,UAAU,cAAc;AAE5B,cAAI,iBAAiB,qBAAqB,OAAO;AACjD,cAAI,WAAW,GAAG,eAAe,OAAO;AACxC,cAAI,MAAM,yBAAyB,UAAU,UAAU,cAAc;AACrE,cAAI,YAAY;AAEhB,cAAI,SAAS;AACb,cAAI,eAAe,WAAW;AAC1B,qBAAS;AACT,gBAAI,MAAM;AAAA,UACd;AAEA,cAAI,iBAAiB,aAAa,SAAS,OAAO;AAC9C,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACjD,kBAAI,WAAW,cAAc,MAAM,GAAG;AACtC,8BAAgB,UAAU,OAAO;AACjC,2BAAa;AACb,0BAAY;AAAA,YAChB;AAAA,UACJ,CAAC;AAED,cAAI,iBAAiB,aAAa,SAAS,OAAO;AAC9C,gBAAI,CAAC,WAAW;AACZ,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACjD,oBAAI,WAAW,cAAc,MAAM,GAAG;AACtC,gCAAgB,UAAU,OAAO;AACjC,6BAAa;AACb,4BAAY;AAAA,cAChB,OAAO;AACH,wBAAQ,SAAS;AACjB,6BAAa;AAAA,cACjB;AAAA,YACJ;AAAA,UACJ,CAAC;AAED,cAAI,iBAAiB,QAAQ,SAAS,OAAO;AACzC,gBAAI,CAAC,QAAQ;AACT,sBAAQ,SAAS;AAAA,gBACb,QAAQ;AAAA,cACZ,CAAC;AAAA,YACL;AAAA,UACJ,CAAC;AAED,iBAAO,QAAQ;AAAA,QACnB;AAAA,QAEA,QAAQ,SAAS,SAAS;AACtB,cAAI,UAAU,cAAc;AAE5B,cAAI,YAAY,GAAG,gBAAgB,OAAO;AAC1C,cAAI,MAAM,yBAAyB,WAAW,UAAU,uCAAuC;AAE/F,cAAI;AAEJ,cAAI,iBAAiB,aAAa,SAAS,OAAO;AAC9C,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACjD,kBAAI,MAAM;AAAA,YACd;AAAA,UACJ,CAAC;AAED,cAAI,iBAAiB,aAAa,SAAS,OAAO;AAC9C,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACjD,kBAAI,MAAM;AAAA,YACd,OAAO;AACH,sBAAQ;AACR,kBAAI,MAAM;AAAA,YACd;AAAA,UACJ,CAAC;AAED,cAAI,iBAAiB,QAAQ,SAAS,OAAO;AACzC,gBAAI,OAAO;AACP,sBAAQ,SAAS;AAAA,YACrB,OAAO;AACH,iBAAG,WAAW;AACd,sBAAQ,WAAW;AAAA,YACvB;AAAA,UACJ,CAAC;AAED,iBAAO,QAAQ;AAAA,QACnB;AAAA,QAEA,UAAW,SAAS,SAAS;AACzB,cAAI,UAAU,cAAc;AAC5B,cAAI,cAAc,GAAG,kBAAkB;AACvC,cAAI,iBAAiB,qBAAqB,OAAO;AACjD,cAAI,MAAM,yBAAyB,aAAa,UAAU,cAAc;AACxE,cAAI,iBAAiB,aAAa,SAAS,OAAO;AAC9C,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACjD,kBAAI,MAAM;AACV,kBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,8BAAgB,OAAO,OAAO;AAAA,YAClC;AAAA,UACJ,CAAC;AACD,iBAAO,QAAQ;AAAA,QACnB;AAAA,QAEA,mBAAoB,WAAW;AAC3B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACnC,gBAAI,MAAM,yBAAyB,YAAY,UAAU,aAAa;AACtE,gBAAI,iBAAiB,aAAa,SAAS,OAAO;AAC9C,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACjD,oBAAI,MAAM;AAAA,cACd;AAAA,YACJ,CAAC;AAAA,UACL,OAAO;AACH,kBAAM;AAAA,UACV;AAAA,QACJ;AAAA,QAEA,aAAa,SAAS,SAAS;AAC3B,iBAAO,sBAAsB;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,QAAQ,kBAAkB;AAC1B,kBAAY,SAAS;AAErB,aAAO;AAAA,QACH,OAAO,SAAS,SAAS;AACrB,cAAI,UAAU,cAAc;AAC5B,cAAI,WAAW,GAAG,eAAe,OAAO;AAExC,yBAAe,UAAU,YAAY,SAAS,OAAO;AACjD,2BAAe,YAAY,UAAU;AACrC,mBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,gBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,4BAAgB,OAAO,OAAO;AAAA,UAClC,CAAC;AAED,iBAAO,QAAQ,QAAQ,WAAW,QAAQ,QAAQ;AAClD,iBAAO,QAAQ;AAAA,QACnB;AAAA,QAEA,QAAQ,SAAS,SAAS;AACtB,cAAI,UAAU,cAAc;AAC5B,cAAI,YAAY,GAAG,gBAAgB,OAAO;AAE1C,yBAAe,UAAU,YAAY,SAAS,OAAO;AACjD,2BAAe,YAAY,UAAU;AACrC,mBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,eAAG,WAAW;AACd,oBAAQ,WAAW;AAAA,UACvB,CAAC;AAED,iBAAO,QAAQ,QAAQ,WAAW,QAAQ,SAAS;AACnD,iBAAO,QAAQ;AAAA,QACnB;AAAA,QAEA,UAAW,SAAS,SAAS;AACzB,cAAI,UAAU,cAAc;AAC5B,cAAI,cAAc,GAAG,kBAAkB,OAAO;AAC9C,yBAAe,UAAU,YAAa,SAAS,OAAO;AAClD,2BAAe,YAAY,UAAU;AACrC,mBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,gBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,4BAAgB,OAAO,OAAO;AAAA,UAClC,CAAC;AACD,iBAAO,QAAQ,QAAQ,WAAW,QAAQ,WAAW;AACrD,iBAAO,QAAQ;AAAA,QAEnB;AAAA,QAEA,mBAAoB,WAAW;AAC3B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACnC,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,UAAU;AAAA,UACxD,OAAO;AACH,kBAAM;AAAA,UACV;AAAA,QACJ;AAAA,QAEA,aAAa,SAAS,SAAS;AAC3B,cAAI,WAAW,QAAQ,aAAa;AAChC,mBAAO,QAAQ;AAAA,UACnB,WAAW,GAAG,aAAa;AACvB,mBAAO,GAAG;AAAA,UACd,OAAO;AACH,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,UAAM,2BAA2B;AAAA,EACrC;AAEA,MAAI,eAAe,WAAW;AAC1B,QAAI,EAAE,gBAAgB,eAAe;AACjC,aAAO,IAAI,aAAa;AAAA,IAC5B;AAEA,iBAAa,QAAQ,WAAW,MAAM;AACtC,iBAAa,WAAW,SAAS;AAEjC,QAAI,KAAK;AAET,aAAS,eAAe;AACpB,UAAI,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAC9B,eAASC,KAAI,GAAGA,KAAI,aAAa,QAAQA,MAAM;AAC3C,YAAI,MAAM,aAAa,IAAIA,EAAC;AAC5B,YAAI,OAAO,IAAI,QAAQ,cAAc,KAAK,GAAG;AACzC,cAAI,QAAQ,aAAa,QAAQ,GAAG;AACpC,cAAI,OAAO;AACP,gBAAI;AACA,kBAAI,UAAU,KAAK,MAAM,KAAK,EAAE;AAChC,kBAAI,CAAC,WAAW,UAAU,MAAM;AAC5B,6BAAa,WAAW,GAAG;AAAA,cAC/B;AAAA,YACJ,SAAS,KAAK;AACV,2BAAa,WAAW,GAAG;AAAA,YAC/B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,OAAG,MAAM,SAAS,OAAO;AACrB,UAAI,CAAC,OAAO;AACR;AAAA,MACJ;AAEA,UAAI,MAAM,iBAAiB;AAC3B,UAAI,QAAQ,aAAa,QAAQ,GAAG;AACpC,UAAI,OAAO;AACP,qBAAa,WAAW,GAAG;AAC3B,gBAAQ,KAAK,MAAM,KAAK;AAAA,MAC5B;AAEA,mBAAa;AACb,aAAO;AAAA,IACX;AAEA,OAAG,MAAM,SAAS,OAAO;AACrB,mBAAa;AAEb,UAAI,MAAM,iBAAiB,MAAM;AACjC,YAAM,WAAU,oBAAI,KAAK,GAAE,QAAQ,IAAK,KAAK,KAAK;AAClD,mBAAa,QAAQ,KAAK,KAAK,UAAU,KAAK,CAAC;AAAA,IACnD;AAAA,EACJ;AAEA,MAAI,gBAAgB,WAAW;AAC3B,QAAI,EAAE,gBAAgB,gBAAgB;AAClC,aAAO,IAAI,cAAc;AAAA,IAC7B;AAEA,QAAI,KAAK;AAET,OAAG,MAAM,SAAS,OAAO;AACrB,UAAI,CAAC,OAAO;AACR;AAAA,MACJ;AAEA,UAAI,QAAQ,UAAU,iBAAiB,KAAK;AAC5C,gBAAU,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,CAAC;AAC5D,UAAI,OAAO;AACP,eAAO,KAAK,MAAM,KAAK;AAAA,MAC3B;AAAA,IACJ;AAEA,OAAG,MAAM,SAAS,OAAO;AACrB,gBAAU,iBAAiB,MAAM,OAAO,KAAK,UAAU,KAAK,GAAG,iBAAiB,EAAE,CAAC;AAAA,IACvF;AAEA,OAAG,aAAa,SAAS,KAAK;AAC1B,gBAAU,KAAK,IAAI,iBAAiB,IAAI,CAAC;AAAA,IAC7C;AAEA,QAAI,mBAAmB,SAAU,SAAS;AACtC,UAAI,MAAM,oBAAI,KAAK;AACnB,UAAI,QAAQ,IAAI,QAAQ,IAAK,UAAQ,KAAG,GAAK;AAC7C,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,SAAU,KAAK;AAC3B,UAAI,OAAO,MAAM;AACjB,UAAI,KAAK,SAAS,OAAO,MAAM,GAAG;AAClC,eAASA,KAAI,GAAGA,KAAI,GAAG,QAAQA,MAAK;AAChC,YAAI,IAAI,GAAGA,EAAC;AACZ,eAAO,EAAE,OAAO,CAAC,KAAK,KAAK;AACvB,cAAI,EAAE,UAAU,CAAC;AAAA,QACrB;AACA,YAAI,EAAE,QAAQ,IAAI,KAAK,GAAG;AACtB,iBAAO,EAAE,UAAU,KAAK,QAAQ,EAAE,MAAM;AAAA,QAC5C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,SAAU,KAAK,OAAO,gBAAgB;AAClD,UAAI,SAAS,MAAM,MAAM,QAAQ,eACd,eAAe,YAAY,IAAI;AAClD,eAAS,SAAS;AAAA,IACtB;AAAA,EACJ;AAEA,WAAS,wBAAwB;AAC7B,QAAI;AACA,aAAO,IAAI,aAAa;AAAA,IAC5B,SAAS,KAAK;AAAA,IACd;AAEA,WAAO,IAAI,cAAc;AAAA,EAC7B;AAEA,WAAS,aAAa,IAAI;AACtB,WAAO,WAAW;AACd,UAAI,GAAG,eAAe;AAClB,WAAG,MAAM,SAAS,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC;AAAA,MAC3D;AAAA,IACJ;AAAA,EACJ;AACJ;AAGA,SAAS,cAAc,OAAO;AAC1B,QAAM,YAAY,OAAO,cAAc,GAAG,KAAK;AAC/C,SAAO,KAAK,SAAS;AACzB;;;AEttDA,IAAI;AAAA,CACH,SAAUI,oBAAmB;AAC5B,EAAAA,mBAAkBA,mBAAkB,aAAa,IAAI,CAAC,IAAI;AAC1D,EAAAA,mBAAkBA,mBAAkB,cAAc,IAAI,CAAC,IAAI;AAC3D,EAAAA,mBAAkBA,mBAAkB,oBAAoB,IAAI,CAAC,IAAI;AACjE,EAAAA,mBAAkBA,mBAAkB,sBAAsB,IAAI,CAAC,IAAI;AACnE,EAAAA,mBAAkBA,mBAAkB,eAAe,IAAI,CAAC,IAAI;AAC5D,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtD,EAAAA,mBAAkBA,mBAAkB,gBAAgB,IAAI,CAAC,IAAI;AAC7D,EAAAA,mBAAkBA,mBAAkB,gBAAgB,IAAI,CAAC,IAAI;AAC/D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,QAAQ,iBAAiB;AACnC,SAAK,SAAS;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACM,YAAY,OAAO,OAAO;AAAA;AAC9B,UAAI;AACF,aAAK,gBAAgB,MAAM,KAAK,gBAAgB,WAAW;AAC3D,aAAK,QAAQ,MAAM,KAAK,gBAAgB,aAAa,IAAI;AACzD,eAAO,MAAM,KAAK,gBAAgB,OAAO,KAAK;AAAA,MAChD,SAAS,OAAO;AACd,cAAM,IAAI,MAAM,yDAAyD,KAAK;AAAA,MAChF;AAAA,IACF;AAAA;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,mBAAmB,IAAI,QAAQ;AAAA,EACtC;AAAA,EACA,sBAAsB;AACpB,SAAK,UAAU,cAAc,eAAa;AACxC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,eAAe,MAAM;AAClC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,uBAAuB,MAAM;AAC1C,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,qBAAqB,MAAM;AACxC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,gBAAgB,MAAM;AACnC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,iBAAiB,MAAM;AACpC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,iBAAiB,WAAS;AACvC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,UAAU,UAAU,mBAAiB;AACxC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,MAAM,kBAAkB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,iBAAiB,oBAAoB;AACnC,UAAM,eAAe,CAAC;AACtB,eAAW,QAAQ,oBAAoB;AACrC,UAAI;AACJ,UAAI,OAAO,SAAS,UAAU;AAC5B,sBAAc;AAAA,UACZ,YAAY,IAAI,OAAO,MAAM,GAAG;AAAA,UAChC,aAAa,CAAC;AAAA,QAChB;AAAA,MACF,OAAO;AACL,sBAAc;AAAA,UACZ,YAAY,IAAI,OAAO,KAAK,KAAK,GAAG;AAAA,UACpC,aAAa,KAAK;AAAA,QACpB;AAAA,MACF;AACA,mBAAa,KAAK,WAAW;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,qBAAqB,CAAC;AAAA,IACtB,0BAA0B;AAAA,IAC1B,eAAe;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,iBAAiB,MAAM;AAAA,IACvB,oBAAoB,MAAM;AAAA,EAC5B,GAAG;AACD,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AACjC,SAAK,2BAA2B;AAChC,SAAK,gBAAgB,aAAa,KAAK,EAAE,OAAO,GAAG;AACnD,SAAK,gBAAgB,KAAK,iBAAiB,kBAAkB;AAC7D,SAAK,iBAAiB,cAAc,YAAY,SAAS,aAAa;AACtE,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACM,OAAmB;AAAA,+CAAd,UAAU,CAAC,GAAG;AACvB,WAAK,kBAAkB,OAAO;AAC9B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,YAAY,IAAI,SAAS,MAAM;AACpC,WAAK,oBAAoB;AACzB,YAAM,gBAAgB,MAAM,KAAK,UAAU,KAAK,WAAW;AAC3D,UAAI,iBAAiB,KAAK,2BAA2B;AACnD,cAAM,KAAK,gBAAgB;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACM,QAAoB;AAAA,+CAAd,UAAU,CAAC,GAAG;AACxB,YAAM,KAAK,UAAU,MAAM,OAAO;AAClC,UAAI,KAAK,2BAA2B;AAClC,cAAM,KAAK,gBAAgB;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA,EACM,OAAO,aAAa;AAAA;AACxB,YAAM,UAAU;AAAA,QACd;AAAA,MACF;AACA,YAAM,KAAK,UAAU,OAAO,OAAO;AACnC,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,EACM,WAEH;AAAA,+CAFY,UAAU;AAAA,MACvB,QAAQ;AAAA,IACV,GAAG;AACD,YAAM,KAAK,UAAU,SAAS,OAAO;AAAA,IACvC;AAAA;AAAA,EACA,aAAa,MAAM,UAAU;AAC3B,QAAI;AACJ,cAAU,KAAK,UAAU,gBAAgB,MAAM,QAAQ;AACvD,QAAI,CAAC,SAAS;AACZ,gBAAU,KAAK,UAAU,aAAa,IAAI;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,aAAa,MAAM,UAAU;AACxC,QAAI,QAAQ,CAAC;AACb,QAAI,KAAK,UAAU,gBAAgB;AACjC,aAAO,KAAK,KAAK,UAAU,cAAc,EAAE,QAAQ,SAAO;AACxD,YAAI,YAAY,aAAa,KAAK;AAChC;AAAA,QACF;AACA,cAAM,iBAAiB,KAAK,UAAU,eAAe,GAAG;AACxD,cAAM,cAAc,eAAe,OAAO,KAAK,CAAC;AAChD,gBAAQ,MAAM,OAAO,WAAW;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,cAAc,KAAK,UAAU,aAAa;AAC5C,YAAMC,cAAa,KAAK,UAAU,YAAY,OAAO,KAAK,CAAC;AAC3D,YAAM,KAAK,GAAGA,WAAU;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,eAAe,cAAc,GAAG;AAC9B,WAAO,KAAK,UAAU,eAAe,WAAW;AAAA,EAClD;AAAA,EACM,cAAmD;AAAA,+CAAvC,cAAc,KAAK,oBAAoB;AACvD,UAAI,KAAK,gBAAgB;AACvB,YAAI,KAAK,eAAe,GAAG;AACzB,gBAAM,IAAI,MAAM,wDAAwD;AAAA,QAC1E;AACA,eAAO;AAAA,MACT;AACA,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,IAAI,MAAM,8CAA8C;AAAA,MAChE;AACA,UAAI;AACF,eAAO,MAAM,KAAK,UAAU,YAAY,WAAW;AAAA,MACrD,SAAS,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA,EACM,gBAAgB,cAAc,OAAO;AAAA;AACzC,UAAI,KAAK,gBAAgB,CAAC,aAAa;AACrC,eAAO,KAAK;AAAA,MACd;AACA,UAAI,CAAC,KAAK,UAAU,eAAe;AACjC,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AACA,aAAO,KAAK,eAAe,MAAM,KAAK,UAAU,gBAAgB;AAAA,IAClE;AAAA;AAAA,EACM,WAAW;AAAA;AACf,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AACA,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,aAAa;AACX,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA,EACA,iBAAiB,UAAU,IAAI,YAAY,GAAG;AAC5C,WAAO,KAAK,KAAK,SAAS,CAAC,EAAE,KAAK,IAAI,WAAS,QAAQ,QAAQ,IAAI,KAAK,0BAA0B,KAAK,gBAAgB,KAAK,IAAI,OAAO,CAAC;AAAA,EAC1I;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,0BAA0B;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACM,yBAAyB,KAAK;AAAA;AAClC,UAAI,KAAK,SAAS,kBAAkB,GAAG,GAAG;AACxC,eAAO,MAAM,KAAK,SAAS,YAAY;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,WAAW,YAAY,WAAW,KAAK,YAAY,KAAK,EAAE,QAAQ,OAAO,YAAY,CAAC,IAAI;AAChG,UAAM,UAAU,WAAW,KAAK,GAAG;AACnC,WAAO,YAAY;AAAA,EACrB;AAAA,EACA,UAAU,KAAK,MAAM;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,CAAC,yBAAyB;AAC5B,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AACA,UAAM,YAAY,CAAC,KAAK,SAAS,eAAe,GAAG,KAAK,aAAa,UAAU,UAAQ,KAAK,cAAc,KAAK,IAAI,CAAC,IAAI;AACxH,QAAI,WAAW;AACb,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AACA,WAAO,cAAc,CAAC,KAAK,KAAK,yBAAyB,GAAG,CAAC,GAAG,GAAG,KAAK,SAAS,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,UAAU,MAAM,aAAa,KAAK,6BAA6B,KAAK,IAAI,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC;AAAA,EACnN;AAAA,EACA,6BAA6B,KAAK,MAAM;AACtC,WAAO,KAAK,SAAS,iBAAiB,IAAI,OAAO,EAAE,KAAK,SAAS,uBAAqB;AACpF,YAAM,QAAQ,IAAI,MAAM;AAAA,QACtB,SAAS;AAAA,MACX,CAAC;AACD,aAAO,KAAK,OAAO,KAAK;AAAA,IAC1B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA8B,SAAS,eAAe,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,GAAG;AACzC,aAAO,KAAK,KAAK,aAAY;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,iBAAiB;AAAA,QAC3B,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,MACD,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,iBAAiB;AAAA,QAC3B,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAAuB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["blocks", "exports", "promise", "i", "sha256", "config", "scripts", "KeycloakEventType", "realmRoles"]}