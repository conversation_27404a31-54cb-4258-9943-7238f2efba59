import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';
import { RUser } from '../model/RUser';

@Injectable({
  providedIn: 'root',
})
export class RUserService {
  private baseUrl: string = environment.apiUrl + '/api/shared/users';
  private interimUrl: string = environment.apiUrl + '/api/shared/interim';
  constructor(private http: HttpClient) {}

  // Get the list of all RUsers
  public getListRUsers(): Observable<RUser[]> {
    return this.http.get<RUser[]>(`${this.baseUrl}`);
  }

  // Get a user by its ID
  public getUserById(idUser: number): Observable<RUser> {
    return this.http.get<RUser>(`${this.baseUrl}/${idUser}`);
  }

  // Get a user by its kidUser
  public getUserByKId(kidUser: string): Observable<RUser> {
    return this.http.get<RUser>(`${this.baseUrl}/k/${kidUser}`);
  }

  // Add the connected user to the database
  public addKuserToDB(): Observable<RUser> {
    return this.http.post<RUser>(`${this.baseUrl}/add`, {});
  }

  refreshInterims(userId: number): Observable<void> {
    return this.http.put<void>(`${this.interimUrl}/refresh-interim/${userId}`, {});
  }
}
