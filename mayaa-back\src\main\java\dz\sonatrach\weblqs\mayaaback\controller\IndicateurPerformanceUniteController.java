package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.IndicateurPeformanceUnite;
import dz.sonatrach.weblqs.mayaaback.repo.IndicateurPeformanceUniteRepository;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Contrôleur pour l'accès aux données de la vue INDICATEURS_PERFORMANCE_COMPLEXE.
 *
 * Endpoint principal :
 *   GET /api/indicateur-performance/{pmois}/{unite}
 *
 * - pmois : période au format ddMMyyyy (ex: 01072025)
 * - unite : code de l'unité (String, 3 caractères)
 *
 * Réponse :
 *   200 OK : liste des objets IndicateurPeformanceUnite correspondant
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/")
public class IndicateurPerformanceUniteController {

    @Autowired
    private IndicateurPeformanceUniteRepository indicateurPeformanceUniteRepository;

    @GetMapping("/indicateur-performance/{pmois}/{unite}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<IndicateurPeformanceUnite>> getIndicateurPerformance(@PathVariable String pmois, @PathVariable String unite) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<IndicateurPeformanceUnite> indicateurs = indicateurPeformanceUniteRepository.findByMoisAndUnite(date, unite);
        if (indicateurs.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(indicateurs);
    }

    /**
     * Récupère tous les indicateurs de performance pour une unité donnée, triés par mois décroissant.
     * @param unite Code de l'unité
     * @return Liste des indicateurs de performance
     */
    @GetMapping("/indicateur-performance/unite/{unite}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<IndicateurPeformanceUnite>> getIndicateurPerformanceByUnite(@PathVariable String unite) {
        List<IndicateurPeformanceUnite> indicateurs = indicateurPeformanceUniteRepository.findByUniteOrderByMoisDesc(unite);
        if (indicateurs.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(indicateurs);
    }

    /**
     * Récupère le dernier indicateur de performance pour une unité donnée.
     * @param unite Code de l'unité
     * @return Le dernier indicateur de performance
     */
    @GetMapping("/indicateur-performance/unite/{unite}/latest")
    @JsonView(View.basic.class)
    public ResponseEntity<IndicateurPeformanceUnite> getLatestIndicateurPerformanceByUnite(@PathVariable String unite) {
        return indicateurPeformanceUniteRepository.findFirstByUniteOrderByMoisDesc(unite)
                .map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.noContent().build());
    }

    /**
     * Récupère tous les indicateurs de performance pour un mois donné, triés par unité.
     * @param pmois Période au format ddMMyyyy
     * @return Liste des indicateurs de performance
     */
    @GetMapping("/indicateur-performance/mois/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<IndicateurPeformanceUnite>> getIndicateurPerformanceByMois(@PathVariable String pmois) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<IndicateurPeformanceUnite> indicateurs = indicateurPeformanceUniteRepository.findByMoisOrderByUnite(date);
        if (indicateurs.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(indicateurs);
    }
}
